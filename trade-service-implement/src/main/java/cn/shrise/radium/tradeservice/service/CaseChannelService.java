package cn.shrise.radium.tradeservice.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.entity.ServiceIntroduction;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.SqlUtil;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.VipSubscription;
import cn.shrise.radium.orderservice.properties.vip.VipPackage;
import cn.shrise.radium.tradeservice.constant.StockCaseChannelOperateConstant;
import cn.shrise.radium.tradeservice.constant.StockChannelType;
import cn.shrise.radium.tradeservice.constant.TradeErrorCode;
import cn.shrise.radium.tradeservice.entity.*;
import cn.shrise.radium.tradeservice.repository.StockCaseChannelRepository;
import cn.shrise.radium.tradeservice.repository.TdStockCaseChannelRecordRepository;
import cn.shrise.radium.tradeservice.repository.TdStrategyChannelExtRepository;
import cn.shrise.radium.tradeservice.req.*;
import cn.shrise.radium.tradeservice.resp.StrategyChannelResp;
import cn.shrise.radium.tradeservice.resp.StrategyUserInfoResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcDepartment;
import cn.shrise.radium.userservice.entity.UcUsers;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.querydsl.jpa.impl.JPAUpdateClause;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.orderservice.constant.ServiceTypeConstant.POOL_CASE;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CaseChannelService {

    private final JPAQueryFactory queryFactory;
    private final JdbcTemplate jdbcTemplate;
    private final StockCaseChannelRepository caseChannelRepository;
    private final QTdStockCaseChannel qTdStockCaseChannel = QTdStockCaseChannel.tdStockCaseChannel;
    private final QTdCaseChannelAnalystRelation analystRelation = QTdCaseChannelAnalystRelation.tdCaseChannelAnalystRelation;
    private final QTdCaseChannelManagerRelation managerRelation = QTdCaseChannelManagerRelation.tdCaseChannelManagerRelation;
    private final TdStrategyChannelExtRepository tdStrategyChannelExtRepository;
    private final QTdStrategyChannelExt qTdStrategyChannelExt = QTdStrategyChannelExt.tdStrategyChannelExt;
    private final QTdCaseChannelDepartmentRelation departmentRelation = QTdCaseChannelDepartmentRelation.tdCaseChannelDepartmentRelation;
    private final OrderClient orderClient;
    private final UserClient userClient;
    private final ContentClient contentClient;
    private final TdStockCaseChannelRecordRepository stockCaseChannelRecordRepository;

    public PageResult<List<TdStockCaseChannel>> getStockCaseChannelList(List<Long> Ids, Integer channelType, Integer companyType, String searchContent, Integer current, Integer size) {
        JPAQuery<TdStockCaseChannel> query = queryFactory.select(qTdStockCaseChannel)
                .from(qTdStockCaseChannel)
                .where(qTdStockCaseChannel.companyType.eq(companyType))
                .where(qTdStockCaseChannel.isEnabled.eq(true));
        if (ObjectUtil.isNotEmpty(Ids)) {
            query.where(qTdStockCaseChannel.id.in(Ids));
        }

        if (ObjectUtil.isNotEmpty(channelType)) {
            query.where(qTdStockCaseChannel.channelType.eq(channelType));
        }

        if (ObjectUtil.isNotEmpty(searchContent)) {
            query.where(qTdStockCaseChannel.name.like("%" + searchContent + "%")
                    .or(qTdStockCaseChannel.remark.like("%" + searchContent + "%")));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qTdStockCaseChannel.id.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public List<TdStockCaseChannel> getStockCaseChannelList(Integer companyType, List<Long> channelIds, Integer channelType, Boolean enabled) {
        JPAQuery<TdStockCaseChannel> query = queryFactory.select(qTdStockCaseChannel)
                .from(qTdStockCaseChannel)
                .where(qTdStockCaseChannel.companyType.eq(companyType));
        if (ObjectUtil.isNotEmpty(channelIds)) {
            query.where(qTdStockCaseChannel.id.in(channelIds));
        }

        if (ObjectUtil.isNotEmpty(channelType)) {
            query.where(qTdStockCaseChannel.channelType.eq(channelType));
        }

        if (ObjectUtil.isNotEmpty(enabled)) {
            query.where(qTdStockCaseChannel.isEnabled.eq(enabled));
        }

        return query.orderBy(qTdStockCaseChannel.id.desc()).fetch();
    }

    public List<TdStockCaseChannel> getStockCaseChannelList(Integer companyType, List<Integer> channelTypes, Boolean enabled) {
        JPAQuery<TdStockCaseChannel> query = queryFactory.select(qTdStockCaseChannel)
                .from(qTdStockCaseChannel)
                .where(qTdStockCaseChannel.companyType.eq(companyType));

        if (ObjectUtil.isNotEmpty(channelTypes)) {
            query.where(qTdStockCaseChannel.channelType.in(channelTypes));
        }

        if (ObjectUtil.isNotEmpty(enabled)) {
            query.where(qTdStockCaseChannel.isEnabled.eq(enabled));
        }

        return query.orderBy(qTdStockCaseChannel.id.desc()).fetch();
    }

    public PageResult<List<TdStockCaseChannel>> getChatStockCaseChannelList(List<Long> ids, Integer channelType, Integer companyType, Integer current, Integer size) {
        JPAQuery<TdStockCaseChannel> query = queryFactory.select(qTdStockCaseChannel)
                .from(qTdStockCaseChannel)
                .where(qTdStockCaseChannel.companyType.eq(companyType))
                .where(qTdStockCaseChannel.isEnabled.eq(true));
        if (ObjectUtil.isNotEmpty(ids)) {
            query.where(qTdStockCaseChannel.id.notIn(ids));
        }

        if (ObjectUtil.isNotEmpty(channelType)) {
            query.where(qTdStockCaseChannel.channelType.eq(channelType));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qTdStockCaseChannel.gmtCreate.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    @Transactional
    public BaseResult<TradeErrorCode> createOrUpdateCaseChannel(EditStockCaseChannelReq req) {

        if (ObjectUtil.isEmpty(req.getChannelId())) {
            TdStockCaseChannel name = queryFactory.select(qTdStockCaseChannel).from(qTdStockCaseChannel)
                    .where(qTdStockCaseChannel.name.eq(req.getName())).fetchOne();
            if (ObjectUtil.isNotEmpty(name)) {
                return BaseResult.of(TradeErrorCode.CHANNEL_NAME_EXISTED);
            }
            TdStockCaseChannel number = queryFactory.select(qTdStockCaseChannel).from(qTdStockCaseChannel)
                    .where(qTdStockCaseChannel.number.eq(req.getNumber())).fetchOne();
            if (ObjectUtil.isNotEmpty(number)) {
                return BaseResult.of(TradeErrorCode.CHANNEL_NUMBER_EXISTED);
            }
            TdStockCaseChannel info = TdStockCaseChannel.builder()
                    .channelType(req.getChannelType())
                    .number(req.getNumber())
                    .name(req.getName())
                    .isEnabled(true)
                    .companyType(req.getCompanyType())
                    .remark(req.getRemark())
                    .needDecision(req.getNeedDecision())
                    .build();
            if (req.getChannelType().equals(StockChannelType.SCT_ROOM)) {
                if (ObjectUtil.isEmpty(req.getRoomId())) {
                    return BaseResult.of(TradeErrorCode.LIVE_ROOM_NOT_EMPTY);
                }
                info.setRoomId(req.getRoomId());
            } else if (req.getChannelType().equals(StockChannelType.SCT_SERIES)) {
                if (ObjectUtil.isEmpty(req.getSeriesId())) {
                    return BaseResult.of(TradeErrorCode.SERIES_NOT_EMPTY);
                }
                info.setSeriesId(req.getSeriesId());
            } else if (Arrays.asList(StockChannelType.SCT_ARTICLE_L1, StockChannelType.SCT_STOCK_L1).contains(req.getChannelType())) {
                info.setSeriesId(req.getSeriesId());
            } else if (req.getChannelType().equals(StockChannelType.SCT_CHATROOM)) {
                if (ObjectUtil.isEmpty(req.getChatId())) {
                    return BaseResult.of(TradeErrorCode.CHATROOM_NOT_EMPTY);
                }
                List<TdStockCaseChannel> fetch = queryFactory.select(qTdStockCaseChannel).from(qTdStockCaseChannel)
                        .where(qTdStockCaseChannel.chatId.eq(req.getChatId())).fetch();
                if (ObjectUtil.isNotEmpty(fetch)) {
                    return BaseResult.of(TradeErrorCode.CHATROOM_EXISTED);
                }
                info.setChatId(req.getChatId());
                if (ObjectUtil.isEmpty(req.getIsFollowHide())) {
                    return BaseResult.of(TradeErrorCode.CHAT_ROOM_IS_FOLLOW_HIDE_NOT_EMPTY);
                }
                info.setIsFollowHide(req.getIsFollowHide());
            }
            TdStockCaseChannel save = caseChannelRepository.save(info);
            log.info("创建案例频道成功,TdStockCaseChannel {}", info);
            if (req.getChannelType().equals(StockChannelType.SCT_STRATEGY)) {
                TdStrategyChannelExt build = TdStrategyChannelExt.builder()
                        .companyType(req.getCompanyType())
                        .number(req.getNumber())
                        .backName(req.getName())
                        .channelId(save.getId())
                        .enabled(true)
                        .isShowProfit(true)
                        .build();
                createOneStrategyChannelExt(build);
            }
            stockCaseChannelRecordRepository.save(TdStockCaseChannelRecord.builder()
                    .channelId(save.getId())
                    .operateId(req.getUserId())
                    .content(StockCaseChannelOperateConstant.CREATE)
                    .build());
        } else {
            TdStockCaseChannel fetchOne = queryFactory.select(qTdStockCaseChannel).from(qTdStockCaseChannel)
                    .where(qTdStockCaseChannel.name.eq(req.getName())).fetchOne();
            if (ObjectUtil.isNotEmpty(fetchOne) && !fetchOne.getId().equals(req.getChannelId())) {
                return BaseResult.of(TradeErrorCode.CHANNEL_NAME_EXISTED);
            }
            TdStockCaseChannel prevOne = queryFactory.select(qTdStockCaseChannel).from(qTdStockCaseChannel)
                    .where(qTdStockCaseChannel.id.eq(req.getChannelId())).fetchOne();
            queryFactory.update(qTdStockCaseChannel)
                    .where(qTdStockCaseChannel.id.eq(req.getChannelId()))
                    .set(qTdStockCaseChannel.name, req.getName())
                    .set(qTdStockCaseChannel.remark, req.getRemark())
                    .set(qTdStockCaseChannel.needDecision, req.getNeedDecision())
                    .execute();
            if (req.getChannelType().equals(StockChannelType.SCT_STRATEGY)) {
                queryFactory.update(qTdStrategyChannelExt)
                        .set(qTdStrategyChannelExt.backName, req.getName())
                        .where(qTdStrategyChannelExt.channelId.eq(req.getChannelId()))
                        .execute();
            }
            if (ObjectUtil.equals(req.getChannelType(), StockChannelType.SCT_CHATROOM)) {
                if (ObjectUtil.isNotEmpty(req.getIsFollowHide()) &&
                        !ObjectUtil.equals(prevOne.getIsFollowHide(), req.getIsFollowHide())) {
                    queryFactory.update(qTdStockCaseChannel)
                            .set(qTdStockCaseChannel.isFollowHide, req.getIsFollowHide())
                            .where(qTdStockCaseChannel.id.eq(req.getChannelId()))
                            .execute();
                    TdStockCaseChannelRecord record = TdStockCaseChannelRecord.builder()
                            .channelId(req.getChannelId())
                            .operateId(req.getUserId())
                            .content(String.format(StockCaseChannelOperateConstant.UPDATE_FOLLOW_HIDE,
                                    ObjectUtil.equals(req.getIsFollowHide(), true) ? "是" : "否"))
                            .build();
                    stockCaseChannelRecordRepository.save(record);
                }
            }
            if (!ObjectUtil.equals(prevOne.getName(), req.getName())) {
                stockCaseChannelRecordRepository.save(TdStockCaseChannelRecord.builder()
                        .channelId(req.getChannelId())
                        .operateId(req.getUserId())
                        .content(StockCaseChannelOperateConstant.UPDATE_NAME)
                        .build());
            }
            if (!ObjectUtil.equals(prevOne.getNeedDecision(), req.getNeedDecision())) {
                stockCaseChannelRecordRepository.save(TdStockCaseChannelRecord.builder()
                        .channelId(req.getChannelId())
                        .operateId(req.getUserId())
                        .content(String.format(StockCaseChannelOperateConstant.UPDATE_NEED_DECISION,
                                ObjectUtil.equals(req.getNeedDecision(), true) ? "是" : "否"))
                        .build());
            }

        }
        return BaseResult.success(TradeErrorCode.SUCCESS);
    }

    /**
     * 新增案例频道老师关系
     *
     * @param req
     */
    @Transactional
    public BaseResult<TradeErrorCode> addAnalyst(CaseChannelAnalystReq req) {

        final Long channelId = req.getChannelId();
        List<TdCaseChannelAnalystRelation> relations = req.getAnalystId().stream().map(analystId -> {
            return TdCaseChannelAnalystRelation.builder()
                    .analystId(analystId)
                    .channelId(channelId)
                    .build();
        }).collect(Collectors.toList());
        final String sql = SqlUtil.onDuplicateKeyUpdateSql(relations);
        jdbcTemplate.execute(sql);
        log.info("创建老师关系成功,TdCaseChannelAnalystRelations {}", relations);
        addAnalystRecord(req);
        return BaseResult.success(TradeErrorCode.SUCCESS);
    }

    /**
     * 新增案例频道老师记录
     **/
    @Transactional(rollbackFor = Exception.class)
    public BaseResult<TradeErrorCode> addAnalystRecord(CaseChannelAnalystReq req) {

        final Long channelId = req.getChannelId();
        final Integer operatorId = req.getOperatorId();
        List<TdCaseChannelAnalystRecord> records = req.getAnalystId().stream().map(analystId -> {
            return TdCaseChannelAnalystRecord.builder()
                    .analystId(analystId)
                    .channelId(channelId)
                    .operatorId(operatorId)
                    .isEnabled(true)
                    .build();
        }).collect(Collectors.toList());
        final String sql = SqlUtil.onDuplicateKeyUpdateSql(records);
        jdbcTemplate.execute(sql);
        log.info("添加老师关系记录成功,TdCaseChannelAnalystRecords {}", records);
        return BaseResult.success(TradeErrorCode.SUCCESS);
    }

    public BaseResult<List<TdCaseChannelAnalystRelation>> getCaseChannelAnalystRelations(Long channelId) {
        JPAQuery<TdCaseChannelAnalystRelation> query = queryFactory.select(analystRelation)
                .from(analystRelation)
                .where(analystRelation.channelId.eq(channelId));
        return BaseResult.success(query.fetch());
    }

    /**
     * 配置频道处理人
     *
     * @param req
     * @return
     */
    @Transactional
    public BaseResult<TradeErrorCode> addManager(CaseChannelManagerReq req) {

        final Long channelId = req.getChannelId();
        List<TdCaseChannelManagerRelation> relations = req.getManagerIds().stream().map(managerId -> {
            return TdCaseChannelManagerRelation.builder()
                    .channelId(channelId)
                    .managerId(managerId)
                    .build();
        }).collect(Collectors.toList());
        final String sql = SqlUtil.onDuplicateKeyUpdateSql(relations);
        jdbcTemplate.execute(sql);
        log.info("创建处理人关系成功,TdCaseChannelManagerRelations {}", relations);
        addManagerRecord(req);
        return BaseResult.success(TradeErrorCode.SUCCESS);
    }

    /**
     * 配置频道处理人
     *
     * @param req
     * @return
     */
    @Transactional
    public BaseResult<TradeErrorCode> setManager(CaseChannelManagerReq req) {
        setUser(req);
        setDepartment(req);
        return BaseResult.success(TradeErrorCode.SUCCESS);
    }

    @Transactional
    public void setUser(CaseChannelManagerReq req) {
        List<Integer> updateManagerIds = req.getManagerIds();
        List<TdCaseChannelManagerRelation> exitsManagerRelations = getCaseChannelManagerRelations(null, req.getChannelId()).getData();
        List<Long> discardManagerRelationIds = exitsManagerRelations.stream()
                .filter(managerRelation -> !updateManagerIds.contains(managerRelation.getManagerId()))
                .map(TdCaseChannelManagerRelation::getId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(discardManagerRelationIds)) {
            long execute = queryFactory.delete(managerRelation)
                    .where(managerRelation.id.in(discardManagerRelationIds)).execute();
            log.info("删除频道处理人关系成功,delete {}", execute);
        }
        if (ObjectUtil.isEmpty(updateManagerIds)) {
            return;
        }
        List<TdCaseChannelManagerRelation> relations = updateManagerIds.stream()
                .map(managerId -> TdCaseChannelManagerRelation.builder()
                        .channelId(req.getChannelId())
                        .managerId(managerId)
                        .build())
                .collect(Collectors.toList());
        final String sql = SqlUtil.onDuplicateKeyUpdateSql(relations);
        jdbcTemplate.execute(sql);
        log.info("创建处理人关系成功,TdCaseChannelManagerRelations {}", relations);
        addManagerRecord(req);
    }

    /**
     * 配置频道处理人为一个部门
     *
     * @param req
     * @return
     */
    @Transactional
    public void setDepartment(CaseChannelManagerReq req) {
        List<Integer> updateDeptIds = req.getDepartmentIds();
        List<TdCaseChannelDepartmentRelation> existDeptRelations = getCaseChannelDepartmentRelations(null, req.getChannelId());
        List<Long> discardDeptRelationIds = existDeptRelations.stream()
                .filter(deptRelation -> !updateDeptIds.contains(deptRelation.getDepartmentId()))
                .map(TdCaseChannelDepartmentRelation::getId)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(discardDeptRelationIds)) {
            long execute = queryFactory.delete(departmentRelation)
                    .where(departmentRelation.id.in(discardDeptRelationIds)).execute();
            log.info("删除频道部门关系成功,delete {}", execute);
        }
        if (ObjectUtil.isEmpty(updateDeptIds)) {
            return;
        }
        List<TdCaseChannelDepartmentRelation> relations = updateDeptIds.stream()
                .map(deptId -> TdCaseChannelDepartmentRelation.builder()
                        .channelId(req.getChannelId())
                        .departmentId(deptId)
                        .build())
                .collect(Collectors.toList());
        final String sql = SqlUtil.onDuplicateKeyUpdateSql(relations);
        jdbcTemplate.execute(sql);
        log.info("创建部门关系成功,TdCaseChannelDepartmentRelations {}", relations);
        addDepartmentRecord(req);
    }

    /**
     * 新增案例频道处理人记录
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResult<TradeErrorCode> addManagerRecord(CaseChannelManagerReq req) {
        if(CollUtil.isEmpty(req.getManagerIds())){
            return BaseResult.success(TradeErrorCode.SUCCESS);
        }
        final Long channelId = req.getChannelId();
        final Integer operatorId = req.getOperatorId();
        List<TdCaseChannelManagerRecord> records = req.getManagerIds().stream().map(managerId -> {
            return TdCaseChannelManagerRecord.builder()
                    .channelId(channelId)
                    .managerId(managerId)
                    .operatorId(operatorId)
                    .isEnabled(true)
                    .build();
        }).collect(Collectors.toList());
        final String sql = SqlUtil.onDuplicateKeyUpdateSql(records);
        jdbcTemplate.execute(sql);
        log.info("添加处理人关系记录成功,TdCaseChannelManagerRecords {}", records);
        return BaseResult.success(TradeErrorCode.SUCCESS);
    }
    /**
     * 新增案例频道部门记录
     *
     * @param req
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResult<TradeErrorCode> addDepartmentRecord(CaseChannelManagerReq req) {
        if (CollUtil.isEmpty(req.getDepartmentIds())) {
            return BaseResult.success(TradeErrorCode.SUCCESS);
        }
        final Long channelId = req.getChannelId();
        final Integer operatorId = req.getOperatorId();
        List<TdCaseChannelManagerRecord> records = req.getDepartmentIds().stream().map(departmentId -> {
            return TdCaseChannelManagerRecord.builder()
                    .channelId(channelId)
                    .departmentId(departmentId)
                    .operatorId(operatorId)
                    .isEnabled(true)
                    .build();
        }).collect(Collectors.toList());
        final String sql = SqlUtil.onDuplicateKeyUpdateSql(records);
        jdbcTemplate.execute(sql);
        log.info("添加处理人关系记录成功,TdCaseChannelManagerRecords {}", records);
        return BaseResult.success(TradeErrorCode.SUCCESS);
    }

    @Transactional
    public BaseResult<TradeErrorCode> deleteAnalystRelation(List<Long> ids) {

        long execute = queryFactory.delete(analystRelation)
                .where(analystRelation.id.in(ids)).execute();
        log.info("删除投顾老师关系成功,delete {}", execute);
        return BaseResult.success(TradeErrorCode.SUCCESS);
    }

    @Transactional
    public BaseResult<TradeErrorCode> deleteManagerRelation(List<Long> ids) {
        long execute = queryFactory.delete(managerRelation)
                .where(managerRelation.id.in(ids)).execute();
        log.info("删除频道处理人关系成功,delete {}", execute);
        return BaseResult.success(TradeErrorCode.SUCCESS);
    }

    public BaseResult<List<TdCaseChannelManagerRelation>> getCaseChannelManagerRelations(Integer managerId, Long channelId) {
        JPAQuery<TdCaseChannelManagerRelation> query = queryFactory.select(managerRelation)
                .from(managerRelation);
        if (ObjectUtil.isNotEmpty(managerId)) {
            query.where(managerRelation.managerId.eq(managerId));
        }
        if (ObjectUtil.isNotEmpty(channelId)) {
            query.where(managerRelation.channelId.eq(channelId));
        }
        return BaseResult.success(query.fetch());
    }

    public List<TdCaseChannelDepartmentRelation> getChannelDepartmentRelation(Integer departmentId, Long channelId) {
        JPAQuery<TdCaseChannelDepartmentRelation> query = queryFactory.selectFrom(departmentRelation)
                .from(departmentRelation);
        if (ObjectUtil.isNotEmpty(departmentId)) {
            query.where(departmentRelation.departmentId.eq(departmentId));
        }
        if (ObjectUtil.isNotEmpty(channelId)) {
            query.where(departmentRelation.channelId.eq(channelId));
        }
        return query.fetch();
    }


    public List<TdCaseChannelDepartmentRelation> getCaseChannelDepartmentRelations(List<Integer> deptIds, Long channelId) {
        JPAQuery<TdCaseChannelDepartmentRelation> query = queryFactory.select(departmentRelation)
                .from(departmentRelation);
        if (CollUtil.isNotEmpty(deptIds)) {
            query.where(departmentRelation.departmentId.in(deptIds));
        }
        if (ObjectUtil.isNotEmpty(channelId)) {
            query.where(departmentRelation.channelId.eq(channelId));
        }
        return query.fetch();
    }

    public Set<Long> getProcessableCaseChannelIds(Integer userId) {
        if (ObjectUtil.isNull(userId)) {
            throw new BusinessException("userId 不能为空");
        }
        // 1. 根据userId 获得可处理的渠道
        List<TdCaseChannelManagerRelation> channelManagerRelations = getCaseChannelManagerRelations(userId, null).getData();
        Set<Long> channelIdByManagerRelation = channelManagerRelations.stream()
                .map(TdCaseChannelManagerRelation::getChannelId).collect(Collectors.toSet());
        // 2. 根据userId 所属的部门 获得可处理的渠道
        //     获得用户所属的各级部门
        BaseResult<List<UcDepartment>> departmentFullPathRes = userClient.getDepartmentFullPath(userId, null);
        if (departmentFullPathRes.isFail()) {
            throw new BusinessException(departmentFullPathRes);
        }
        List<Integer> userDepartmentIds = departmentFullPathRes.orElse(new ArrayList<>())
                .stream().map(UcDepartment::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(userDepartmentIds)) {
            return channelIdByManagerRelation;
        }
        //    获得用户所属的各级部门可以处理的渠道
        List<TdCaseChannelDepartmentRelation> channelDepartmentRelations = getCaseChannelDepartmentRelations(userDepartmentIds, null);
        Set<Long> channelIdByDepartmentRelation = channelDepartmentRelations.stream()
                .map(TdCaseChannelDepartmentRelation::getChannelId).collect(Collectors.toSet());
        return Sets.union(channelIdByManagerRelation, channelIdByDepartmentRelation);
    }

    public Optional<TdStockCaseChannel> findOneByFilter(Long id, String number, Long chatId) {
        if (ObjectUtil.isAllEmpty(id, number, chatId)) {
            throw new BusinessException(BaseResult.of(TradeErrorCode.PARAM_INVALID));
        }
        JPAQuery<TdStockCaseChannel> query = queryFactory.selectFrom(qTdStockCaseChannel);
        if (ObjectUtil.isNotEmpty(id)) {
            query.where(qTdStockCaseChannel.id.eq(id));
        }

        if (ObjectUtil.isNotEmpty(number)) {
            query.where(qTdStockCaseChannel.number.eq(number));
        }

        if (ObjectUtil.isNotEmpty(chatId)) {
            query.where(qTdStockCaseChannel.chatId.eq(chatId));
        }
        return Optional.ofNullable(query.fetchFirst());
    }

    public List<TdStockCaseChannel> findBySeriesId(Integer seriesId) {
        return queryFactory.selectFrom(qTdStockCaseChannel).where(qTdStockCaseChannel.seriesId.eq(seriesId)).fetch();
    }

    public List<TdStockCaseChannel> getStrategyListByType(Integer companyType, Integer channelType, Collection<String> numbers) {
        if (ObjectUtils.isEmpty(numbers)) {
            return Collections.emptyList();
        }
        return queryFactory.select(qTdStockCaseChannel)
                .from(qTdStockCaseChannel)
                .where(qTdStockCaseChannel.channelType.eq(channelType))
                .where(qTdStockCaseChannel.companyType.eq(companyType))
                .where(qTdStockCaseChannel.number.in(numbers))
                .fetch();
    }

    public List<ServiceIntroduction> getPoolCaseServiceIntroductionList(Integer companyType, Collection<String> numbers) {
        if (ObjectUtils.isEmpty(numbers)) {
            return Collections.emptyList();
        }
        return queryFactory.select(Projections.bean(ServiceIntroduction.class,
                        qTdStockCaseChannel.id.as("serviceId"),
                        qTdStockCaseChannel.name.as("serviceName"),
                        qTdStockCaseChannel.name.as("alias"),
                        qTdStockCaseChannel.name.as("name"),
                        qTdStockCaseChannel.number.as("number"),
                        Expressions.asString(POOL_CASE).as("type")
                ))
                .from(qTdStockCaseChannel)
                .where(qTdStockCaseChannel.companyType.eq(companyType))
                .where(qTdStockCaseChannel.number.in(numbers))
                .fetch();
    }

    public void createOneStrategyChannelExt(TdStrategyChannelExt strategyChannelExt) {
        tdStrategyChannelExtRepository.save(strategyChannelExt);
    }

    public PageResult<List<StrategyChannelResp>> getStrategyChannelList(Integer companyType, Integer userId, String searchContent, Integer current, Integer size) {
        List<Long> channelIds = Lists.newArrayList(getProcessableCaseChannelIds(userId));

        JPAQuery<TdStrategyChannelExt> query = queryFactory.select(qTdStrategyChannelExt)
                .from(qTdStrategyChannelExt)
                .leftJoin(qTdStockCaseChannel)
                .on(qTdStrategyChannelExt.channelId.eq(qTdStockCaseChannel.id))
                .where(qTdStrategyChannelExt.companyType.eq(companyType))
                .where(qTdStrategyChannelExt.channelId.in(channelIds))
                .where(qTdStockCaseChannel.isEnabled.eq(true));
        if (ObjectUtil.isNotEmpty(searchContent)) {
            query.where(qTdStrategyChannelExt.number.like("%" + searchContent + "%")
                    .or(qTdStrategyChannelExt.backName.like("%" + searchContent + "%"))
                    .or(qTdStrategyChannelExt.showName.like("%" + searchContent + "%")));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qTdStrategyChannelExt.id.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }

        List<StrategyChannelResp> strategyChannelResps = new ArrayList<>();
        List<TdStrategyChannelExt> fetch = query.fetch();
        List<TdStockCaseChannel> stockCaseChannelList = getStockCaseChannelList(companyType, channelIds, StockChannelType.SCT_STRATEGY, null);
        Map<Long, TdStockCaseChannel> caseChannelMap = stockCaseChannelList.stream().collect(Collectors.toMap(TdStockCaseChannel::getId, Function.identity()));
        for (TdStrategyChannelExt tdStrategyChannelExt : fetch) {
            StrategyChannelResp strategyChannelResp = new StrategyChannelResp();
            BeanUtil.copyProperties(tdStrategyChannelExt, strategyChannelResp);
            strategyChannelResp.setTags(JSON.parseArray(tdStrategyChannelExt.getTags()));
            strategyChannelResp.setChannelName(caseChannelMap.getOrDefault(tdStrategyChannelExt.getChannelId(), new TdStockCaseChannel()).getName());
            strategyChannelResps.add(strategyChannelResp);
        }
        return PageResult.success(strategyChannelResps, Pagination.of(current, size, total));
    }

    public BaseResult<StrategyChannelResp> getStrategyInfo(Long id) {
        TdStrategyChannelExt tdStrategyChannelExt = tdStrategyChannelExtRepository.findById(id).orElseThrow(RecordNotExistedException::new);
        StrategyChannelResp strategyChannelResp = new StrategyChannelResp();
        BeanUtil.copyProperties(tdStrategyChannelExt, strategyChannelResp);
        strategyChannelResp.setTags(JSON.parseArray(tdStrategyChannelExt.getTags()));
        if (ObjectUtil.isNotEmpty(tdStrategyChannelExt.getAnalystId())) {
            SsAnalystInfo analystInfo = contentClient.getAnalystInfo(tdStrategyChannelExt.getAnalystId()).getData();
            strategyChannelResp.setAnalystInfo(analystInfo);
        }
        return BaseResult.success(strategyChannelResp);
    }

    public BaseResult<StrategyChannelResp> getStrategyByChannelId(Long channelId) {
        TdStrategyChannelExt tdStrategyChannelExt = tdStrategyChannelExtRepository.findByChannelId(channelId);
        StrategyChannelResp strategyChannelResp = new StrategyChannelResp();
        if (ObjectUtil.isNotEmpty(tdStrategyChannelExt)) {
            BeanUtil.copyProperties(tdStrategyChannelExt, strategyChannelResp);
            strategyChannelResp.setTags(JSON.parseArray(tdStrategyChannelExt.getTags()));
            if (ObjectUtil.isNotNull(tdStrategyChannelExt.getAnalystId())) {
                SsAnalystInfo analystInfo = contentClient.getAnalystInfo(tdStrategyChannelExt.getAnalystId()).getData();
                strategyChannelResp.setAnalystInfo(analystInfo);
            }
        }
        return BaseResult.success(strategyChannelResp);
    }

    @Transactional
    public void updateStrategyInfo(UpdateStrategyInfoReq req) {
        Long id = req.getId();
        JPAUpdateClause clause = queryFactory.update(qTdStrategyChannelExt)
                .where(qTdStrategyChannelExt.id.eq(id));
        if (ObjectUtil.isNotEmpty(req.getShowName())) {
            clause.set(qTdStrategyChannelExt.showName, req.getShowName());
        }
        if (ObjectUtil.isNotEmpty(req.getIsShowProfit())) {
            clause.set(qTdStrategyChannelExt.isShowProfit, req.getIsShowProfit());
        }
        if (ObjectUtil.isNotEmpty(req.getTags())) {
            clause.set(qTdStrategyChannelExt.tags, JSON.toJSONString(req.getTags()));
        } else {
            clause.setNull(qTdStrategyChannelExt.tags);
        }
        if (ObjectUtil.isNotEmpty(req.getDescription())) {
            clause.set(qTdStrategyChannelExt.description, req.getDescription());
        }
        if (ObjectUtil.isNotEmpty(req.getAnalystId())) {
            clause.set(qTdStrategyChannelExt.analystId, req.getAnalystId());
        }
        clause.execute();
    }

    public PageResult<List<StrategyUserInfoResp>> getStrategyUserInfoList(Long id, Integer searchContent, Integer current, Integer size) {

        TdStrategyChannelExt tdStrategyChannelExt = tdStrategyChannelExtRepository.findById(id).orElseThrow(RecordNotExistedException::new);
        Integer companyType = tdStrategyChannelExt.getCompanyType();
        String number = tdStrategyChannelExt.getNumber();
        List<String> vipNumbers = orderClient.getPoolCaseVipPackageNumber(companyType, number).orElseThrow();

        if (ObjectUtil.isEmpty(vipNumbers)) {
            return PageResult.empty(current, size);
        }
        PageResult<List<VipSubscription>> pageResult = orderClient.pageFilterVipSubscription(ObjectUtil.isEmpty(searchContent) ? null : searchContent, vipNumbers, false, current, size);
        List<VipSubscription> data = pageResult.getData();
        List<Integer> userIds = data.stream().map(VipSubscription::getUserId).collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIds)).getData();

        List<StrategyUserInfoResp> strategyUserInfoResps = new ArrayList<>();
        List<VipPackage> vipPackageList = orderClient.getVipPackageByNumbers(companyType, vipNumbers).getData();
        Map<String, VipPackage> packageMap = vipPackageList.stream().collect(Collectors.toMap(VipPackage::getNumber, Function.identity()));
        for (VipSubscription vipSubscription : data) {
            Integer userId = vipSubscription.getUserId();
            StrategyUserInfoResp build = StrategyUserInfoResp.builder()
                    .userId(userId)
                    .nickName(usersMap.getOrDefault(userId, new UcUsers()).getNickName())
                    .name(packageMap.getOrDefault(vipSubscription.getNumber(), new VipPackage()).getName())
                    .openTime(vipSubscription.getOpenTime())
                    .expireTime(vipSubscription.getExpireTime())
                    .build();
            strategyUserInfoResps.add(build);
        }
        return PageResult.success(strategyUserInfoResps, pageResult.getPagination());
    }

    public List<TdStrategyChannelExt> getStrategyChannelListByEnabled(Integer companyType, Boolean enabled) {
        return queryFactory.selectFrom(qTdStrategyChannelExt)
                .where(qTdStrategyChannelExt.companyType.eq(companyType))
                .where(qTdStrategyChannelExt.enabled.eq(enabled))
                .fetch();
    }

    @Transactional
    public void setFollowHide(UpdateCaseChannelHideReq req) {
        queryFactory.update(qTdStockCaseChannel)
                .where(qTdStockCaseChannel.id.eq(req.getId()))
                .set(qTdStockCaseChannel.isFollowHide, req.getIsFollowHide())
                .execute();
        TdStockCaseChannelRecord record = TdStockCaseChannelRecord.builder()
                .channelId(req.getId())
                .operateId(req.getOperatorId())
                .content(StockCaseChannelOperateConstant.UPDATE_FOLLOW_HIDE)
                .build();
        stockCaseChannelRecordRepository.save(record);
    }
}
