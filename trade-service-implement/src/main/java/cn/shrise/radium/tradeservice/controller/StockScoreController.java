package cn.shrise.radium.tradeservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.tradeservice.entity.TdStockFactorRelation;
import cn.shrise.radium.tradeservice.entity.TdStockScoreInfo;
import cn.shrise.radium.tradeservice.req.StockFactorMapReq;
import cn.shrise.radium.tradeservice.req.StockFactorReq;
import cn.shrise.radium.tradeservice.req.StockScoreReq;
import cn.shrise.radium.tradeservice.resp.StockFactorResp;
import cn.shrise.radium.tradeservice.resp.StockScoreResp;
import cn.shrise.radium.tradeservice.service.StockScoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;

/**
 * @Author: tangjiajun
 * @Date: 2025/4/2 17:11
 * @Desc:
 **/
@Api
@RestController
@RequestMapping("stock-score")
@RequiredArgsConstructor
public class StockScoreController {

    private final StockScoreService stockScoreService;

    @PostMapping("stock-list")
    @ApiOperation("竞价算力股票列表")
    public PageResult<List<TdStockScoreInfo>> stockScoreList(@RequestBody @Valid StockScoreReq stockScoreReq) {
        return stockScoreService.stockScoreList(stockScoreReq.getDate(), stockScoreReq.getOrderType(), stockScoreReq.getFactorList(),
                stockScoreReq.getIsAsc(), stockScoreReq.getIsFilterSt(), stockScoreReq.getIsFilterLimitUp(), stockScoreReq.getCurrent(), stockScoreReq.getSize());
    }

    @PostMapping("stock-test")
    @ApiOperation("竞价算力股票列表")
    public PageResult<List<StockScoreResp>> stockScoreTestList(@RequestBody @Valid StockScoreReq stockScoreReq) {
        return stockScoreService.stockScoreTestList(stockScoreReq.getDate(), stockScoreReq.getOrderType(), stockScoreReq.getFactorList(),
                stockScoreReq.getIsAsc(), stockScoreReq.getIsFilterSt(), stockScoreReq.getIsFilterLimitUp(), stockScoreReq.getCurrent(), stockScoreReq.getSize());
    }

    @PostMapping("factor-map")
    @ApiOperation("获取因子信息map")
    public BaseResult<Map<String, List<TdStockFactorRelation>>> getStockFactorMapByFactorIds(@RequestBody StockFactorMapReq req) {
        List<String> stockCodes = req.getStockCodes();
        LocalDate date = req.getDate();
        Map<String, List<TdStockFactorRelation>> factorMap = stockScoreService.getStockFactorMapByFactorIds(stockCodes, date);
        return BaseResult.success(factorMap);
    }


    @GetMapping("factor/list")
    @ApiOperation("获取因子列表")
    public PageResult<List<StockFactorResp>> factorList(
            @RequestParam(required = false) @ApiParam("启用/禁用") Boolean enabled,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size
    ) {
        return stockScoreService.factorList(enabled, current, size);
    }

    @PostMapping("factor/add")
    @ApiOperation("创建因子")
    public BaseResult<Void> addFactor(@RequestBody @Valid StockFactorReq stockFactorReq) {
        return stockScoreService.addFactor(stockFactorReq);
    }

    @PostMapping("factor/enable")
    @ApiOperation("更新启用状态")
    public BaseResult<Void> factorEnabled(@RequestParam Long id, @RequestParam @ApiParam("启用/禁用") Boolean enabled) {
        return stockScoreService.factorEnabled(id, enabled);
    }
}
