package cn.shrise.radium.tradeservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.entity.ServiceIntroduction;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.tradeservice.constant.TradeErrorCode;
import cn.shrise.radium.tradeservice.entity.*;
import cn.shrise.radium.tradeservice.req.*;
import cn.shrise.radium.tradeservice.resp.StrategyChannelResp;
import cn.shrise.radium.tradeservice.resp.StrategyUserInfoResp;
import cn.shrise.radium.tradeservice.service.CaseChannelService;
import cn.shrise.radium.tradeservice.service.StockCaseChannelRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Set;

import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

/**
 * <AUTHOR>
 */
@Api
@RestController
@RequestMapping("case_channel")
@RequiredArgsConstructor
public class CaseChannelController {

    private final CaseChannelService caseChannelService;
    private final StockCaseChannelRecordService stockCaseChannelRecordService;

    @GetMapping("list")
    @ApiOperation("获取案例频道列表")
    public PageResult<List<TdStockCaseChannel>> getStockCaseChannelList(
            @RequestParam(required = false) @ApiParam("频道Id列表") List<Long> Ids,
            @RequestParam(required = false) @ApiParam("频道类型") Integer channelType,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("搜索") String searchContent,
            @RequestParam @ApiParam("页码") Integer current,
            @RequestParam @ApiParam("分页数量") Integer size) {
        return caseChannelService.getStockCaseChannelList(Ids, channelType, companyType, searchContent, current, size);
    }

    @GetMapping("list/all")
    @ApiOperation("获取全部案例频道列表")
    public BaseResult<List<TdStockCaseChannel>> getStockCaseChannelList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("频道Id列表") List<Long> channelIds,
            @RequestParam(required = false) @ApiParam("频道类型") Integer channelType,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enabled) {

        List<TdStockCaseChannel> stockCaseChannelList = caseChannelService.getStockCaseChannelList(companyType, channelIds, channelType, enabled);
        return BaseResult.success(stockCaseChannelList);
    }

    @GetMapping("all-filter")
    @ApiOperation("获取全部案例频道列表")
    public BaseResult<List<TdStockCaseChannel>> getAllCaseChannelList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("频道类型") List<Integer> channelTypes,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enabled) {
        List<TdStockCaseChannel> stockCaseChannelList = caseChannelService.getStockCaseChannelList(companyType, channelTypes, enabled);
        return BaseResult.success(stockCaseChannelList);
    }

    @GetMapping("chat/list")
    @ApiOperation("获取聊天室案例频道列表")
    public PageResult<List<TdStockCaseChannel>> getChatStockCaseChannelList(
            @RequestParam(required = false) @ApiParam("频道Id列表") List<Long> ids,
            @RequestParam(required = false) @ApiParam("频道类型") Integer channelType,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("页码") Integer current,
            @RequestParam @ApiParam("分页数量") Integer size) {
        return caseChannelService.getChatStockCaseChannelList(ids, channelType, companyType, current, size);
    }

    @PostMapping("createOrUpdate")
    @ApiOperation("创建/编辑案例频道")
    public BaseResult<TradeErrorCode> createOrUpdateCaseChannel(@Validated @RequestBody EditStockCaseChannelReq req) {
        return caseChannelService.createOrUpdateCaseChannel(req);
    }

    @PostMapping("addAnalyst")
    @ApiOperation("为频道添加投顾老师")
    public BaseResult<TradeErrorCode> addAnalyst(@Validated @RequestBody CaseChannelAnalystReq req) {
        return caseChannelService.addAnalyst(req);
    }

    @PostMapping("deleteAnalystRelation")
    @ApiOperation("删除投顾老师关系")
    public BaseResult<TradeErrorCode> deleteAnalystRelation(
            @RequestParam @ApiParam("需要删除的关系id") List<Long> Ids) {
        return caseChannelService.deleteAnalystRelation(Ids);
    }

    @GetMapping("getChannelAnalystRelation")
    @ApiOperation("获取案例频道对映老师关系")
    public BaseResult<List<TdCaseChannelAnalystRelation>> getCaseChannelAnalystRelations(
            @RequestParam @ApiParam("频道ID") Long channelId) {
        return caseChannelService.getCaseChannelAnalystRelations(channelId);
    }

    @PostMapping("addManager")
    @ApiOperation("为频道配置处理人")
    public BaseResult<TradeErrorCode> addManager(@Validated @RequestBody CaseChannelManagerReq req) {
        return caseChannelService.addManager(req);
    }

    @PostMapping("setManager")
    @ApiOperation("为频道配置处理人")
    BaseResult<TradeErrorCode> setManager(@Validated @RequestBody CaseChannelManagerReq req) {
        return caseChannelService.setManager(req);
    }

    @PostMapping("getProcessableCaseChannelIds")
    @ApiOperation("获取用户可处理的案例频道id")
    BaseResult<Set<Long>> getProcessableCaseChannelIds(@RequestParam @ApiParam("用户id") Integer userId) {
        return BaseResult.success(caseChannelService.getProcessableCaseChannelIds(userId));
    }

    @GetMapping("getChannelManagerRelation")
    @ApiOperation("获取案例频道对应处理人关系")
    public BaseResult<List<TdCaseChannelManagerRelation>> getCaseChannelManagerRelations(
            @RequestParam(required = false) @ApiParam("处理人Id") Integer managerId,
            @RequestParam(required = false) @ApiParam("频道ID") Long channelId) {
        return caseChannelService.getCaseChannelManagerRelations(managerId, channelId);
    }

    @GetMapping("getChannelDepartmentRelation")
    @ApiOperation("获取案例频道对应部门关系")
    BaseResult<List<TdCaseChannelDepartmentRelation>> getChannelDepartmentRelation(
            @RequestParam(required = false) @ApiParam("部门Id") Integer departmentId,
            @RequestParam(required = false) @ApiParam("频道ID") Long channelId) {
        return BaseResult.success(caseChannelService.getChannelDepartmentRelation(departmentId, channelId));
    }

    @PostMapping("deleteManagerRelation")
    @ApiOperation("删除频道处理人关系")
    public BaseResult<TradeErrorCode> deleteManagerRelation(
            @RequestParam @ApiParam("需要删除的关系id") List<Long> Ids) {
        return caseChannelService.deleteManagerRelation(Ids);
    }

    @GetMapping("filter")
    @ApiOperation("获取案例频道")
    public BaseResult<TdStockCaseChannel> getStockCaseChannelByFilter(
            @RequestParam(required = false) @ApiParam("频道Id") Long id,
            @RequestParam(required = false) @ApiParam("频道编号") String number,
            @RequestParam(required = false) @ApiParam("聊天室Id") Long chatId) {
        return BaseResult.success(caseChannelService.findOneByFilter(id, number, chatId).orElse(null));
    }

    @GetMapping("by-seriesId")
    @ApiOperation("获取案例频道")
    public BaseResult<List<TdStockCaseChannel>> getStockCaseChannelBySeriesId(
            @RequestParam @ApiParam("系列id") Integer seriesId) {
        return BaseResult.success(caseChannelService.findBySeriesId(seriesId));
    }

    @PostMapping({"service/introduction/number"})
    @ApiOperation("获取股池服务简介列表")
    BaseResult<List<ServiceIntroduction>> getPoolCaseServiceIntroductionList(
            @RequestParam Integer companyType,
            @RequestBody @Valid BatchReq<String> req) {
        List<ServiceIntroduction> indexServiceIntroductionList = caseChannelService.getPoolCaseServiceIntroductionList(companyType, req.getValues());
        return BaseResult.success(indexServiceIntroductionList);
    }

    @PostMapping({"stock-channel-list"})
    @ApiOperation("批量获取股池频道列表")
    BaseResult<List<TdStockCaseChannel>> getStrategyListByType(
            @RequestParam Integer companyType,
            @RequestParam Integer channelType,
            @RequestBody @Valid BatchReq<String> req) {
        List<TdStockCaseChannel> resultList = caseChannelService.getStrategyListByType(companyType, channelType, req.getValues());
        return BaseResult.success(resultList);
    }

    @GetMapping("strategyList")
    @ApiOperation("策略组列表")
    public PageResult<List<StrategyChannelResp>> getStrategyChannelList(
            @RequestParam Integer companyType,
            @RequestParam Integer userId,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return caseChannelService.getStrategyChannelList(companyType, userId, searchContent, current, size);
    }

    @GetMapping("strategyList/by/enabled")
    @ApiOperation("根据状态获取策略组列表")
    public List<TdStrategyChannelExt> getStrategyChannelListByEnabled(
            @RequestParam Integer companyType,
            @RequestParam @ApiParam("状态") Boolean enabled) {
        return caseChannelService.getStrategyChannelListByEnabled(companyType, enabled);
    }

    @GetMapping("strategy/info")
    @ApiOperation("策略组详情")
    public BaseResult<StrategyChannelResp> getStrategyInfo(@RequestParam @ApiParam("策略组ID") Long id) {
        return caseChannelService.getStrategyInfo(id);
    }

    @PostMapping("strategy/update")
    @ApiOperation("策略组信息编辑")
    public BaseResult<Void> updateStrategyInfo(@Validated @RequestBody UpdateStrategyInfoReq req) {
        caseChannelService.updateStrategyInfo(req);
        return BaseResult.successful();
    }

    @GetMapping("strategy/userList")
    @ApiOperation("策略组用户列表")
    public PageResult<List<StrategyUserInfoResp>> getStrategyUserInfoList(
            @RequestParam @ApiParam("策略组ID") Long id,
            @RequestParam(required = false) @ApiParam("搜索内容") Integer searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return caseChannelService.getStrategyUserInfoList(id, searchContent, current, size);
    }

    @GetMapping("strategy/channelId")
    @ApiOperation("根据频道id获取策略组详情")
    public BaseResult<StrategyChannelResp> getStrategyByChannelId(@RequestParam @ApiParam("策略组频道ID") Long channelId) {
        return caseChannelService.getStrategyByChannelId(channelId);
    }

    @GetMapping("record/list")
    @ApiOperation("获取案例频道操作记录列表")
    public PageResult<List<TdStockCaseChannelRecord>> getStockCaseChannelRecordList(
            @RequestParam @ApiParam("频道id") Long channelId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "10") @ApiParam("每页条数") Integer size) {
        return stockCaseChannelRecordService.getStockCaseChannelRecordList(channelId, current, size);
    }

    @PostMapping("set-follow-hide")
    @ApiOperation("设置股票池频道解盘股票跟踪前端是否隐藏")
    public BaseResult<Void> setFollowHide(@Validated @RequestBody UpdateCaseChannelHideReq req) {
        caseChannelService.setFollowHide(req);
        return BaseResult.successful();
    }
}
