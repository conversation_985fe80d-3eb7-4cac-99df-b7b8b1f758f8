package cn.shrise.radium.tradeservice.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.RecordExistedException;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.tradeservice.entity.*;
import cn.shrise.radium.tradeservice.repository.StockFactorRepository;
import cn.shrise.radium.tradeservice.req.StockFactorReq;
import cn.shrise.radium.tradeservice.resp.StockFactorInfoResp;
import cn.shrise.radium.tradeservice.resp.StockFactorResp;
import cn.shrise.radium.tradeservice.resp.StockScoreResp;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: tangjiajun
 * @Date: 2025/4/3 9:27
 * @Desc:
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class StockScoreService {

    private final JPAQueryFactory queryFactory;
    private final QTdStockScoreInfo stockScoreInfo = QTdStockScoreInfo.tdStockScoreInfo;
    private final QTdStockFactor stockFactor = QTdStockFactor.tdStockFactor;
    private final QTdStockFactorRelation stockFactorRelation = QTdStockFactorRelation.tdStockFactorRelation;
    private final StockFactorRepository stockFactorRepository;

    public PageResult<List<TdStockScoreInfo>> stockScoreList(LocalDate date, Integer orderType, List<Long> factorList, Boolean isAsc, Boolean isFilterSt, Boolean isFilterLimitUp, Integer current, Integer size) {
        Instant dayOfStart = DateUtils.getDayOfStart(date);
        JPAQuery<TdStockScoreInfo> query = queryFactory.selectFrom(stockScoreInfo)
                .where(stockScoreInfo.addDate.eq(dayOfStart));
        if (ObjectUtil.isNotEmpty(factorList)) {
            JPAQuery<String> subQuery = queryFactory.select(stockFactorRelation.stockCode).distinct()
                    .from(stockFactorRelation)
                    .where(stockFactorRelation.addDate.eq(dayOfStart))
                    .where(stockFactorRelation.isHit.eq(true))
                    .where(stockFactorRelation.factorId.in(factorList))
                    .groupBy(stockFactorRelation.stockCode).having(stockFactorRelation.count().eq(Long.valueOf(factorList.size())));
            query.where(stockScoreInfo.stockCode.in(subQuery));
        }
        if (isFilterSt) {
            query.where(stockScoreInfo.isSt.eq(false));
        }
        if (isFilterLimitUp) {
            query.where(stockScoreInfo.limitStatus.eq(0));
        }
        NumberTemplate<Long> count = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(count).fetchOne();
        if (ObjectUtil.isAllNotEmpty(orderType, isAsc)) {
            switch (orderType) {
                case 10:
                    query = isAsc ? query.orderBy(stockScoreInfo.stockCode.asc()) : query.orderBy(stockScoreInfo.stockCode.desc());
                    break;
                case 20:
                    query = isAsc ? query.orderBy(stockScoreInfo.jjScore.asc(), stockScoreInfo.stockCode.desc()) : query.orderBy(stockScoreInfo.jjScore.desc(), stockScoreInfo.stockCode.desc());
                    break;
                case 30:
                    query = isAsc ? query.orderBy(stockScoreInfo.dxScore.asc(), stockScoreInfo.stockCode.desc()) : query.orderBy(stockScoreInfo.dxScore.desc(), stockScoreInfo.stockCode.desc());
                    break;
            }
        }
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public PageResult<List<StockFactorResp>> factorList(Boolean enabled, Integer current, Integer size) {
        JPAQuery<StockFactorResp> query = queryFactory.select(Projections.bean(StockFactorResp.class, stockFactor.id,
                stockFactor.number, stockFactor.name, stockFactor.type, stockFactor.gmtCreate, stockFactor.enabled)).from(stockFactor);
        if (ObjectUtil.isNotEmpty(enabled)) {
            query.where(stockFactor.enabled.eq(enabled));
        }
        query.orderBy(stockFactor.id.desc());
        NumberTemplate<Long> count = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(count).fetchOne();
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    @Transactional
    public BaseResult<Void> addFactor(StockFactorReq stockFactorReq) {
        List<TdStockFactor> fetch = queryFactory.selectFrom(stockFactor).where(stockFactor.number.eq(stockFactorReq.getNumber())).fetch();
        if (ObjectUtil.isNotEmpty(fetch)) {
            throw new RecordExistedException("因子编号重复");
        }
        TdStockFactor build = TdStockFactor.builder()
                .name(stockFactorReq.getName())
                .number(stockFactorReq.getNumber())
                .type(stockFactorReq.getType())
                .remark(stockFactorReq.getRemark())
                .enabled(true)
                .gmtCreate(Instant.now())
                .gmtModified(Instant.now())
                .build();
        stockFactorRepository.save(build);
        return BaseResult.successful();
    }

    @Transactional
    public BaseResult<Void> factorEnabled(Long id, Boolean enabled) {
        queryFactory.update(stockFactor).set(stockFactor.enabled, enabled).where(stockFactor.id.eq(id)).execute();
        return BaseResult.successful();
    }

    public PageResult<List<StockScoreResp>> stockScoreTestList(LocalDate date, Integer orderType, List<Long> factorList, Boolean isAsc, Boolean isFilterSt, Boolean isFilterLimitUp, Integer current, Integer size) {
        Instant dayOfStart = DateUtils.getDayOfStart(date);
        JPAQuery<TdStockScoreInfo> query = queryFactory.selectFrom(stockScoreInfo)
                .where(stockScoreInfo.addDate.eq(dayOfStart));
        if (ObjectUtil.isNotEmpty(factorList)) {
            JPAQuery<String> subQuery = queryFactory.select(stockFactorRelation.stockCode).distinct()
                    .from(stockFactorRelation)
                    .where(stockFactorRelation.addDate.eq(dayOfStart))
                    .where(stockFactorRelation.isHit.eq(true))
                    .where(stockFactorRelation.factorId.in(factorList))
                    .groupBy(stockFactorRelation.stockCode).having(stockFactorRelation.count().eq(Long.valueOf(factorList.size())));
            query.where(stockScoreInfo.stockCode.in(subQuery));
        }
        if (ObjectUtil.isNotEmpty(isFilterSt) && isFilterSt) {
            query.where(stockScoreInfo.isSt.eq(false));
        }
        if (ObjectUtil.isNotEmpty(isFilterLimitUp) && isFilterLimitUp) {
            query.where(stockScoreInfo.limitStatus.ne(0));
        }
        NumberTemplate<Long> count = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(count).fetchOne();
        if (ObjectUtil.isNotEmpty(orderType)) {
            if (orderType == 10) {
                query.orderBy(stockScoreInfo.stockCode.asc());
            } else if (orderType == 20) {
                query.orderBy(stockScoreInfo.jjScore.desc());
            } else if (orderType == 30) {
                query.orderBy(stockScoreInfo.dxScore.desc());
            }
        }
        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        List<TdStockScoreInfo> scoreInfos = query.fetch();

        List<String> stockCodes = scoreInfos.stream().map(TdStockScoreInfo::getStockCode).collect(Collectors.toList());
        Map<String, List<TdStockFactorRelation>> stockFactorMap = getStockFactorMapByFactorIds(stockCodes, date);
        Map<Long, StockFactorResp> factorMap = factorList(true, 1, 100).getData().stream().collect(Collectors.toMap(StockFactorResp::getId, Function.identity()));
        List<StockScoreResp> respList = scoreInfos.stream().map(stock -> {
            StockScoreResp resp = new StockScoreResp();
            BeanUtils.copyProperties(stock, resp);
            List<Long> factorIds = stockFactorMap.getOrDefault(stock.getStockCode(), Collections.emptyList()).stream().map(TdStockFactorRelation::getFactorId).collect(Collectors.toList());
            List<StockFactorInfoResp> factorResps = factorIds.stream().map(factorId -> {
                StockFactorResp factorResp = factorMap.getOrDefault(factorId, null);
                if (ObjectUtil.isNotEmpty(factorResp)) {
                    return StockFactorInfoResp.builder()
                            .id(factorResp.getId())
                            .number(factorResp.getNumber())
                            .name(factorResp.getName())
                            .type(factorResp.getType())
                            .build();
                }
                return null;
            }).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
            resp.setFactors(factorResps);
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(respList, Pagination.of(current, size, total));
    }

    public Map<String, List<TdStockFactorRelation>> getStockFactorMapByFactorIds(List<String> stockCodes, LocalDate date) {
        return queryFactory.selectFrom(stockFactorRelation)
                .where(stockFactorRelation.stockCode.in(stockCodes))
                .where(stockFactorRelation.addDate.eq(DateUtils.getDayOfStart(date)))
                .where(stockFactorRelation.isHit.eq(true))
                .fetch().stream().collect(Collectors.groupingBy(TdStockFactorRelation::getStockCode));
    }
}
