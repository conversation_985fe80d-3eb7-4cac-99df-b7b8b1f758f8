package cn.shrise.radium.tradeservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.entity.ServiceIntroduction;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.tradeservice.entity.TdPortfolioChannel;
import cn.shrise.radium.tradeservice.req.UpdatePortfolioChannelReq;
import cn.shrise.radium.tradeservice.service.PortfolioChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api
@RestController
@RequestMapping("channel")
@RequiredArgsConstructor
public class ChannelController {

    private final PortfolioChannelService portfolioChannelService;

    @GetMapping("bindList")
    @ApiOperation("根据用户ID获取绑定的频道列表")
    public BaseResult<List<TdPortfolioChannel>> getBindChannelList(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("频道类型") Integer channelType) {
        List<TdPortfolioChannel> bindChannelListByUserId = portfolioChannelService.getBindChannelListByUserId(userId, channelType);
        return BaseResult.success(bindChannelListByUserId);
    }

    @GetMapping("unbindList")
    @ApiOperation("根据用户ID获取未绑定的频道列表")
    public BaseResult<List<TdPortfolioChannel>> getUnbindChannelList(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("频道类型") Integer channelType) {
        List<TdPortfolioChannel> bindChannelListByUserId = portfolioChannelService.getUnbindChannelListByUserId(userId, channelType);
        return BaseResult.success(bindChannelListByUserId);
    }

    @PostMapping("bind")
    @ApiOperation("用户绑定频道")
    public BaseResult<Boolean> bindChannel(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("頻道id") Integer channelId,
            @RequestParam @ApiParam("操作人id") Integer operatorId) {
        portfolioChannelService.bindChannel(userId, channelId, operatorId);
        return BaseResult.success(true);
    }

    @PostMapping("unbind")
    @ApiOperation("用户解绑频道")
    public BaseResult<Boolean> unbindChannel(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("頻道id") Integer channelId,
            @RequestParam @ApiParam("操作人id") Integer operatorId) {
        portfolioChannelService.unbindChannel(userId, channelId, operatorId);
        return BaseResult.success(true);
    }

    @PostMapping("introduction")
    @ApiOperation("获取实战跟投服务简介列表")
    public BaseResult<List<ServiceIntroduction>> getPortfolioServiceIntroductionList(
            @RequestParam Integer companyType,
            @RequestBody @Valid BatchReq<String> req) {
        List<ServiceIntroduction> introductions = portfolioChannelService.getPortfolioServiceIntroductionList(companyType, req.getValues());
        return BaseResult.success(introductions);
    }

    @GetMapping("all")
    @ApiOperation("获取所有频道")
    public BaseResult<List<TdPortfolioChannel>> getPortfolioChannels(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("频道类型") Integer channelType) {
        List<TdPortfolioChannel> channels = portfolioChannelService.getChannels(companyType, channelType);
        return BaseResult.success(channels);
    }

    @PostMapping("update")
    @ApiOperation("更新频道")
    public BaseResult<Void> updatePortfolioChannel(
            @RequestBody @Valid UpdatePortfolioChannelReq req) {
        portfolioChannelService.updateChannel(req);
        return BaseResult.successful();
    }

    @GetMapping("info")
    @ApiOperation("获取指定频道信息")
    public BaseResult<TdPortfolioChannel> getPortfolioChannelInfo(
            @RequestParam @ApiParam("频道id") Integer id) {
        TdPortfolioChannel channel = portfolioChannelService.getPortfolioChannelInfo(id);
        return BaseResult.success(channel);
    }
}
