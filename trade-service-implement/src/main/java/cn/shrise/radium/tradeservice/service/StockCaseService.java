package cn.shrise.radium.tradeservice.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.constant.RedisKeyConstant;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordExistedException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.*;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.constant.LiveRoomMessageTypeEnum;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.contentservice.req.EditRoomMessageReq;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.ImChatRoom;
import cn.shrise.radium.imservice.req.CreateChatStreamMsgReq;
import cn.shrise.radium.notificationservice.constant.PcMessagePushTargetConstant;
import cn.shrise.radium.notificationservice.constant.PcPushMsgBodyConstant;
import cn.shrise.radium.notificationservice.constant.PushMQConstant;
import cn.shrise.radium.notificationservice.req.AppChatPushReq;
import cn.shrise.radium.notificationservice.req.InitiatePcPushReq;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.VipSubscription;
import cn.shrise.radium.orderservice.resp.ChatRoomConfigResp;
import cn.shrise.radium.pushserviceapp.constant.PlatformConstant;
import cn.shrise.radium.quoteservice.QuoteClient;
import cn.shrise.radium.quoteservice.entity.DynamicInfo;
import cn.shrise.radium.quoteservice.resp.StockResp;
import cn.shrise.radium.tradeservice.constant.*;
import cn.shrise.radium.tradeservice.dao.StockChannelScoreRecordDao;
import cn.shrise.radium.tradeservice.dto.CustomerStockCaseDto;
import cn.shrise.radium.tradeservice.dto.StockCaseDealDto;
import cn.shrise.radium.tradeservice.dto.StockCaseDto;
import cn.shrise.radium.tradeservice.entity.*;
import cn.shrise.radium.tradeservice.properties.DzStockPoolChannelProperties;
import cn.shrise.radium.tradeservice.repository.*;
import cn.shrise.radium.tradeservice.req.*;
import cn.shrise.radium.tradeservice.resp.*;
import cn.shrise.radium.tradeservice.util.CaseDealUtil;
import cn.shrise.radium.tradeservice.util.StockHelper;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.dto.DepartmentDTO;
import com.blazebit.persistence.CriteriaBuilderFactory;
import com.blazebit.persistence.PagedList;
import com.blazebit.persistence.querydsl.BlazeJPAQuery;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.shrise.radium.common.constant.RocketMQConstant.PUSH_TOPIC;
import static cn.shrise.radium.common.constant.RocketMQConstant.TAG_CHAT_PUSH_APP;
import static cn.shrise.radium.notificationservice.constant.PcPushMsgTypeEnum.CHAT;
import static cn.shrise.radium.notificationservice.constant.PushMsgTypeEnum.*;
import static cn.shrise.radium.tradeservice.constant.StockCaseDealTypeEnum.*;
import static cn.shrise.radium.tradeservice.constant.StockChannelType.*;
import static cn.shrise.radium.common.util.LockUtils.getSubStockCaseLockKey;
import static cn.shrise.radium.tradeservice.constant.TradeErrorCode.SUB_STOCK_CASE_COUNT_INSUFFICIENT;

/**
 * <AUTHOR>
 * @version 1.0
 * desc: 案例股票池
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class StockCaseService {
    private final JdbcTemplate jdbcTemplate;
    private final JPAQueryFactory jpaQueryFactory;

    private final ContentClient contentClient;

    private final StockCaseRepository stockCaseRepository;
    private final StockCaseChannelRepository stockCaseChannelRepository;
    private final StockCaseDealRepository stockCaseDealRepository;
    private final TdCaseSubInfoRepository caseSubInfoRepository;
    private final TdCaseSubRecordRepository caseSubRecordRepository;

    private final QTdStockCase qTdStockCase = QTdStockCase.tdStockCase;
    private final QTdStockCaseDeal qTdStockCaseDeal = QTdStockCaseDeal.tdStockCaseDeal;
    private final QTdStockCaseChannel qTdStockCaseChannel = QTdStockCaseChannel.tdStockCaseChannel;
    private final QTdCaseProfit qTdCaseProfit = QTdCaseProfit.tdCaseProfit;
    private final QTdCaseSubInfo qTdCaseSubInfo = QTdCaseSubInfo.tdCaseSubInfo;
    private final QTdCaseSubRecord qTdCaseSubRecord = QTdCaseSubRecord.tdCaseSubRecord;

    private final EntityManager entityManager;
    private final CriteriaBuilderFactory criteriaBuilderFactory;
    private final ImClient imClient;

    private final RocketMqUtils rocketMqUtils;

    private final StockHelper stockHelper;

    private final OrderClient orderClient;

    private final CaseChannelService caseChannelService;

    private final QuoteClient quoteClient;

    private final DzStockPoolChannelProperties dzStockPoolChannelProperties;
    private static final Integer COMPANY_TYPE = 45;
    private final CommonProperties commonProperties;

    private final RedisUtil redisUtil;
    private final static Long EXPIRE_TIME = 1L;
    private final static Long STOCK_CASE_SUB_EXPIRE_TIME = 5L;
    private final StockCaseRecordService stockCaseRecordService;
    private final StockChannelScoreRecordDao stockChannelScoreRecordDao;
    private final UserClient userClient;
    private final DzDynamicInfoService dzDynamicInfoService;

    private static final String CASE_URL = "app://chatroom/{}/caseDetail?caseId={}";

    private static final String ROOM_URL = "app://chatroom/{}/stream";
    private static final String QUANTIZE_URL = "app://specialStockPool";

    private void check(Integer operatorId, Long channelId) {
        // 检查操作员与频道的关系
        List<TdCaseChannelManagerRelation> tdCaseChannelManagerRelations = caseChannelService.getCaseChannelManagerRelations(operatorId, channelId).orElse(null);
        if (ObjectUtil.isNotEmpty(tdCaseChannelManagerRelations)) {
            return;
        }

        // 获取部门信息
        List<DepartmentDTO> departmentDTOS = userClient.getDepartment(operatorId, false)
                .orElseThrow(() -> new BusinessException("频道处理人状态已变更，请刷新后重试"));

        // 提取部门ID列表
        List<Integer> deptIds = departmentDTOS.stream()
                .map(DepartmentDTO::getId)
                .collect(Collectors.toList());

        // 检查部门与频道的关系
        List<TdCaseChannelDepartmentRelation> departmentRelations = caseChannelService.getCaseChannelDepartmentRelations(deptIds, channelId);
        if (ObjectUtil.isEmpty(departmentRelations)) {
            throw new BusinessException("频道处理人状态已变更，请刷新后重试");
        }
    }


    @Transactional
    public TdStockCase addStockCase(@NonNull EditStockCaseReq req) {
        check(req.getOperatorId(),req.getChannelId());
        TdStockCaseChannel caseChannel = jpaQueryFactory.selectFrom(qTdStockCaseChannel)
                .where(qTdStockCaseChannel.id.eq(req.getChannelId())).fetchOne();
        // channelType = 80,81 一档
        boolean isTargetChannelType = Arrays.asList(StockChannelType.SCT_ARTICLE_L1, StockChannelType.SCT_STOCK_L1).contains(caseChannel.getChannelType());
        boolean isRequiredFieldMissing = ObjectUtil.isNull(req.getLossPrice()) || ObjectUtil.isNull(req.getTargetPrice());

        if (isTargetChannelType && isRequiredFieldMissing) {
            throw new BusinessException("必填项未填写，请确认后再试");
        }

        Integer caseAuditStatus;
        Integer dealAuditStatus = null;
        if (ObjectUtil.equals(caseChannel.getNeedDecision(), true)) {
            caseAuditStatus = StockCaseAuditStatusEnum.WAITING_DECISION.getCode();
        } else {
            caseAuditStatus = StockCaseAuditStatusEnum.WAITING.getCode();
            dealAuditStatus = StockCaseAuditStatusEnum.WAITING.getCode();
        }
        TdStockCase info = TdStockCase.builder()
                .channelId(req.getChannelId())
                .companyType(req.getCompanyType())
                .labelCode(req.getLabelCode())
                .category(req.getCategory())
                .count(req.getCount())
                .isClosed(false)
                .isEnabled(true)
                .creatorId(req.getOperatorId())
                .auditStatus(caseAuditStatus)
                .analystId(req.getAnalystId())
                .sourceType(req.getSourceType())
                .score(req.getScore())
                .dzStockId(req.getDzStockId())
                .isResearch(req.getIsResearch())
                .build();
        info = stockCaseRepository.save(info);
        log.info("案例创建成功");
        TdStockCaseDeal dealInfo = TdStockCaseDeal.builder()
                .caseId(info.getId())
                .companyType(req.getCompanyType())
                .endCount(req.getCount())
                .priceUp(req.getPriceUp())
                .priceDown(req.getPriceDown())
                .lossPrice(req.getLossPrice())
                .targetPrice(req.getTargetPrice())
                .tips(req.getTips())
                .reason(req.getReason())
                .isEnabled(true)
                .risk(req.getRisk())
                .dealType(StockCaseDealTypeEnum.DT_OPEN.getValue())
                .operatorId(req.getOperatorId())
                .takeProfitUp(req.getTakeProfitUp())
                .takeProfitDown(req.getTakeProfitDown())
                .stopLossUp(req.getStopLossUp())
                .stopLossDown(req.getStopLossDown())
                .auditStatus(dealAuditStatus)
                .appPush(req.getAppPush())
                .pushStatus(StockCasePushStatusConstant.WAITING)
                .build();
        StockCaseQuoteReq quoteReq = StockCaseQuoteReq.builder()
                .caseId(info.getId())
                .dealType(DT_OPEN.getValue())
                .labelCode(req.getLabelCode())
                .build();
        rocketMqUtils.send(TradeServiceConst.TRADE_TOPIC, TradeServiceConst.MqTagType.STOCK_CASE_QUOTE, quoteReq);
        log.info("更新买入时间与价格，req:{}", quoteReq);
        addStockCaseDeal(dealInfo);
        stockCaseRecordService.createOne(info.getId(), req.getOperatorId());
        if (!ObjectUtil.equals(caseChannel.getNeedDecision(), true)) {
            stockCaseRecordService.createOne(info.getId(), null, StockCaseAuditStatusEnum.WAITING.getCode());
        }
        return info;
    }

    /**
     * 新增案例操作
     *
     * @param info 案例操作
     * @return 案例操作
     */
    @Transactional(rollbackFor = Exception.class)
    public TdStockCaseDeal addStockCaseDeal(@NonNull TdStockCaseDeal info) {
        info.setPushStatus(StockCasePushStatusConstant.WAITING);
        StockCaseDto stockCaseDto = findOneByFilter(info.getCaseId()).orElseThrow(RecordNotExistedException::new);
        //获取行情最新价
        DynamicInfo dynamicInfo = quoteClient.getDynamic(stockCaseDto.getCaseInfo().getLabelCode()).getData();
        if (ObjectUtil.isNotEmpty(dynamicInfo) && ObjectUtil.isNotEmpty(dynamicInfo.getNewPx())) {
            Double newPx = dynamicInfo.getNewPx();
            info.setPrice(newPx);
        }
        if (Arrays.asList(DT_BUY.getValue(), DT_SELL.getValue(), DT_CLOSE.getValue()).contains(info.getDealType())) {
            if (DT_CLOSE.getValue().equals(info.getDealType())) {
                info.setEndCount(0);
            }
            Integer operatorCount = Math.abs(stockCaseDto.getCaseInfo().getCount() - info.getEndCount());
            info.setCount(operatorCount);
            info = stockCaseDealRepository.save(info);
            log.info("操作保存成功");
        } else {
            info = stockCaseDealRepository.save(info);
            log.info("操作保存成功");
        }
        return info;
    }

    @Transactional(rollbackFor = Exception.class)
    public void auditStockCaseDeal(Long dealId, Integer auditorId, Integer auditStatus, String rejectReason) {
        String lockKey = LockUtils.getAuditStockCaseDealLockKey(dealId);
        Boolean lock = redisUtil.getLock(lockKey, EXPIRE_TIME);
        if (!lock) {
            throw new BusinessException("状态已更新，请刷新重试");
        }
        TdStockCaseDeal info = stockCaseDealRepository.findById(dealId).orElseThrow(RecordNotExistedException::new);
        if (!ObjectUtil.equal(info.getAuditStatus(), StockCaseAuditStatusEnum.WAITING.getCode())) {
            throw new BusinessException("状态已更新，请刷新重试");
        }
        StockCaseDto stockCaseDto = findOneByFilter(info.getCaseId()).orElseThrow(RecordNotExistedException::new);
        Instant now = Instant.now();
        //更新案例操作审核状态
        info.setAuditStatus(auditStatus);
        info.setAuditorId(auditorId);
        info.setAuditTime(now);
        info.setRejectReason(rejectReason);
        if (StockCaseDealTypeEnum.DT_OPEN.getValue().equals(info.getDealType())) {
            //更新案例状态
            TdStockCase stockCase = stockCaseDto.getCaseInfo();
            stockCase.setAuditStatus(auditStatus);
            stockCase.setAuditorId(auditorId);
            stockCase.setAuditTime(now);
            stockCase.setRejectReason(rejectReason);
            stockCaseRepository.save(stockCase);
            stockCaseRecordService.createOne(info.getCaseId(), auditorId, auditStatus);
        }
        //推送
        if (ObjectUtil.equals(StockCaseAuditStatusEnum.PASS.getCode(), auditStatus) &&
                !((ObjectUtil.equals(stockCaseDto.getChannelInfo().getChannelType(), SCT_PORTFOLIO) || ObjectUtil.equals(info.getIsHide(), true)) &&
                ObjectUtil.equals(info.getDealType(), DT_FOLLOW.getValue()))) {
            //更新仓位
            if (Arrays.asList(DT_BUY.getValue(), DT_SELL.getValue(), DT_CLOSE.getValue()).contains(info.getDealType())) {
                TdStockCase updateCase = new TdStockCase();
                if (DT_CLOSE.getValue().equals(info.getDealType())) {
                    updateCase.setIsClosed(true);
                }
                updateCase.setCount(info.getEndCount());
                updateCaseInfo(info.getCaseId(), updateCase);
                log.info("案例仓位更新成功");
            }
            TdStockCaseChannel channelInfo = stockCaseDto.getChannelInfo();
            String analystName = "--";
            if (ObjectUtil.isNotEmpty(stockCaseDto.getCaseInfo().getAnalystId())) {
                BaseResult<SsAnalystInfo> analystBaseResult = contentClient.getAnalystInfo(stockCaseDto.getCaseInfo().getAnalystId());
                if (analystBaseResult.isPresent()) {
                    analystName = analystBaseResult.getData().getName();
                }
            }
            TdStockCase caseInfo = stockCaseDto.getCaseInfo();
//            List<String> labels = Collections.singletonList(caseInfo.getLabelCode());
//            JSONArray objects = stockHelper.reqDynamicQuote(labels);
//            if (ObjectUtil.isEmpty(objects)) {
//                throw new BusinessException("未找到股票代码：" + labels + "的最新行情");
//            }
//            String labelCodeName = objects.getJSONObject(0).getStr("name");
            List<SymbolDetailResponse.Symbol> symbolList = dzDynamicInfoService.getSymbolDetail(caseInfo.getLabelCode());
            String labelCodeName = ObjectUtil.isEmpty(symbolList) ? "" : symbolList.get(0).getName();
            String labelCode = caseInfo.getLabelCode() + labelCodeName;
            if (DT_CLOSE.getValue().equals(info.getDealType())) {
                StockCaseQuoteReq quoteReq = StockCaseQuoteReq.builder()
                        .caseId(info.getCaseId())
                        .labelCode(caseInfo.getLabelCode())
                        .dealType(DT_CLOSE.getValue())
                        .build();
                rocketMqUtils.send(TradeServiceConst.TRADE_TOPIC, TradeServiceConst.MqTagType.STOCK_CASE_QUOTE, quoteReq);
                log.info("更新清仓时间与价格，req:{}", quoteReq);
            }
            if (channelInfo.getRoomId() != null) {
                contentClient.addLiveRoomMessage(EditRoomMessageReq.builder()
                        .roomId(channelInfo.getRoomId())
                        .operatorId(info.getOperatorId())
                        .messageType(LiveRoomMessageTypeEnum.LRMT_Stock)
                        .dealId(info.getId())
                        .build());
                log.info("同步直播室消息");
            } else if (channelInfo.getChatId() != null) {
                imClient.addChatStreamMessage(CreateChatStreamMsgReq.builder()
                        .chatId(channelInfo.getChatId())
                        .creatorId(info.getOperatorId())
                        .analystId(stockCaseDto.getCaseInfo().getAnalystId())
                        .companyType(info.getCompanyType())
                        .messageType(LiveRoomMessageTypeEnum.LRMT_Stock.getValue())
                        .caseDealId(info.getId())
                        .auditorId(auditorId)
                        .build());
                log.info("同步聊天室消息");
                BaseResult<ImChatRoom> chatRoomBaseResult = imClient.getChatRoom(channelInfo.getChatId());
                String roomName = null;
                if (chatRoomBaseResult.isPresent()) {
                    roomName = chatRoomBaseResult.getData().getName();
                }
                //解盘案例消息APP推送
//                if (ObjectUtil.isNotEmpty(info.getAppPush()) && info.getAppPush()) {
                String title = StrUtil.format("你订阅的【{}】，有解盘更新", roomName);
                String template = CaseDealUtil.template(info);
                String content = StrUtil.sub(StrUtil.format("【{}】{}{}", StockCaseDealEnum.getTypeValue(info.getDealType()), labelCode, template), 0, 50);
                String body = StrUtil.format("【{}】：{}，点击查看详情...", analystName, content);
                AppChatPushReq appChatPushReq = AppChatPushReq.builder()
                        .chatId(channelInfo.getChatId())
                        .title(title)
                        .body(body)
                        .pushMsgType(PMT_ChatCase.getCode())
                        .eventName(String.valueOf(info.getId()))
                        .url(StrUtil.format(ROOM_URL, channelInfo.getChatId()))
                        .build();
                appChatPushReq.setPlatform(PlatformConstant.ANDROID);
                rocketMqUtils.send(PUSH_TOPIC, TAG_CHAT_PUSH_APP, appChatPushReq);
                appChatPushReq.setPlatform(PlatformConstant.IOS);
                rocketMqUtils.send(PUSH_TOPIC, TAG_CHAT_PUSH_APP, appChatPushReq);
//                }
            }
            if (ObjectUtil.isNotEmpty(channelInfo.getChannelType()) && ObjectUtil.equals(channelInfo.getChannelType(), SCT_PORTFOLIO)) {
                BaseResult<ImChatRoom> baseResult = imClient.getChatRoomByCaseChannel(channelInfo.getId());
                if (baseResult.isPresent()) {
                    ImChatRoom chatRoom = baseResult.getData();
                    if (ObjectUtil.equals(chatRoom.getIsCasePush(), true)) {
                        String caseTitle = StrUtil.format("您订阅的【{}】，有案例更新", chatRoom.getName());
                        String template = CaseDealUtil.template(info);
                        String content = StrUtil.sub(StrUtil.format("【{}】{}{}", StockCaseDealEnum.getTypeValue(info.getDealType()), labelCode, template), 0, 50);
                        String body = StrUtil.format("【{}】：{}，点击查看详情...", analystName, content);
                        AppChatPushReq req = AppChatPushReq.builder()
                                .chatId(chatRoom.getId())
                                .title(caseTitle)
                                .body(body)
                                .pushMsgType(PMT_ChatPortfolio.getCode())
                                .eventName(String.valueOf(info.getId()))
                                .caseId(info.getCaseId())
                                .url(StrUtil.format(CASE_URL, chatRoom.getId(), info.getCaseId()))
                                .build();
                        if (StockCaseDealEnum.DT_OPEN.getCode().equals(info.getDealType())) {
                            req.setIsOpen(true);
                        } else {
                            req.setIsOpen(false);
                        }
                        //实战跟投APP推送
                        req.setPlatform(PlatformConstant.ANDROID);
                        rocketMqUtils.send(PUSH_TOPIC, TAG_CHAT_PUSH_APP, req);
                        req.setPlatform(PlatformConstant.IOS);
                        rocketMqUtils.send(PUSH_TOPIC, TAG_CHAT_PUSH_APP, req);
                        // 聊天室PC推送
                        processChatRoomPcPush(chatRoom, channelInfo, info);
                    }
                }
            }else if (Objects.equals(channelInfo.getChannelType(), SCT_QUANTIZE) && Objects.equals(info.getDealType(), StockCaseDealEnum.DT_OPEN.getCode())) {
                // 量化频道
                processQuantizeAppPush(channelInfo, info, labelCode);
            }
            //PC推送-案例
            //判断是否推送
            //去掉PC推送
//            if (PcPushMsgTypeEnum.containNumber(channelInfo.getNumber())) {
//                int msgType;
//                String title;
//                String content;
//                if (StockCaseDealTypeEnum.DT_OPEN.getValue().equals(info.getDealType())) {
//                    msgType = PMT_GcOpen.getCode();
//                    title = StringUtil.format(PcPushMsgBodyConstant.GC_CASE_TITLE, channelInfo.getName());
//                    content = StringUtil.format(PcPushMsgBodyConstant.GC_CASE_CONTENT, channelInfo.getName());
//                } else {
//                    msgType = PMT_GcAdjust.getCode();
//                    title = StringUtil.format(PcPushMsgBodyConstant.GC_DEAL_TITLE, channelInfo.getName());
//                    content = StringUtil.format(PcPushMsgBodyConstant.GC_DEAL_CONTENT, channelInfo.getName());
//                }
//
//                List<String> numberList = orderClient.getPoolCaseVipPackageNumber(channelInfo.getCompanyType(), channelInfo.getNumber()).getData();
//                InitiatePcPushReq initiatePcPushReq = InitiatePcPushReq.builder()
//                        .pushTarget(PcMessagePushTargetConstant.L1)
//                        .title(title)
//                        .content(content)
//                        .msgType(PcPushMsgTypeEnum.getMsgTypeByNumber(channelInfo.getNumber()))
//                        .subType(channelInfo.getNumber())
//                        .numbers(numberList)
//                        .companyType(caseInfo.getCompanyType())
//                        .eventName(String.valueOf(dealId))
//                        .pushMsgType(msgType)
//                        .build();
//                rocketMqUtils.send(PushMQConstant.PUSH_TOPIC, PushMQConstant.TAG_PUSH_PC, initiatePcPushReq);
//            }
            // H5 推送
            if (ObjectUtil.isNotEmpty(channelInfo.getChannelType()) && ObjectUtil.equals(channelInfo.getChannelType(), SCT_PORTFOLIO)) {
                StockCaseDealDto dto = StockCaseDealDto.builder()
                        .caseInfo(caseInfo)
                        .channelInfo(channelInfo)
                        .dealInfo(info)
                        .build();
                StockCaseNotifyResp data = StockCaseNotifyResp.builder()
                        .dto(dto)
                        .build();
                if (StockCaseDealEnum.DT_OPEN.getCode().equals(info.getDealType())) {
                    rocketMqUtils.send(TradeServiceConst.TRADE_TOPIC, TradeServiceConst.MqTagType.STOCK_CASE_OPEN_NOTIFY, data);
                } else {
                    rocketMqUtils.send(TradeServiceConst.TRADE_TOPIC, TradeServiceConst.MqTagType.STOCK_CASE_TRADE_NOTIFY, data);
                }
            }
        }
    }

    public void processQuantizeAppPush(TdStockCaseChannel channelInfo, TdStockCaseDeal info, String labelCode) {
        // 量化频道
        String caseTitle = StrUtil.format("您订阅的【{}】，有新增案例", channelInfo.getName());
        String template = CaseDealUtil.template(info);
        String content = StrUtil.sub(StrUtil.format("【{}】{}{}", StockCaseDealEnum.getTypeValue(info.getDealType()), labelCode, template), 0, 50);
        String body = StrUtil.format("{}，点击查看详情...", content);
        AppChatPushReq req = AppChatPushReq.builder()
                .title(caseTitle)
                .body(body)
                .pushMsgType(PMT_Quantize.getCode())
                .eventName(String.valueOf(info.getCaseId()))
                .caseId(info.getCaseId())
                .url(QUANTIZE_URL)
                .companyType(channelInfo.getCompanyType())
                .channelNumber(channelInfo.getNumber())
                .channelType(channelInfo.getChannelType())
                .build();
        req.setPlatform(PlatformConstant.ANDROID);
        rocketMqUtils.send(PUSH_TOPIC, TAG_CHAT_PUSH_APP, req);
        req.setPlatform(PlatformConstant.IOS);
        rocketMqUtils.send(PUSH_TOPIC, TAG_CHAT_PUSH_APP, req);
    }

    //聊天室PC推送
    public void processChatRoomPcPush(ImChatRoom chatRoom, TdStockCaseChannel channelInfo, TdStockCaseDeal info) {
        if (!Arrays.asList(DT_OPEN.getValue(), DT_BUY.getValue(), DT_SELL.getValue(), DT_CLOSE.getValue()).contains(info.getDealType())) {
            return;
        }
        int msgType;
        String title;
        String content;
        if (StockCaseDealTypeEnum.DT_OPEN.getValue().equals(info.getDealType())) {
            msgType = PMT_ChatOpen.getCode();
            title = StringUtil.format(PcPushMsgBodyConstant.CHAT_CASE_TITLE, chatRoom.getName());
            content = StringUtil.format(PcPushMsgBodyConstant.CHAT_CASE_CONTENT, chatRoom.getName());
        } else {
            msgType = PMT_ChatAdjust.getCode();
            title = StringUtil.format(PcPushMsgBodyConstant.CHAT_DEAL_TITLE, chatRoom.getName());
            content = StringUtil.format(PcPushMsgBodyConstant.CHAT_DEAL_CONTENT, chatRoom.getName());
        }
        Set<String> numberList = orderClient.getChatVipPackageNumber(chatRoom.getCompanyType(), chatRoom.getNumber()).orElse(new HashSet<>());
        String linkUrl = StrUtil.format("{}/second-gear/stock-pool-detail?caseID={}", commonProperties.getGcJcjUrl(), info.getCaseId());
        InitiatePcPushReq initiatePcPushReq = InitiatePcPushReq.builder()
                .pushTarget(PcMessagePushTargetConstant.L2)
                .title(title)
                .content(content)
                .msgType(CHAT.getMsgType())
                .subType(channelInfo.getNumber())
                .url(linkUrl)
                .numbers(new ArrayList<>(numberList))
                .companyType(info.getCompanyType())
                .eventName(String.valueOf(info.getId()))
                .pushMsgType(msgType)
                .caseId(info.getCaseId())
                .build();
        rocketMqUtils.send(PushMQConstant.PUSH_TOPIC, PushMQConstant.TAG_PUSH_PC, initiatePcPushReq);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAudit(Long caseId, Integer auditorId, Integer auditStatus, String rejectReason) {
        TdStockCase stockCase = jpaQueryFactory.selectFrom(qTdStockCase).where(qTdStockCase.id.eq(caseId)).fetchOne();
        boolean isCheck = true;
        Integer preAuditStatus = stockCase.getAuditStatus();
        if (ObjectUtil.equal(auditStatus, StockCaseAuditStatusEnum.REJECT_DECISION.getCode())) {
            isCheck = ObjectUtil.equal(preAuditStatus, StockCaseAuditStatusEnum.WAITING_DECISION.getCode());
        } else if (ObjectUtil.equal(auditStatus, StockCaseAuditStatusEnum.WAITING.getCode())) {
            isCheck = ObjectUtil.equal(preAuditStatus, StockCaseAuditStatusEnum.WAITING_DECISION.getCode());
        }
        if (!isCheck) {
            throw new BusinessException("状态已更新，请刷新重试");
        }
        jpaQueryFactory.update(qTdStockCase)
                .set(qTdStockCase.auditStatus, auditStatus)
                .set(qTdStockCase.auditorId, auditorId)
                .set(qTdStockCase.rejectReason, rejectReason)
                .set(qTdStockCase.auditTime, Instant.now())
                .where(qTdStockCase.id.eq(caseId))
                .execute();
        if (ObjectUtil.equals(auditStatus, StockCaseAuditStatusEnum.WAITING.getCode())) {
            jpaQueryFactory.update(qTdStockCaseDeal)
                    .set(qTdStockCaseDeal.auditStatus, auditStatus)
                    .where(qTdStockCaseDeal.caseId.eq(caseId))
                    .where(qTdStockCaseDeal.dealType.eq(StockCaseDealTypeEnum.DT_OPEN.getValue()))
                    .execute();
        }
        stockCaseRecordService.createOne(caseId, auditorId, auditStatus);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCaseInfo(@NonNull Long id, @NonNull TdStockCase info) {
        info.setId(id);
        String sql = SqlUtil.updateById(info);
        if (ObjectUtil.isNotEmpty(sql)) {
            jdbcTemplate.execute(sql);
        }
    }

    public Optional<StockCaseDto> findOneByFilter(Long id) {
        JPAQuery<Tuple> query = jpaQueryFactory.select(qTdStockCase, qTdStockCaseChannel)
                .from(qTdStockCase)
                .leftJoin(qTdStockCaseChannel)
                .on(qTdStockCase.channelId.eq(qTdStockCaseChannel.id))
                .where(qTdStockCase.id.eq(id));

        Tuple res = query.fetchFirst();
        if (res != null && ObjectUtil.isNotEmpty(res)) {
            return Optional.of(StockCaseDto.builder().caseInfo(res.get(qTdStockCase)).channelInfo(res.get(qTdStockCaseChannel)).build());
        } else {
            return Optional.empty();
        }
    }

    /**
     * 查询案例
     *
     * @param companyType
     * @param channelId
     * @param labelCode
     * @param timeType    1： 创建时间  2： 更新时间
     * @param startTime
     * @param endTime
     * @param isAsc
     * @param isResearch
     * @param current
     * @param size
     * @return
     */
    public PageResult<List<TdStockCase>> findAllCaseByFilter(@NonNull Integer companyType, Long channelId, Collection<Long> channelIds, String labelCode,
                                                             Integer timeType, LocalDateTime startTime, LocalDateTime endTime,
                                                             Boolean isAsc, Boolean isClosed, Boolean isTop, Boolean isTopSort,
                                                             Integer sourceType, Integer ratioType, List<Integer> auditStatusList,
                                                             Integer analystId, Boolean isResearch, Integer current, Integer size) {
        JPAQuery<TdStockCase> query = jpaQueryFactory.select(qTdStockCase).from(qTdStockCase)
                .leftJoin(qTdCaseProfit).on(qTdCaseProfit.caseId.eq(qTdStockCase.id))
                .where(qTdStockCase.companyType.eq(companyType));

        if (ObjectUtil.isNotEmpty(auditStatusList)) {
            query.where(qTdStockCase.auditStatus.in(auditStatusList));
        }

        if (ObjectUtil.isNotEmpty(channelId)) {
            query.where(qTdStockCase.channelId.eq(channelId));
        }

        if (ObjectUtil.isNotEmpty(channelIds)) {
            query.where(qTdStockCase.channelId.in(channelIds));
        }

        if (ObjectUtil.isNotEmpty(labelCode)) {
            query.where(qTdStockCase.labelCode.eq(labelCode));
        }

        if (ObjectUtil.isNotEmpty(isClosed)) {
            query.where(qTdStockCase.isClosed.eq(isClosed));
        }

        if (ObjectUtil.isNotEmpty(isTop)) {
            query.where(qTdStockCase.isTop.eq(isTop));
        }

        if (ObjectUtil.isNotEmpty(sourceType)) {
            query.where(qTdStockCase.sourceType.eq(sourceType));
        }

        if (ObjectUtil.isNotEmpty(analystId)) {
            query.where(qTdStockCase.analystId.eq(analystId));
        }

        if(ObjectUtil.isNotEmpty(isResearch)){
            query.where(qTdStockCase.isResearch.eq(isResearch));
        }

        if (ObjectUtil.isNotEmpty(startTime)) {
            Instant st = startTime.atZone(ZoneId.systemDefault()).toInstant();
            if (timeType == 1) {
                query.where(qTdStockCase.gmtCreate.goe(st));
            } else {
                query.where(qTdStockCase.gmtModified.goe(st));
            }

        }

        if (ObjectUtil.isNotEmpty(endTime)) {
            Instant et = endTime.atZone(ZoneId.systemDefault()).toInstant();
            if (timeType == 1) {
                query.where(qTdStockCase.gmtCreate.lt(et));
            } else {
                query.where(qTdStockCase.gmtModified.lt(et));
            }
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        if (ObjectUtil.isNotEmpty(isTopSort) && isTopSort) {
            query.orderBy(qTdStockCase.topTime.desc());
        }
        if (ratioType != null && isAsc != null) {
            if (ratioType == 7) {
                // 按7天涨幅排序
                if (isAsc) {
                    query.orderBy(qTdCaseProfit.max7Ratio.asc(), qTdCaseProfit.id.desc());
                } else {
                    query.orderBy(qTdCaseProfit.max7Ratio.desc(), qTdCaseProfit.id.desc());
                }
            } else if (ratioType == 15) {
                // 按15天涨幅排序
                if (isAsc) {
                    query.orderBy(qTdCaseProfit.max15Ratio.asc(), qTdCaseProfit.id.desc());
                } else {
                    query.orderBy(qTdCaseProfit.max15Ratio.desc(), qTdCaseProfit.id.desc());
                }
            } else if (ratioType == 30) {
                // 按30天涨幅排序
                if (isAsc) {
                    query.orderBy(qTdCaseProfit.max30Ratio.asc(), qTdCaseProfit.id.desc());
                } else {
                    query.orderBy(qTdCaseProfit.max30Ratio.desc(), qTdCaseProfit.id.desc());
                }
            } else if (ratioType == 180) {
                // 按180天涨幅排序
                if (isAsc) {
                    query.orderBy(qTdCaseProfit.max180Ratio.asc(), qTdCaseProfit.id.desc());
                } else {
                    query.orderBy(qTdCaseProfit.max180Ratio.desc(), qTdCaseProfit.id.desc());
                }
            } else if (ratioType == -2) {
                // 按照入池后涨幅排序
                if (isAsc) {
                    query.orderBy(qTdCaseProfit.maxRatio.asc(), qTdCaseProfit.id.desc());
                } else {
                    query.orderBy(qTdCaseProfit.maxRatio.desc(), qTdCaseProfit.id.desc());
                }
            } else if (ratioType == 9) {
                // 按T+8涨幅排序
                if (isAsc) {
                    query.orderBy(qTdCaseProfit.max9Ratio.asc(), qTdCaseProfit.id.desc());
                } else {
                    query.orderBy(qTdCaseProfit.max9Ratio.desc(), qTdCaseProfit.id.desc());
                }
            } else if (ratioType == -9) {
                // 按T+8跌幅排序
                if (isAsc) {
                    query.orderBy(qTdCaseProfit.min9Ratio.asc(), qTdCaseProfit.id.desc());
                } else {
                    query.orderBy(qTdCaseProfit.min9Ratio.desc(),  qTdCaseProfit.id.desc());
                }
            } else {
                // 按总涨跌幅排序
                if (isAsc) {
                    query.orderBy(qTdCaseProfit.changeRatio.asc(), qTdCaseProfit.id.desc());
                } else {
                    query.orderBy(qTdCaseProfit.changeRatio.desc(), qTdCaseProfit.id.desc());
                }
            }
        } else if (timeType != null && isAsc != null) {
            if (timeType == 1) {
                if (isAsc) {
                    query.orderBy(qTdStockCase.gmtCreate.asc());
                } else {
                    query.orderBy(qTdStockCase.gmtCreate.desc());
                }
            } else {
                if (isAsc) {
                    query.orderBy(qTdStockCase.gmtModified.asc());
                } else {
                    query.orderBy(qTdStockCase.gmtModified.desc());
                }
            }
        } else {
            query.orderBy(qTdStockCase.id.desc());
        }

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    /**
     * 查询案例 及其 渠道信息
     *
     * @param companyType
     * @param channelId
     * @param labelCode
     * @param timeType
     * @param startTime
     * @param endTime
     * @param isResearch
     * @param current
     * @param size
     * @return
     */
    public PageResult<List<StockCaseDto>> findCaseDetailByFilter(@NonNull Integer companyType, Long channelId, Collection<Long> channelIds, String labelCode,
                                                                 Integer timeType, LocalDateTime startTime, LocalDateTime endTime,
                                                                 Boolean isAsc, Boolean isClosed, Boolean isTop, Boolean isTopSort,
                                                                 Integer sourceType, Integer ratioType, List<Integer> auditStatusList,
                                                                 Integer analystId, Boolean isResearch, Integer current, Integer size) {
        PageResult<List<TdStockCase>> result = findAllCaseByFilter(companyType, channelId, channelIds, labelCode, timeType,
                startTime, endTime, isAsc, isClosed, isTop, isTopSort, sourceType, ratioType, auditStatusList,analystId, isResearch,current, size);
        Pagination pagination = result.getPagination();
        if (result.getData().size() > 0) {
            Set<Long> channelIdList = result.getData().stream().map(TdStockCase::getChannelId).collect(Collectors.toSet());
            Set<Long> caseIdList = result.getData().stream().map(TdStockCase::getId).collect(Collectors.toSet());
            List<TdStockCaseDeal> dealList = findCreateDeal(caseIdList);
            Map<Long, TdStockCaseDeal> dealMap = dealList.stream().collect(Collectors.toMap(TdStockCaseDeal::getCaseId, i -> i));
            List<TdStockCaseChannel> channelInfo = stockCaseChannelRepository.findAllById(channelIdList);
            Map<Long, TdStockCaseChannel> channelMap = channelInfo.stream().collect(Collectors.toMap(TdStockCaseChannel::getId, i -> i));
            List<TdCaseProfit> profitList = findCaseProfit(caseIdList);
            Map<Long, TdCaseProfit> profitMap = profitList.stream().collect(Collectors.toMap(TdCaseProfit::getCaseId, i -> i));
            List<StockCaseDto> dtoList = result.getData().stream().map(i -> StockCaseDto.builder()
                    .caseInfo(i).createDealInfo(dealMap.get(i.getId()))
                    .channelInfo(channelMap.get(i.getChannelId()))
                    .caseProfit(profitMap.get(i.getId())).build()).collect(Collectors.toList());
            return PageResult.success(dtoList, result.getPagination());
        } else {
            return PageResult.empty(pagination.getCurrent(), pagination.getSize());
        }

    }

    public List<TdStockCaseDeal> findCreateDeal(Collection<Long> caseIdList) {
        return jpaQueryFactory.select(qTdStockCaseDeal).from(qTdStockCaseDeal)
                .where(qTdStockCaseDeal.caseId.in(caseIdList))
                .where(qTdStockCaseDeal.dealType.eq(DT_OPEN.getValue())).fetch();
    }

    /**
     * 查询案例操作记录
     *
     * @param companyType
     * @param channelId   案例渠道
     * @param labelCode   股票代码
     * @param caseId      案例ID
     * @param isResearch
     * @param current
     * @param size
     * @return
     */
    public PageResult<List<StockCaseDealDto>> findAllCaseDealByFilter(@NonNull Integer companyType, Long channelId, String labelCode,
                                                                      Long caseId, Boolean isAudit, Integer auditStatus,
                                                                      Integer analystId, Integer orderType, Boolean isAsc,
                                                                      Boolean isResearch, Integer current, Integer size) {
        JPAQuery<Tuple> query = jpaQueryFactory.select(qTdStockCaseDeal, qTdStockCase, qTdStockCaseChannel)
                .from(qTdStockCaseDeal)
                .leftJoin(qTdStockCase)
                .on(qTdStockCaseDeal.caseId.eq(qTdStockCase.id))
                .leftJoin(qTdStockCaseChannel)
                .on(qTdStockCase.channelId.eq(qTdStockCaseChannel.id))
                .where(qTdStockCaseDeal.companyType.eq(companyType));
        if (ObjectUtil.isNotEmpty(caseId)) {
            query.where(qTdStockCaseDeal.caseId.eq(caseId));
        }

        if (ObjectUtil.isNotEmpty(channelId)) {
            query.where(qTdStockCase.channelId.eq(channelId));
        }

        if (ObjectUtil.isNotEmpty(labelCode)) {
            query.where(qTdStockCase.labelCode.eq(labelCode));
        }

        if (ObjectUtil.isNotEmpty(auditStatus)) {
            query.where(qTdStockCaseDeal.auditStatus.eq(auditStatus));
        }

        if (ObjectUtil.isNotNull(analystId)) {
            query.where(qTdStockCase.analystId.eq(analystId));
        }

        if(ObjectUtil.isNotEmpty(isResearch)){
            query.where(qTdStockCase.isResearch.eq(isResearch));
        }

        if (ObjectUtil.isNotEmpty(isAudit)) {
            if (isAudit) {
                query.where(qTdStockCaseDeal.auditStatus.in(StockCaseAuditStatusEnum.PASS.getCode(),
                        StockCaseAuditStatusEnum.REJECT.getCode()));
            } else {
                query.where(qTdStockCaseDeal.auditStatus.eq(StockCaseAuditStatusEnum.WAITING.getCode()));
            }
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        if (ObjectUtil.equals(orderType, 1)) {
            if (ObjectUtil.equals(isAsc, true)) {
                query.orderBy(qTdStockCase.labelCode.asc(), qTdStockCaseDeal.gmtCreate.asc());
            } else {
                query.orderBy(qTdStockCase.labelCode.desc(), qTdStockCaseDeal.gmtCreate.desc());
            }
        } else {
            query.orderBy(qTdStockCaseDeal.gmtCreate.desc());
        }

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }

        List<StockCaseDealDto> result = query.fetch().stream().map(i -> StockCaseDealDto.builder()
                .caseInfo(i.get(qTdStockCase))
                .dealInfo(i.get(qTdStockCaseDeal))
                .channelInfo(i.get(qTdStockCaseChannel))
                .build()).collect(Collectors.toList());
        return PageResult.success(result, Pagination.of(current, size, total));
    }

    /**
     * 获取案例股票最新动态
     *
     * @param companyType 公司类型
     * @param startTime   查询开始时间
     * @param serviceTime 服务开通时间
     * @param pageable    分页信息
     * @return 最新动态
     */
    public Page<StockCaseDealDto> getStockCaseNews(@NonNull Integer companyType, Integer seriesId, Instant startTime, Instant serviceTime, Pageable pageable) {

        BlazeJPAQuery<TdStockCase> blazeJpaQuery = new BlazeJPAQuery<>(entityManager, criteriaBuilderFactory);

        BlazeJPAQuery<Tuple> query = blazeJpaQuery.select(qTdStockCaseDeal, qTdStockCase, qTdStockCaseChannel)
                .from(qTdStockCaseDeal)
                .leftJoin(qTdStockCase)
                .on(qTdStockCaseDeal.caseId.eq(qTdStockCase.id))
                .leftJoin(qTdStockCaseChannel)
                .on(qTdStockCase.channelId.eq(qTdStockCaseChannel.id))
                .where(qTdStockCaseDeal.companyType.eq(companyType))
                .where(qTdStockCaseDeal.auditStatus.eq(StockCaseAuditStatusEnum.PASS.getCode()))
                .where(qTdStockCase.gmtCreate.goe(DateUtils.getDayOfStart(serviceTime)));

        if (ObjectUtil.isNotEmpty(seriesId)) {
            query.where(qTdStockCaseChannel.seriesId.eq(seriesId));
        }

//        if (serviceTime.isBefore(instantNow)) {
//            query.where(qTdStockCase.gmtCreate.goe(instantNow));
//        } else {
//            query.where(qTdStockCase.gmtCreate.goe(serviceTime));
//        }

//        if (ObjectUtil.isNotEmpty(startTime)) {
//            query.where(qTdStockCase.gmtCreate.goe(startTime));
//        }

        PagedList<Tuple> tuples = query.orderBy(qTdStockCaseDeal.gmtCreate.desc(), qTdStockCaseDeal.id.desc())
                .fetchPage((int) pageable.getOffset(), pageable.getPageSize());

        List<StockCaseDealDto> result = tuples.stream().map(i -> StockCaseDealDto.builder()
                        .caseInfo(i.get(qTdStockCase))
                        .dealInfo(i.get(qTdStockCaseDeal))
                        .channelInfo(i.get(qTdStockCaseChannel))
                        .build())
                .collect(Collectors.toList());
        return new PageImpl<>(result, pageable, tuples.getTotalSize());
    }

    public Page<StockCaseDealDto> getStockCaseList(@NonNull Integer companyType, Long channelId, Integer analystId, Boolean filterOpen,
                                                   Boolean filterClose, Boolean filterTop, Boolean checkOpen,
                                                   Integer checkOpenType,
                                                   Instant caseStartTime, Instant caseEndTime,
                                                   Instant dealStartTime, Instant dealEndTime,
                                                   Instant openTime, Pageable pageable) {

        List<TdCaseChannelAnalystRelation> analystRelations = caseChannelService.getCaseChannelAnalystRelations(channelId).orElse(null);

        if (ObjectUtil.isEmpty(analystRelations)) {
            return Page.empty();
        }
        BlazeJPAQuery<TdStockCase> blazeJpaQuery = new BlazeJPAQuery<>(entityManager, criteriaBuilderFactory);
        BlazeJPAQuery<Tuple> query = blazeJpaQuery.select(qTdStockCaseDeal, qTdStockCase, qTdStockCaseChannel)
                .from(qTdStockCaseDeal)
                .leftJoin(qTdStockCase)
                .on(qTdStockCaseDeal.caseId.eq(qTdStockCase.id))
                .leftJoin(qTdStockCaseChannel)
                .on(qTdStockCase.channelId.eq(qTdStockCaseChannel.id))
                .where(qTdStockCaseDeal.companyType.eq(companyType))
                .where(qTdStockCaseDeal.auditStatus.eq(StockCaseAuditStatusEnum.PASS.getCode()))
                .where(qTdStockCase.analystId.in(analystRelations.stream().map(TdCaseChannelAnalystRelation::getAnalystId).collect(Collectors.toList())));

        if (ObjectUtil.isNotNull(channelId)) {
            query.where(qTdStockCase.channelId.eq(channelId));
        }

        if (ObjectUtil.isNotNull(analystId)) {
            query.where(qTdStockCase.analystId.eq(analystId));
        }

        if (ObjectUtil.equals(filterOpen, true)) {
            query.where(qTdStockCaseDeal.dealType.eq(DT_OPEN.getValue()));
        }

        if (ObjectUtil.equals(filterClose, true)) {
            query.where(qTdStockCase.isClosed.eq(true));
        }

        if (ObjectUtil.equals(filterTop, true)) {
            query.where(qTdStockCase.isTop.eq(true));
        }

        if (ObjectUtil.isNotNull(openTime)) {
            if (ObjectUtil.equals(checkOpen, true)) {
                if (ObjectUtil.equals(checkOpenType, 1)) {
                    query.where(qTdStockCase.gmtCreate.goe(openTime));
                } else if (ObjectUtil.equals(checkOpenType, 2)) {
                    query.where(qTdStockCaseDeal.gmtCreate.goe(openTime));
                }
            }
        }

        if (ObjectUtil.isNotNull(caseStartTime)) {
            query.where(qTdStockCase.gmtCreate.goe(caseStartTime));
        }

        if (ObjectUtil.isNotNull(caseEndTime)) {
            query.where(qTdStockCase.gmtCreate.lt(caseEndTime));
        }

        if (ObjectUtil.isNotNull(dealStartTime)) {
            query.where(qTdStockCaseDeal.gmtCreate.goe(dealStartTime));
        }

        if (ObjectUtil.isNotNull(dealEndTime)) {
            query.where(qTdStockCaseDeal.gmtCreate.lt(dealEndTime));
        }

        PagedList<Tuple> tuples = query.orderBy(qTdStockCaseDeal.gmtCreate.desc(), qTdStockCaseDeal.id.desc())
                .fetchPage((int) pageable.getOffset(), pageable.getPageSize());

        List<StockCaseDealDto> result = tuples.stream().map(i -> StockCaseDealDto.builder()
                        .caseInfo(i.get(qTdStockCase))
                        .dealInfo(i.get(qTdStockCaseDeal))
                        .channelInfo(i.get(qTdStockCaseChannel))
                        .build())
                .collect(Collectors.toList());
        return new PageImpl<>(result, pageable, tuples.getTotalSize());
    }

    public List<StockCaseInfoResp> getAllByIds(List<Long> dealIds) {
        return jpaQueryFactory.select(Projections.bean(StockCaseInfoResp.class, qTdStockCaseDeal.id, qTdStockCase.labelCode, qTdStockCase.category,
                        qTdStockCaseDeal.dealType, qTdStockCaseDeal.price, qTdStockCaseDeal.count, qTdStockCaseDeal.endCount, qTdStockCaseDeal.reason,
                        qTdStockCaseDeal.risk, qTdStockCaseDeal.tips, qTdStockCaseDeal.priceUp, qTdStockCaseDeal.priceDown, qTdStockCaseDeal.takeProfitUp,
                        qTdStockCaseDeal.takeProfitDown, qTdStockCaseDeal.stopLossUp, qTdStockCaseDeal.stopLossDown, qTdStockCaseDeal.gmtCreate.as("createTime")))
                .from(qTdStockCaseDeal)
                .leftJoin(qTdStockCase).on(qTdStockCaseDeal.caseId.eq(qTdStockCase.id))
                .where(qTdStockCaseDeal.id.in(dealIds))
                .fetch();
    }

    @Transactional
    public void editCaseTop(Long caseId, Boolean isTop) {
        TdStockCase stockCase = stockCaseRepository.findById(caseId).orElseThrow(() -> new RecordExistedException("案例不存在"));
        TdStockCaseChannel caseChannel = stockCaseChannelRepository.findById(stockCase.getChannelId()).orElse(new TdStockCaseChannel());
        if (!Arrays.asList(StockChannelType.SCT_ARTICLE_L1, StockChannelType.SCT_STOCK_L1).contains(caseChannel.getChannelType())) {
            if (Objects.equals(isTop, true) && !Objects.equals(stockCase.getIsClosed(), true)) {
                throw new BusinessException("未清仓案例不能置顶");
            }
        }
        if (Objects.equals(isTop, true) && !Objects.equals(stockCase.getIsTop(), true)) {
            jpaQueryFactory.update(qTdStockCase)
                    .where(qTdStockCase.id.eq(caseId))
                    .set(qTdStockCase.isTop, true)
                    .set(qTdStockCase.topTime, Instant.now())
                    .execute();
        }else if (Objects.equals(isTop, false) && Objects.equals(stockCase.getIsTop(), true)) {
            jpaQueryFactory.update(qTdStockCase)
                    .where(qTdStockCase.id.eq(caseId))
                    .set(qTdStockCase.isTop, false)
                    .execute();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void editCaseSource(Long caseId, Integer sourceType) {
        jpaQueryFactory.update(qTdStockCase)
                .where(qTdStockCase.id.eq(caseId))
                .set(qTdStockCase.sourceType, sourceType)
                .execute();
    }

    public List<TdCaseProfit> findCaseProfit(Collection<Long> caseIdList) {
        return jpaQueryFactory.select(qTdCaseProfit).from(qTdCaseProfit)
                .where(qTdCaseProfit.caseId.in(caseIdList)).fetch();
    }

    public TdCaseProfit findCaseProfitByCaseId(Long caseId) {
        return jpaQueryFactory.select(qTdCaseProfit).from(qTdCaseProfit)
                .where(qTdCaseProfit.caseId.eq(caseId)).fetchOne();
    }

    public TdStockCase findStockCaseById(Long caseId) {
        return jpaQueryFactory.selectFrom(qTdStockCase)
                .where(qTdStockCase.id.eq(caseId))
                .fetchOne();
    }

    public List<TdStockCase> findCaseByProcessDays(Boolean isClosed, Integer processDays) {
        JPAQuery<TdStockCase> query = jpaQueryFactory.select(qTdStockCase).from(qTdStockCase)
                .leftJoin(qTdCaseProfit).on(qTdStockCase.id.eq(qTdCaseProfit.caseId));
        if (ObjectUtil.isNotEmpty(isClosed)) {
            query.where(qTdStockCase.isClosed.eq(isClosed));
        }
        if (ObjectUtil.isNotEmpty(processDays)) {
            query.where(qTdCaseProfit.processDays.loe(processDays));
        }
        return query.fetch();
    }

    public List<TdStockCase> findCaseByProcessDays(Boolean isClosed, Integer processDays, Integer startProcessDays) {
        JPAQuery<TdStockCase> query = jpaQueryFactory.select(qTdStockCase).from(qTdStockCase)
                .leftJoin(qTdCaseProfit).on(qTdStockCase.id.eq(qTdCaseProfit.caseId))
                .orderBy(qTdCaseProfit.id.desc());
        if (ObjectUtil.isNotEmpty(isClosed)) {
            query.where(qTdStockCase.isClosed.eq(isClosed));
        }
        if (ObjectUtil.isNotEmpty(processDays)) {
            query.where(qTdCaseProfit.processDays.loe(processDays));
        }
        if (ObjectUtil.isNotEmpty(startProcessDays)) {
            query.where(qTdCaseProfit.processDays.goe(startProcessDays));
        }
        return query.fetch();
    }

    public List<TdStockCase> findCaseByCreateTime(Integer days) {
        JPAQuery<TdStockCase> query = jpaQueryFactory.select(qTdStockCase).from(qTdStockCase)
                .leftJoin(qTdCaseProfit).on(qTdStockCase.id.eq(qTdCaseProfit.caseId))
                .orderBy(qTdStockCase.id.desc());
        if (ObjectUtil.isNotEmpty(days)) {
            Instant endTime = DateUtils.getDayOfEnd(Instant.now());
            Instant startTime = endTime.minus(days, ChronoUnit.DAYS);
            query.where(qTdStockCase.gmtCreate.gt(startTime)).where(qTdStockCase.gmtCreate.loe(endTime));
        }
        return query.fetch();
    }

    public BaseResult<TdStockCase> getLastStockCaseDetail(Integer companyType, Long channelId, Integer auditStatus) {
        JPAQuery<TdStockCase> query = jpaQueryFactory.selectFrom(qTdStockCase)
                .where(qTdStockCase.companyType.eq(companyType));
        if (ObjectUtil.isNotEmpty(channelId)) {
            query.where(qTdStockCase.channelId.eq(channelId));
        }
        if (ObjectUtil.isNotEmpty(auditStatus)) {
            query.where(qTdStockCase.auditStatus.eq(auditStatus));
        }
        query.orderBy(qTdStockCase.gmtCreate.desc());
        return BaseResult.success(query.fetchFirst());
    }

    public BaseResult<List<TdStockCase>> getStockCaseSignalList(
            Integer companyType, List<Long> channelIdList, String labelCode, Integer auditStatus) {
        JPAQuery<TdStockCase> query = jpaQueryFactory.selectFrom(qTdStockCase)
                .where(qTdStockCase.companyType.eq(companyType))
                .where(qTdStockCase.isEnabled.eq(true));
        if (ObjectUtil.isNotEmpty(channelIdList)) {
            query.where(qTdStockCase.channelId.in(channelIdList));
        }
        if (ObjectUtil.isNotEmpty(labelCode)) {
            query.where(qTdStockCase.labelCode.eq(labelCode));
        }
        if (ObjectUtil.isNotEmpty(auditStatus)) {
            query.where(qTdStockCase.auditStatus.eq(auditStatus));
        }
        query.orderBy(qTdStockCase.gmtCreate.asc());
        return BaseResult.success(query.fetch());
    }

    public BaseResult<StockCaseDto> getStockCaseDetailInfo(Long caseId) {
        TdStockCase stockCase = findStockCaseById(caseId);
        if (ObjectUtil.isEmpty(stockCase)) {
            return BaseResult.success(null);
        }

        List<TdStockCaseDeal> dealList = findCreateDeal(Collections.singletonList(stockCase.getId()));
        Map<Long, TdStockCaseDeal> dealMap = dealList.stream().collect(Collectors.toMap(TdStockCaseDeal::getCaseId, i -> i));
        TdStockCaseChannel caseChannel = stockCaseChannelRepository.findById(stockCase.getChannelId()).orElse(null);
        List<TdCaseProfit> profitList = findCaseProfit(Collections.singletonList(stockCase.getId()));
        Map<Long, TdCaseProfit> profitMap = profitList.stream().collect(Collectors.toMap(TdCaseProfit::getCaseId, i -> i));
        StockCaseDto caseDto = StockCaseDto.builder()
                .caseInfo(stockCase)
                .createDealInfo(ObjectUtil.isNotEmpty(dealMap.get(stockCase.getId())) ? dealMap.get(stockCase.getId()) : null)
                .channelInfo(caseChannel)
                .caseProfit(ObjectUtil.isNotEmpty(profitMap.get(stockCase.getId())) ? profitMap.get(stockCase.getId()) : null)
                .build();
        return BaseResult.success(caseDto);
    }

    public List<StockCaseDealDto> findNeedPushCase(Integer companyType, List<Integer> dealTypes, Integer auditStatus) {

        JPAQuery<Tuple> query = jpaQueryFactory.select(qTdStockCaseDeal, qTdStockCase, qTdStockCaseChannel)
                .from(qTdStockCaseDeal)
                .leftJoin(qTdStockCase)
                .on(qTdStockCaseDeal.caseId.eq(qTdStockCase.id))
                .leftJoin(qTdStockCaseChannel)
                .on(qTdStockCase.channelId.eq(qTdStockCaseChannel.id))
                .where(qTdStockCaseDeal.companyType.eq(companyType))
                .where(qTdStockCaseDeal.auditStatus.eq(auditStatus))
                .where(qTdStockCaseDeal.pushStatus.eq(StockCasePushStatusConstant.WAITING))
                .where(qTdStockCaseDeal.dealType.in(dealTypes));

        List<StockCaseDealDto> result = query.stream().map(i -> StockCaseDealDto.builder()
                        .caseInfo(i.get(qTdStockCase))
                        .dealInfo(i.get(qTdStockCaseDeal))
                        .channelInfo(i.get(qTdStockCaseChannel))
                        .build())
                .collect(Collectors.toList());
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStockCasePushStatusToFinished(Long id) {
        jpaQueryFactory.update(qTdStockCaseDeal)
                .set(qTdStockCaseDeal.pushStatus, StockCasePushStatusConstant.FINISHED)
                .where(qTdStockCaseDeal.id.eq(id))
                .where(qTdStockCaseDeal.pushStatus.eq(StockCasePushStatusConstant.WAITING))
                .execute();
    }

    public PageResult<List<TdCaseSubInfo>> getStockCaseSubInfoList(Long caseId, String content, Integer current, Integer size) {
        JPAQuery<TdCaseSubInfo> query = jpaQueryFactory.selectFrom(qTdCaseSubInfo)
                .where(qTdCaseSubInfo.caseId.eq(caseId))
                .where(qTdCaseSubInfo.enabled.eq(true));
        if (ObjectUtil.isNotEmpty(content)) {
            query.where(qTdCaseSubInfo.userId.eq(DesensitizeUtil.maskToId(content)));
        }
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qTdCaseSubInfo.gmtCreate.desc(), qTdCaseSubInfo.userId.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }

        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public PageResult<List<TdCaseSubRecord>> getStockCaseSubRecordList(Long caseId, String content, Integer current, Integer size) {
        JPAQuery<TdCaseSubRecord> query = jpaQueryFactory.selectFrom(qTdCaseSubRecord)
                .where(qTdCaseSubRecord.caseId.eq(caseId));

        if (ObjectUtil.isNotEmpty(content)) {
            query.where(qTdCaseSubRecord.userId.eq(DesensitizeUtil.maskToId(content)));
        }
        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qTdCaseSubRecord.gmtCreate.desc(), qTdCaseSubRecord.userId.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }

        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public TdCaseSubInfo filterStockCaseSubInfo(Long caseId, Integer userId) {
        TdCaseSubInfo info = jpaQueryFactory.selectFrom(qTdCaseSubInfo)
                .where(qTdCaseSubInfo.caseId.eq(caseId))
                .where(qTdCaseSubInfo.userId.eq(userId))
                .fetchFirst();
        return info;
    }

    @Transactional(rollbackFor = Exception.class)
    public void editStockCaseSubInfo(EditStockCaseSubInfoReq req) {
        String lockKey = getSubStockCaseLockKey(req.getUserId());
        Boolean locked = redisUtil.getLock(lockKey, lockKey);
        if (!locked) {
            throw new BusinessException("StockCaseSub locked");
        }
        try {
            if(req.getIsSub()) {
                Integer lastCount  = getCaseSubLastCount(req.getUserId(), req.getCompanyType()).getData().getSubLastCount();
                if (lastCount <= 0) {
                    throw new BusinessException(SUB_STOCK_CASE_COUNT_INSUFFICIENT);
                }
            }
            TdCaseSubInfo info = filterStockCaseSubInfo(req.getCaseId(), req.getUserId());
            if (ObjectUtil.isEmpty(info)) {
                TdCaseSubInfo build = TdCaseSubInfo.builder()
                        .caseId(req.getCaseId())
                        .userId(req.getUserId())
                        .enabled(req.getIsSub())
                        .build();
                caseSubInfoRepository.save(build);
            } else {
                jpaQueryFactory.update(qTdCaseSubInfo)
                        .where(qTdCaseSubInfo.id.eq(info.getId()))
                        .set(qTdCaseSubInfo.enabled, req.getIsSub())
                        .execute();
            }
            createStockCaseSubRecord(req);
        } finally {
            redisUtil.releaseLock(lockKey);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void createStockCaseSubRecord(EditStockCaseSubInfoReq req) {
        TdCaseSubRecord record = TdCaseSubRecord.builder()
                .caseId(req.getCaseId())
                .userId(req.getUserId())
                .operatorId(req.getOperatorId())
                .isSub(req.getIsSub())
                .build();
        caseSubRecordRepository.save(record);
    }

    public void batchEditCaseSub(BatchEditStockCaseSubInfoReq req) {
        if (ObjectUtil.isEmpty(req.getUserIdList())) {
            return;
        }
        List<TdCaseSubInfo> infoList = new ArrayList<>();
        List<TdCaseSubRecord> recordList = new ArrayList<>();
        Long caseId = req.getCaseId();
        Integer operatorId = req.getOperatorId();
        req.getUserIdList().forEach(userId -> {
            TdCaseSubInfo info = TdCaseSubInfo.builder()
                    .caseId(caseId)
                    .userId(userId)
                    .enabled(true)
                    .build();
            infoList.add(info);
            TdCaseSubRecord record = TdCaseSubRecord.builder()
                    .caseId(caseId)
                    .userId(userId)
                    .operatorId(operatorId)
                    .isSub(true)
                    .build();
            recordList.add(record);
        });
        String batchInfoSql = SqlUtil.onDuplicateKeyUpdateSql(infoList);
        String batchRecordSql = SqlUtil.onDuplicateKeyUpdateSql(recordList);
        jdbcTemplate.execute(batchInfoSql);
        jdbcTemplate.execute(batchRecordSql);
    }

    public BaseResult<List<Integer>> filterStockCaseSubInfoUser(Long caseId, Collection<Integer> userIds) {
        List<Integer> filterUser = jpaQueryFactory.select(qTdCaseSubInfo.userId)
                .from(qTdCaseSubInfo)
                .where(qTdCaseSubInfo.caseId.eq(caseId))
                .where(qTdCaseSubInfo.userId.in(userIds))
                .where(qTdCaseSubInfo.enabled.eq(true))
                .fetch();
        return BaseResult.success(filterUser);
    }

    public PageResult<List<TdStockCase>> filterStockCaseByChannel(Integer companyType, Long channelId, String content, Long startTime, Long endTime, Integer current, Integer size) {
        JPAQuery<TdStockCase> query = jpaQueryFactory.select(qTdStockCase)
                .from(qTdStockCase)
                .leftJoin(qTdStockCaseDeal)
                .on(qTdStockCase.id.eq(qTdStockCaseDeal.caseId))
                .where(qTdStockCaseDeal.dealType.eq(DT_OPEN.getValue()))
                .where(qTdStockCaseDeal.auditStatus.eq(StockCaseAuditStatusEnum.PASS.getCode()))
                .where(qTdStockCase.companyType.eq(companyType))
                .where(qTdStockCase.channelId.eq(channelId));
        if (ObjectUtil.isNotEmpty(content)) {
            try {
                Long id = Long.valueOf(content);
                query.where(qTdStockCase.id.eq(id));
            } catch (NumberFormatException e) {
                query.where(qTdStockCase.labelCode.eq(content));
            }
        }

        if (ObjectUtil.isNotNull(startTime)) {
            query.where(qTdStockCase.gmtCreate.goe(Instant.ofEpochMilli(startTime)));
        }

        if (ObjectUtil.isNotNull(endTime)) {
            query.where(qTdStockCase.gmtCreate.lt(Instant.ofEpochMilli(endTime)));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qTdStockCase.gmtCreate.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }

        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public BaseResult<List<StrategyStockCaseResp>> getStrategyStockCaseRespList(Integer companyType, Long channelId) {
        List<TdStockCase> fetch = jpaQueryFactory.selectFrom(qTdStockCase)
                .where(qTdStockCase.companyType.eq(companyType))
                .where(qTdStockCase.channelId.eq(channelId))
                .where(qTdStockCase.isClosed.eq(false))
                .where(qTdStockCase.auditStatus.eq(StockCaseAuditStatusEnum.PASS.getCode()))
                .orderBy(qTdStockCase.gmtCreate.desc())
                .fetch();
        List<String> labelCodeList = fetch.stream().map(TdStockCase::getLabelCode).collect(Collectors.toList());
        List<StockResp> stockRespList = quoteClient.getStockInfoListByLabel(BatchReq.of(labelCodeList)).getData();
        Map<String, StockResp> stockRespMap = stockRespList.stream().collect(Collectors.toMap(StockResp::getLabel, Function.identity()));
        List<StrategyStockCaseResp> strategyStockCaseResps = new ArrayList<>();
        for (TdStockCase tdStockCase : fetch) {
            String labelCode = tdStockCase.getLabelCode();
            StockResp stockResp = stockRespMap.getOrDefault(labelCode, new StockResp());
            StrategyStockCaseResp build = StrategyStockCaseResp.builder()
                    .labelCode(labelCode)
                    .swCode2(stockResp.getSwCode2())
                    .swName2(stockResp.getSwName2())
                    .build();
            strategyStockCaseResps.add(build);
        }
        return BaseResult.success(strategyStockCaseResps);
    }

    public BaseResult<List<StrategyStockCaseResp>> getStrategyStockCaseWithoutQuoteRespList(Integer companyType, Long channelId, Instant openTime) {
        List<TdStockCase> fetch = jpaQueryFactory.selectFrom(qTdStockCase)
                .where(qTdStockCase.companyType.eq(companyType))
                .where(qTdStockCase.channelId.eq(channelId))
                .where(qTdStockCase.isClosed.eq(false))
                .where(qTdStockCase.auditStatus.eq(StockCaseAuditStatusEnum.PASS.getCode()))
                .where(qTdStockCase.gmtCreate.goe(openTime))
                .orderBy(qTdStockCase.gmtCreate.desc())
                .fetch();
        List<StrategyStockCaseResp> strategyStockCaseResps = new ArrayList<>();
        for (TdStockCase tdStockCase : fetch) {
            String labelCode = tdStockCase.getLabelCode();
            StrategyStockCaseResp build = StrategyStockCaseResp.builder()
                    .labelCode(labelCode)
                    .build();
            strategyStockCaseResps.add(build);
        }
        return BaseResult.success(strategyStockCaseResps);
    }

    public PageResult<List<StrategyStockCaseResp>> getStrategyProfitDetailList(Integer companyType, Long channelId, Integer current, Integer size) {
        JPAQuery<Tuple> query = jpaQueryFactory.select(qTdStockCase.labelCode, qTdStockCase.price, qTdStockCase.gmtCreate, qTdCaseProfit.changeRatio)
                .from(qTdStockCase)
                .leftJoin(qTdCaseProfit).on(qTdStockCase.id.eq(qTdCaseProfit.caseId))
                .where(qTdStockCase.companyType.eq(companyType))
                .where(qTdStockCase.channelId.eq(channelId))
                .where(qTdCaseProfit.gmtCreate.goe(Instant.now().minus(180, ChronoUnit.DAYS)));

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qTdStockCase.id.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }

        List<Tuple> tuples = query.fetch();

        List<StrategyStockCaseResp> strategyStockCaseResps = new ArrayList<>();
        for (Tuple tuple : tuples) {
            String labelCode = tuple.get(qTdStockCase.labelCode);
            StrategyStockCaseResp build = StrategyStockCaseResp.builder()
                    .labelCode(labelCode)
                    .price(tuple.get(qTdStockCase.price))
                    .gmtCreate(tuple.get(qTdStockCase.gmtCreate))
                    .changeRatio(tuple.get(qTdCaseProfit.changeRatio))
                    .build();
            strategyStockCaseResps.add(build);
        }
        return PageResult.success(strategyStockCaseResps, Pagination.of(current, size, total));
    }

    public List<StrategyStockCaseResp> getStrategyStockCaseRespList(Integer companyType, Collection<Long> channelIds) {
        List<Tuple> tuples = jpaQueryFactory.select(qTdStockCase.channelId, qTdCaseProfit.changeRatio).from(qTdStockCase)
                .leftJoin(qTdCaseProfit).on(qTdStockCase.id.eq(qTdCaseProfit.caseId))
                .where(qTdStockCase.companyType.eq(companyType))
                .where(qTdStockCase.channelId.in(channelIds))
                .where(qTdCaseProfit.gmtCreate.goe(Instant.now().minus(180, ChronoUnit.DAYS)))
                .fetch();
        List<StrategyStockCaseResp> strategyStockCaseResps = new ArrayList<>();
        for (Tuple tuple : tuples) {
            StrategyStockCaseResp build = StrategyStockCaseResp.builder()
                    .changeRatio(tuple.get(qTdCaseProfit.changeRatio))
                    .channelId(tuple.get(qTdStockCase.channelId))
                    .build();
            strategyStockCaseResps.add(build);
        }
        return strategyStockCaseResps;
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateProfitRatio(Collection<TdStrategyChannelExt> channelExts) {
        if (ObjectUtil.isEmpty(channelExts)) {
            return;
        }
        List<TdStrategyChannelExt> dataList = channelExts.stream().map(i -> {
            TdStrategyChannelExt data = new TdStrategyChannelExt();
            BeanUtils.copyProperties(i, data);
            return data;
        }).collect(Collectors.toList());
        String sqlStr = SqlUtil.onDuplicateKeyUpdateSql(dataList);
        jdbcTemplate.batchUpdate(sqlStr);
    }

    public PageResult<List<CustomerStockCaseDto>> getCustomerSubStockCaseList(Integer companyType, Integer userId, Integer current, Integer size) {

        JPAQuery<Tuple> query = jpaQueryFactory.select(qTdStockCaseDeal, qTdStockCase, qTdStockCaseChannel,qTdCaseSubInfo,qTdCaseProfit)
                .from(qTdCaseSubInfo)
                .leftJoin(qTdStockCase).on(qTdCaseSubInfo.caseId.eq(qTdStockCase.id))
                .leftJoin(qTdStockCaseChannel).on(qTdStockCase.channelId.eq(qTdStockCaseChannel.id))
                .leftJoin(qTdCaseProfit).on(qTdCaseProfit.caseId.eq(qTdStockCase.id))
                .leftJoin(qTdStockCaseDeal).on(qTdStockCaseDeal.caseId.eq(qTdStockCase.id).and(qTdStockCaseDeal.id.in(
                        jpaQueryFactory.select(qTdStockCaseDeal.id.max()).from(qTdStockCaseDeal)
                                .where(qTdStockCaseDeal.caseId.eq(qTdStockCase.id)
                                        .and(qTdStockCaseDeal.priceUp.isNotNull())
                                        .and(qTdStockCaseDeal.priceDown.isNotNull())
                                        .and(qTdStockCaseDeal.isEnabled.eq(true)))
                )))
                .where(qTdCaseSubInfo.userId.eq(userId))
                .where(qTdCaseSubInfo.enabled.eq(true))
                .where(qTdStockCase.isEnabled.eq(true))
                .where(qTdStockCase.companyType.eq(companyType))
                .where(qTdStockCaseDeal.companyType.eq(companyType));


        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qTdStockCase.gmtCreate.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }

        List<CustomerStockCaseDto> result = query.fetch().stream().map(i -> CustomerStockCaseDto.builder()
                        .caseInfo(i.get(qTdStockCase))
                        .channelInfo(i.get(qTdStockCaseChannel))
                        .subInfo(i.get(qTdCaseSubInfo))
                        .caseProfit(i.get(qTdCaseProfit))
                        .latestCaseDeal(i.get(qTdStockCaseDeal))
                        .build())
                .collect(Collectors.toList());

        return PageResult.success(result, Pagination.of(current, size, total));
    }

    public PageResult<List<StockCaseDealDto>> getMySubStockCaseList(Integer companyType, Integer userId, Instant openTime, Integer current, Integer size) {

        JPAQuery<Tuple> query = jpaQueryFactory.select(qTdStockCaseDeal, qTdStockCase, qTdStockCaseChannel)
                .from(qTdCaseSubInfo)
                .leftJoin(qTdStockCase)
                .on(qTdCaseSubInfo.caseId.eq(qTdStockCase.id))
                .leftJoin(qTdStockCaseDeal)
                .on(qTdStockCaseDeal.caseId.eq(qTdStockCase.id))
                .leftJoin(qTdStockCaseChannel)
                .on(qTdStockCase.channelId.eq(qTdStockCaseChannel.id))
                .where(qTdCaseSubInfo.enabled.eq(true))
                .where(qTdCaseSubInfo.userId.eq(userId))
                .where(qTdStockCaseDeal.companyType.eq(companyType))
                .where(qTdStockCaseDeal.auditStatus.eq(StockCaseAuditStatusEnum.PASS.getCode()))
                .where(qTdStockCaseDeal.dealType.eq(DT_OPEN.getValue()));


        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qTdStockCaseDeal.gmtCreate.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }
        List<StockCaseDealDto> result = query.fetch().stream().map(i -> StockCaseDealDto.builder()
                        .caseInfo(i.get(qTdStockCase))
                        .dealInfo(i.get(qTdStockCaseDeal))
                        .channelInfo(i.get(qTdStockCaseChannel))
                        .build())
                .collect(Collectors.toList());

        return PageResult.success(result, Pagination.of(current, size, total));
    }

    public BaseResult<SubStockCaseCountResp> getMySubStockCaseCount(Integer userId, Boolean isSelf) {

        List<TdCaseSubRecord> recordList = jpaQueryFactory.select(qTdCaseSubRecord)
                .from(qTdCaseSubInfo)
                .leftJoin(qTdCaseSubRecord)
                .on(qTdCaseSubInfo.caseId.eq(qTdCaseSubRecord.caseId).and(qTdCaseSubInfo.userId.eq(qTdCaseSubRecord.userId)))
                .where(qTdCaseSubInfo.userId.eq(userId))
                .where(qTdCaseSubInfo.enabled.eq(true))
                .fetch();
        Integer subCount = 0;
        if (ObjectUtil.isNotEmpty(recordList)) {
            recordList = recordList.stream().collect(Collectors.groupingBy(TdCaseSubRecord::getCaseId)).values()
                    .stream().flatMap(e -> Stream.of(e.stream().max(Comparator.comparing(TdCaseSubRecord::getGmtCreate)).get()))
                    .filter(TdCaseSubRecord::getIsSub).collect(Collectors.toList());
            subCount = recordList.size();
            if (subCount != 0 && ObjectUtil.isNotEmpty(isSelf)) {
                if (isSelf) {
                    // 用户本人关注
                    subCount = (int) recordList.stream().filter(e -> e.getUserId().equals(e.getOperatorId())).count();
                } else {
                    // 后台非本人关注
                    subCount = (int) recordList.stream().filter(e -> !e.getUserId().equals(e.getOperatorId())).count();
                }
            }
        }
        SubStockCaseCountResp resp = SubStockCaseCountResp.builder()
                .userId(userId)
                .subCount(subCount)
                .build();
        return BaseResult.success(resp);
    }

    public BaseResult<List<TdCaseSubInfo>> filterSubByUserId(Integer userId) {
        List<TdCaseSubInfo> fetch = jpaQueryFactory.selectFrom(qTdCaseSubInfo)
                .where(qTdCaseSubInfo.userId.eq(userId))
                .where(qTdCaseSubInfo.enabled.eq(true))
                .fetch();
        return BaseResult.success(fetch);
    }

    public BaseResult<List<StrategyStockCaseResp>> getStrategyStock(Integer companyType, Long channelId, Instant openTime) {
        List<TdStockCase> fetch = jpaQueryFactory.select(qTdStockCase).from(qTdStockCase)
                .where(qTdStockCase.companyType.eq(companyType))
                .where(qTdStockCase.channelId.eq(channelId))
                .where(qTdStockCase.isClosed.eq(false))
                .where(qTdStockCase.gmtCreate.goe(openTime))
                .where(qTdStockCase.auditStatus.eq(StockCaseAuditStatusEnum.PASS.getCode()))
                .orderBy(qTdStockCase.id.desc())
                .fetch();

        List<Long> caseIdList = fetch.stream().map(TdStockCase::getId).collect(Collectors.toList());
        List<TdCaseProfit> caseProfits = jpaQueryFactory.selectFrom(qTdCaseProfit).where(qTdCaseProfit.caseId.in(caseIdList)).fetch();
        Map<Long, TdCaseProfit> caseProfitMap = caseProfits.stream().collect(Collectors.toMap(TdCaseProfit::getCaseId, Function.identity()));
        List<StrategyStockCaseResp> strategyStockCaseResps = new ArrayList<>();
        for (TdStockCase stockCase : fetch) {
            StrategyStockCaseResp build = StrategyStockCaseResp.builder()
                    .labelCode(stockCase.getLabelCode())
                    .price(stockCase.getPrice())
                    .count(stockCase.getCount())
                    .gmtCreate(stockCase.getGmtCreate())
                    .auditTime(stockCase.getAuditTime())
                    .build();
            // 创建第一天为 1 所以+1
            long days = DateUtil.betweenDay(Date.from(stockCase.getGmtCreate()), new Date(), true) + 1;
            build.setHoldingDays(days);
            TdCaseProfit tdCaseProfit = caseProfitMap.get(stockCase.getId());
            if (ObjectUtil.isNotNull(tdCaseProfit)) {
                build.setChangeRatio(tdCaseProfit.getChangeRatio());
                build.setMaximumRatio(tdCaseProfit.getMax180Ratio());
                double averageDailyReturn = NumberUtil.div(tdCaseProfit.getChangeRatio(), Double.valueOf(days));
                build.setAverageDailyReturn(averageDailyReturn);
            }
            strategyStockCaseResps.add(build);
        }
        return BaseResult.success(strategyStockCaseResps);
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancelUserStockCaseSub(Integer userId) {

        List<Long> caseIdList = jpaQueryFactory.select(qTdCaseSubInfo.caseId)
                .from(qTdCaseSubInfo)
                .where(qTdCaseSubInfo.userId.eq(userId))
                .where(qTdCaseSubInfo.enabled.eq(true))
                .fetch();
        if (ObjectUtil.isEmpty(caseIdList)) {
            return;
        }
        long execute = jpaQueryFactory.update(qTdCaseSubInfo)
                .set(qTdCaseSubInfo.enabled, false)
                .where(qTdCaseSubInfo.userId.eq(userId))
                .where(qTdCaseSubInfo.enabled.eq(true))
                .execute();
        List<TdCaseSubRecord> recordList = caseIdList.stream().map(c -> {
            return TdCaseSubRecord.builder()
                    .caseId(c)
                    .userId(userId)
                    .operatorId(userId)
                    .isSub(false)
                    .build();
        }).collect(Collectors.toList());
        caseSubRecordRepository.saveAll(recordList);
    }

    public BaseResult<List<TdStockCaseDeal>> getStrategyStockDealList(Integer companyType, Long channelId, Instant openTime, Instant endTime, Integer day) {

        TdStockCaseDeal first;
        if (ObjectUtil.isEmpty(endTime)) {
            first = jpaQueryFactory.select(qTdStockCaseDeal).from(qTdStockCaseDeal)
                    .leftJoin(qTdStockCase)
                    .on(qTdStockCase.id.eq(qTdStockCaseDeal.caseId))
                    .where(qTdStockCase.companyType.eq(companyType))
                    .where(qTdStockCase.channelId.eq(channelId))
                    .where(qTdStockCase.gmtCreate.goe(openTime))
                    .where(qTdStockCaseDeal.auditStatus.eq(StockCaseAuditStatusEnum.PASS.getCode()))
                    .orderBy(qTdStockCaseDeal.gmtCreate.desc())
                    .fetchFirst();
            if (ObjectUtil.isEmpty(first)) {
                return BaseResult.success(Collections.EMPTY_LIST);
            }
            endTime = DateUtils.getDayOfEnd(first.getGmtCreate());
        }
        Instant startTime = endTime.minus(day, ChronoUnit.DAYS).plus(1, ChronoUnit.SECONDS).isBefore(openTime) ? openTime : endTime.minus(day, ChronoUnit.DAYS).plus(1, ChronoUnit.SECONDS);

        List<TdStockCaseDeal> dealList = jpaQueryFactory.select(qTdStockCaseDeal).from(qTdStockCaseDeal)
                .leftJoin(qTdStockCase)
                .on(qTdStockCase.id.eq(qTdStockCaseDeal.caseId))
                .where(qTdStockCase.companyType.eq(companyType))
                .where(qTdStockCase.channelId.eq(channelId))
                .where(qTdStockCase.gmtCreate.gt(startTime))
                .where(qTdStockCaseDeal.gmtCreate.loe(endTime))
                .where(qTdStockCaseDeal.auditStatus.eq(StockCaseAuditStatusEnum.PASS.getCode()))
                .orderBy(qTdStockCaseDeal.gmtCreate.desc())
                .fetch();

        return BaseResult.success(dealList);
    }

    public BaseResult<List<TdStockCase>> batchStockCase(List<Long> caseIdList) {
        List<TdStockCase> fetch = jpaQueryFactory.selectFrom(qTdStockCase)
                .where(qTdStockCase.id.in(caseIdList))
                .fetch();
        return BaseResult.success(fetch);
    }

    public BaseResult<TdStockCaseDeal> getLastStockCaseDeal(Integer companyType, Long channelId, Instant openTime, Instant deadline) {
        JPAQuery<TdStockCaseDeal> query = jpaQueryFactory.select(qTdStockCaseDeal).from(qTdStockCaseDeal)
                .leftJoin(qTdStockCase)
                .on(qTdStockCase.id.eq(qTdStockCaseDeal.caseId))
                .where(qTdStockCase.companyType.eq(companyType))
                .where(qTdStockCase.channelId.eq(channelId))
                .where(qTdStockCaseDeal.auditStatus.eq(StockCaseAuditStatusEnum.PASS.getCode()));

        if (ObjectUtil.isNotEmpty(openTime)) {
            query.where(qTdStockCaseDeal.gmtCreate.goe(openTime));
        }
        if (ObjectUtil.isNotEmpty(deadline)) {
            query.where(qTdStockCaseDeal.gmtCreate.loe(deadline));
        }
        TdStockCaseDeal first = query.orderBy(qTdStockCaseDeal.gmtCreate.desc())
                .fetchFirst();
        return BaseResult.success(first);
    }


    public List<UserStockCaseSubInfoResp> getUserSubInfoList(List<Integer> userIdList) {
        JPAQuery<UserStockCaseSubInfoResp> query = jpaQueryFactory.select(Projections.bean(UserStockCaseSubInfoResp.class,
                        qTdCaseSubInfo.id.as("id"),
                        qTdCaseSubInfo.caseId.as("caseId"),
                        qTdCaseSubInfo.userId.as("userId"),
                        qTdStockCase.labelCode.as("labelCode")))
                .from(qTdCaseSubInfo)
                .leftJoin(qTdStockCase)
                .on(qTdCaseSubInfo.caseId.eq(qTdStockCase.id))
                .leftJoin(qTdStockCaseChannel)
                .on(qTdStockCase.channelId.eq(qTdStockCaseChannel.id))
                .where(qTdCaseSubInfo.enabled.eq(true))
                .where(qTdStockCaseChannel.channelType.eq(SCT_PORTFOLIO));
        if (ObjectUtil.isNotEmpty(userIdList)) {
            query.where(qTdCaseSubInfo.userId.in(userIdList));
        }
        return query.fetch();
    }

    public void userBatchCaseSub(UserBatchStockCaseSubReq req) {
        List<TdCaseSubInfo> infoList = new ArrayList<>();
        List<TdCaseSubRecord> recordList = new ArrayList<>();
        Integer operatorId = req.getOperatorId();
        List<Long> subInfoList = filterSubByUserId(req.getUserId()).getData()
                .stream().map(TdCaseSubInfo::getCaseId)
                .collect(Collectors.toList());
        List<Long> caseIdList = req.getCaseIdList();
        List<Long> addList = caseIdList.stream()
                .filter(x -> !subInfoList.contains(x))
                .collect(Collectors.toList());
        List<Long> removeList = subInfoList.stream()
                .filter(x -> !caseIdList.contains(x))
                .collect(Collectors.toList());
        addList.forEach(caseId -> {
            TdCaseSubInfo info = TdCaseSubInfo.builder()
                    .caseId(caseId)
                    .userId(req.getUserId())
                    .enabled(true)
                    .build();
            infoList.add(info);
            TdCaseSubRecord record = TdCaseSubRecord.builder()
                    .caseId(caseId)
                    .userId(req.getUserId())
                    .operatorId(operatorId)
                    .isSub(true)
                    .build();
            recordList.add(record);
        });
        removeList.forEach(caseId -> {
            TdCaseSubInfo info = TdCaseSubInfo.builder()
                    .caseId(caseId)
                    .userId(req.getUserId())
                    .enabled(false)
                    .build();
            infoList.add(info);
            TdCaseSubRecord record = TdCaseSubRecord.builder()
                    .caseId(caseId)
                    .userId(req.getUserId())
                    .operatorId(operatorId)
                    .isSub(false)
                    .build();
            recordList.add(record);
        });
        if (CollectionUtil.isNotEmpty(infoList)) {
            String batchInfoSql = SqlUtil.onDuplicateKeyUpdateSql(infoList);
            jdbcTemplate.execute(batchInfoSql);
        }

        if (CollectionUtil.isNotEmpty(recordList)) {
            String batchRecordSql = SqlUtil.onDuplicateKeyUpdateSql(recordList);
            jdbcTemplate.execute(batchRecordSql);
        }
    }

    public PageResult<List<CaseChannelResp>> findStockCaseByChannelType(Integer companyType, Integer channelType, Integer current, Integer size) {
        JPAQuery<CaseChannelResp> query = jpaQueryFactory.select(Projections.bean(CaseChannelResp.class,
                        qTdStockCase.id,
                        qTdStockCase.channelId,
                        qTdStockCase.labelCode,
                        qTdStockCaseChannel.name.as("channelName"),
                        qTdStockCaseChannel.remark.as("remark")))
                .from(qTdStockCase)
                .leftJoin(qTdStockCaseChannel).on(qTdStockCase.channelId.eq(qTdStockCaseChannel.id))
                .where(qTdStockCase.companyType.eq(companyType));

        if (ObjectUtil.isNotEmpty(channelType)) {
            query.where(qTdStockCaseChannel.channelType.eq(channelType));
        }

        NumberTemplate<Long> countTemp = Expressions.numberTemplate(Long.class, "COUNT(*)");
        Long total = query.clone().select(countTemp).fetchOne();

        query.orderBy(qTdStockCase.id.desc());

        if (ObjectUtil.isAllNotEmpty(current, size)) {
            query.offset((long) (current - 1) * size).limit(size);
        }

        return PageResult.success(query.fetch(), Pagination.of(current, size, total));
    }

    public void dzStockCaseCreate(DzStockPoolReq req) {
        DzStockPoolChannelProperties.Channel channel = dzStockPoolChannelProperties.getChannel(req.getPoolId());
        if (ObjectUtil.isEmpty(channel) || ObjectUtil.isEmpty(channel.getChannelNumber())) {
            log.error("channelProperties is null, req:{}", JsonUtils.toJson(req));
            throw new BusinessException("channelProperties is null");
        }
        String channelNumber = channel.getChannelNumber();
        TdStockCaseChannel caseChannel = jpaQueryFactory.select(qTdStockCaseChannel).from(qTdStockCaseChannel)
                .where(qTdStockCaseChannel.companyType.eq(COMPANY_TYPE))
                .where(qTdStockCaseChannel.channelType.eq(SCT_QUANTIZE))
                .where(qTdStockCaseChannel.number.eq(channelNumber))
                .fetchFirst();
        if (ObjectUtil.isNull(caseChannel)) {
            log.error("caseChannel is null, req:{}", JsonUtils.toJson(req));
            throw new BusinessException("caseChannel is null");
        }
        List<EditStockCaseReq> stockCaseReqList = req.getStockList().stream()
                .filter(Objects::nonNull)
                .map(stockInfo -> getStockCaseInfo(stockInfo, caseChannel.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
//        Map<String, TdStockChannelScoreRecord> labelCodeScoreMap = new HashMap<>();
//        Boolean scoreFlag = channel.getScoreFlag();
//        if (Boolean.TRUE.equals(scoreFlag)) {
//            // 查评分
//            Instant instant = DateUtils.getDayOfStart(Instant.now());
//            Set<String> labelCodes = stockCaseReqList.stream().map(EditStockCaseReq::getLabelCode)
//                    .filter(Objects::nonNull).collect(Collectors.toSet());
//            labelCodeScoreMap = Optional.ofNullable(stockChannelScoreRecordDao.getStockChannelScoreRecord(channelNumber, instant, labelCodes))
//                    .map(list -> list.stream().filter(Objects::nonNull).collect(Collectors.toMap(TdStockChannelScoreRecord::getLabelCode, o -> o)))
//                    .orElse(new HashMap<>());
//        }
//        List<StockCaseScoreReq.StockCaseScore> updateStockCaseScoreList = new ArrayList<>();
        // 插数据
        if (ObjectUtil.isNotEmpty(stockCaseReqList)) {
            for (EditStockCaseReq editStockCaseReq : stockCaseReqList) {
//                String labelCode = editStockCaseReq.getLabelCode();
//                if (labelCodeScoreMap.containsKey(labelCode)) {
//                    editStockCaseReq.setScore(labelCodeScoreMap.get(labelCode).getScore().doubleValue());
//                }
                TdStockCase tdStockCase = addStockCase(editStockCaseReq);
//                if (Boolean.TRUE.equals(scoreFlag) && tdStockCase != null && tdStockCase.getScore() == null) {
//                    StockCaseScoreReq.StockCaseScore caseScore = StockCaseScoreReq.StockCaseScore.builder()
//                            .id(tdStockCase.getId())
//                            .labelCode(tdStockCase.getLabelCode())
//                            .build();
//                    updateStockCaseScoreList.add(caseScore);
//                }
            }
        }
//        if (ObjectUtil.isNotEmpty(updateStockCaseScoreList)) {
//            StockCaseScoreReq scoreReq = StockCaseScoreReq.builder()
//                    .stockCaseScoreList(updateStockCaseScoreList)
//                    .poolId(req.getPoolId())
//                    .build();
//            rocketMqUtils.send(TradeServiceConst.TRADE_TOPIC, TradeServiceConst.MqTagType.TAG_UPDATE_STOCK_CASE_SCORE, scoreReq);
//        }
    }

    private EditStockCaseReq getStockCaseInfo(DzStockPoolReq.StockInfo stockInfo, Long channelId) {
        String label = StockUtils.stockLabelFormat(stockInfo.getCode());
        if (label.contains(".")) {
            return EditStockCaseReq.builder()
                    .companyType(COMPANY_TYPE)
                    .channelId(channelId)
                    .labelCode(label)
                    .category("决策家系统")
                    .priceUp(stockInfo.getPrice())
                    .priceDown(stockInfo.getPrice())
                    .count(10)
                    .reason("指标运算")
                    .risk("市场波动风险，行业政策变化风险，业绩不及预期风险")
                    .tips("任何时候，都要学会敬畏市场，不重仓单只个股，不随意加仓；学会控制情绪，建立好交易规则。")
                    .appPush(true)
                    .sourceType(StockCaseSourceType.SCST_ZBYS)
                    .dzStockId(stockInfo.getId())
                    .score(ObjectUtil.isEmpty(stockInfo.getScore()) ? null : Double.valueOf(stockInfo.getScore()))
                    .build();
        }
        log.info("label: {} not contains . ", label);
        return null;
    }

    @Transactional
    public void updateStockCaseScore(Long id, double score) {
        jpaQueryFactory.update(qTdStockCase)
                .set(qTdStockCase.score, score)
                .where(qTdStockCase.id.eq(id))
                .execute();
    }

    public BaseResult<SubStockCaseCountResp> getCaseSubLastCount(Integer customerId, Integer companyType) {

        BaseResult<VipSubscription> result = orderClient.getUserVipSubscription(customerId, 2);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (ObjectUtil.isEmpty(result.getData())) {
            throw new BusinessException("用户未开通该服务");
        }

        ChatRoomConfigResp chatRoomConfigResp = orderClient.getChatRoomConfig().orElseThrow(() -> new BusinessException("获取聊天室配置错误"));

        SubStockCaseCountResp resp = getMySubStockCaseCount(customerId, true).orElseThrow();
        resp.setSubLastCount(Math.max(chatRoomConfigResp.getStockCount() - resp.getSubCount(),0));
        return BaseResult.success(resp);
    }

    public List<TdStockCase> getStockCaseByIdList(List<Long> idList) {
        if (ObjectUtil.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return jpaQueryFactory.selectFrom(qTdStockCase).where(qTdStockCase.id.in(idList)).fetch();
    }

    public void refreshCaseProfit(Long caseId) {
        redisUtil.addToSet(RedisKeyConstant.CASE_PROFIT_REFRESH_SET, String.valueOf(caseId));
    }

    public List<TdStockCase> getProfitRefreshCaseList() {
        Long count = redisUtil.getSetCount(RedisKeyConstant.CASE_PROFIT_REFRESH_SET);
        List<String> caseIdList = redisUtil.popFromSet(RedisKeyConstant.CASE_PROFIT_REFRESH_SET, count);
        if (CollUtil.isEmpty(caseIdList)) {
            return Collections.emptyList();
        }
        return getStockCaseByIdList(caseIdList.stream().map(Long::valueOf).collect(Collectors.toList()));

    }
}
