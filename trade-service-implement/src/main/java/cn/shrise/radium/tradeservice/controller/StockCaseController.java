package cn.shrise.radium.tradeservice.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.tradeservice.constant.StockCaseAuditStatusEnum;
import cn.shrise.radium.tradeservice.dto.CustomerStockCaseDto;
import cn.shrise.radium.tradeservice.dto.StockCaseDealDto;
import cn.shrise.radium.tradeservice.dto.StockCaseDto;
import cn.shrise.radium.tradeservice.entity.*;
import cn.shrise.radium.tradeservice.req.*;
import cn.shrise.radium.tradeservice.resp.*;
import cn.shrise.radium.tradeservice.service.StockCaseRecordService;
import cn.shrise.radium.tradeservice.service.StockCaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

import static cn.shrise.radium.tradeservice.constant.StockCaseDealTypeEnum.DT_FOLLOW;

/**
 * <AUTHOR>
 * @version 1.0
 * desc: 案例股票池
 */
@Api
@RestController
@RequestMapping("stock/case")
@RequiredArgsConstructor
public class StockCaseController {

    private final StockCaseService stockCaseService;
    private final StockCaseRecordService stockCaseRecordService;
    private final RocketMqUtils rocketMqUtils;

    @GetMapping
    @ApiOperation("案例股票池-案例列表")
    public PageResult<List<StockCaseDto>> getStockCaseDetailList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("频道") Long channelId,
            @RequestParam(required = false) @ApiParam("频道Ids") Collection<Long> channelIdList,
            @RequestParam(required = false) @ApiParam("筛选") String searchText,
            @RequestParam(required = false) @ApiParam("时间类型") Integer timeType,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) @ApiParam("正序排列") Boolean isAsc,
            @RequestParam(required = false) @ApiParam("是否清仓") Boolean isClosed,
            @RequestParam(required = false) @ApiParam("是否置顶") Boolean isTop,
            @RequestParam(required = false) @ApiParam("是否置顶排序") Boolean isTopSort,
            @RequestParam(required = false) @ApiParam("案例来源") Integer sourceType,
            @RequestParam(required = false) @ApiParam("涨幅类型") Integer ratioType,
            @RequestParam(required = false) @ApiParam("审核状态") List<Integer> auditStatusList,
            @RequestParam(required = false) @ApiParam("顾问老师id") Integer analystId,
            @RequestParam(required = false) @ApiParam("是否调研票") Boolean isResearch,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size) {
        return stockCaseService.findCaseDetailByFilter(companyType, channelId, channelIdList, searchText, timeType, startTime, endTime, isAsc, isClosed, isTop, isTopSort, sourceType, ratioType, auditStatusList, analystId, isResearch, current, size);
    }

    @PostMapping
    @ApiOperation("新增案例")
    public BaseResult<String> addStockCase(@Validated @RequestBody EditStockCaseReq req) {
        stockCaseService.addStockCase(req);
        return BaseResult.success();
    }

    @PostMapping("deal")
    @ApiOperation("新增案例操作")
    public BaseResult<TdStockCaseDeal> addStockCaseDeal(@Validated @RequestBody EditStockCaseDealReq req) {
        TdStockCaseDeal info = TdStockCaseDeal.builder()
                .caseId(req.getCaseId())
                .companyType(req.getCompanyType())
                .endCount(req.getEndCount())
                .tips(req.getTips())
                .price(req.getPrice())
                .priceUp(req.getPriceUp())
                .priceDown(req.getPriceDown())
                .reason(req.getReason())
                .isEnabled(true)
                .risk(req.getRisk())
                .dealType(req.getDealType())
                .operatorId(req.getOperatorId())
                .takeProfitUp(req.getTakeProfitUp())
                .takeProfitDown(req.getTakeProfitDown())
                .stopLossUp(req.getStopLossUp())
                .stopLossDown(req.getStopLossDown())
                .appPush(req.getAppPush())
                .auditStatus(StockCaseAuditStatusEnum.WAITING.getCode())
                //.isHide(req.getIsHide())
                .build();
        if (ObjectUtil.equals(req.getDealType(), DT_FOLLOW.getValue())) {
            info.setIsHide(req.getIsHide());
        }
        info = stockCaseService.addStockCaseDeal(info);
        return BaseResult.success(info);
    }

    @PostMapping("deal/audit")
    @ApiOperation("审核案例操作")
    public BaseResult<Void> auditStockCaseDeal(
            @RequestParam @ApiParam("操作id") Long dealId,
            @RequestParam @ApiParam("审核人") Integer auditorId,
            @RequestParam @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("拒绝原因") String rejectReason) {
        stockCaseService.auditStockCaseDeal(dealId, auditorId, auditStatus, rejectReason);
        return BaseResult.successful();
    }

    @PostMapping("audit")
    @ApiOperation("审核案例")
    public BaseResult<Void> auditStockCase(
            @RequestParam @ApiParam("案例id") Long caseId,
            @RequestParam @ApiParam("审核人") Integer auditorId,
            @RequestParam @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("拒绝原因") String rejectReason) {
        stockCaseService.updateAudit(caseId, auditorId, auditStatus, rejectReason);
        return BaseResult.successful();
    }

    @GetMapping("deal_history")
    @ApiOperation("案例股票池-案例操作历史")
    public PageResult<List<StockCaseDealDto>> getStockCaseDealList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("频道") Long channelId,
            @RequestParam(required = false) @ApiParam("案例Id") Long caseId,
            @RequestParam(required = false) @ApiParam("股票代码") String labelCode,
            @RequestParam(required = false) @ApiParam("是否已审核") Boolean isAudit,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("投资顾问") Integer analystId,
            @RequestParam(required = false) @ApiParam("排序类型") Integer orderType,
            @RequestParam(required = false) @ApiParam("是否升序") Boolean isAsc,
            @RequestParam(required = false) @ApiParam("是否调研票") Boolean isResearch,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size) {
        return stockCaseService.findAllCaseDealByFilter(companyType, channelId, labelCode, caseId, isAudit, auditStatus,
                analystId, orderType, isAsc, isResearch, current, size);
    }

    @GetMapping("news")
    @ApiOperation("案例最新动态")
    public PageResult<List<StockCaseDealDto>> getStockCaseNewsList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("文章栏目id") Integer seriesId,
            @RequestParam(required = false) @ApiParam("查询的开始时间") Instant startTime,
            @RequestParam @ApiParam("用户服务开通时间") Instant serviceTime,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size) {
        Pageable pageable = PageRequest.of(current - 1, size);
        Page<StockCaseDealDto> stockCaseNews = stockCaseService.getStockCaseNews(companyType, seriesId, startTime, serviceTime, pageable);
        return PageResult.success(stockCaseNews);
    }

    @GetMapping("chat/list")
    @ApiOperation("聊天室案例列表")
    public PageResult<List<StockCaseDealDto>> getStockCaseList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("案例频道ID") Long channelId,
            @RequestParam(required = false) @ApiParam("筛选开仓") Boolean filterOpen,
            @RequestParam(required = false) @ApiParam("筛选清仓") Boolean filterClose,
            @RequestParam(required = false) @ApiParam("筛选置顶") Boolean filterTop,
            @RequestParam(required = false) @ApiParam("筛选开通") Boolean checkOpen,
            @RequestParam(required = false) @ApiParam("开通类型, 1: 案例, 2: 操作") Integer checkOpenType,
            @RequestParam(required = false) @ApiParam("老师id") Integer analystId,
            @RequestParam(required = false) @ApiParam("案例开始时间") Instant caseStartTime,
            @RequestParam(required = false) @ApiParam("案例结束时间") Instant caseEndTime,
            @RequestParam(required = false) @ApiParam("案例操作开始时间") Instant dealStartTime,
            @RequestParam(required = false) @ApiParam("案例操作结束时间") Instant dealEndTime,
            @RequestParam(required = false) @ApiParam("服务开通时间") Instant openTime,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size) {
        Pageable pageable = PageRequest.of(current - 1, size);
        Page<StockCaseDealDto> stockCaseNews = stockCaseService.getStockCaseList(companyType, channelId, analystId, filterOpen,
                filterClose, filterTop, checkOpen, checkOpenType, caseStartTime, caseEndTime, dealStartTime, dealEndTime, openTime, pageable);
        return PageResult.success(stockCaseNews);
    }

    @GetMapping("ids")
    @ApiOperation("根据ids查询案例信息")
    public BaseResult<List<StockCaseInfoResp>> getAllByIds(
            @RequestParam @ApiParam("案例操作ids") List<Long> dealIds) {
        List<StockCaseInfoResp> respList = stockCaseService.getAllByIds(dealIds);
        return BaseResult.success(respList);
    }

    @GetMapping("editCaseTop")
    @ApiOperation("编辑案例是否置顶")
    public BaseResult<Void> editCaseTop(
            @RequestParam @ApiParam("案例id") Long caseId,
            @RequestParam @ApiParam("是否置顶") Boolean isTop) {
        stockCaseService.editCaseTop(caseId, isTop);
        return BaseResult.successful();
    }

    @GetMapping("editCaseSource")
    @ApiOperation("编辑案例来源")
    public BaseResult<Void> editCaseSource(
            @RequestParam @ApiParam("案例id") Long caseId,
            @RequestParam @ApiParam("来源类型") Integer sourceType) {
        stockCaseService.editCaseSource(caseId, sourceType);
        return BaseResult.successful();
    }

    @GetMapping("getCaseProfit/caseId")
    @ApiOperation("通过案例id获取案例收益")
    public BaseResult<TdCaseProfit> findCaseProfitByCaseId(
            @RequestParam @ApiParam("案例id") Long caseId) {
        TdCaseProfit caseProfit = stockCaseService.findCaseProfitByCaseId(caseId);
        return BaseResult.success(caseProfit);
    }

    @GetMapping("lastCase")
    @ApiOperation("获取最后一条案例")
    public BaseResult<TdStockCase> getLastStockCaseDetail(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("频道") Long channelId,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus) {
        return stockCaseService.getLastStockCaseDetail(companyType, channelId, auditStatus);
    }

    @GetMapping("signal")
    @ApiOperation("获取案例股票信号列表")
    public BaseResult<List<TdStockCase>> getStockCaseSignalList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("股票代码") String labelCode,
            @RequestParam(required = false) @ApiParam("频道") List<Long> channelIdList,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus) {
        return stockCaseService.getStockCaseSignalList(companyType, channelIdList, labelCode, auditStatus);
    }

    @GetMapping("lastCaseDeal")
    @ApiOperation("获取最后一条案例操作")
    public BaseResult<TdStockCaseDeal> getLastStockCaseDeal(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("频道") Long channelId,
            @RequestParam(required = false) @ApiParam("开通时间") Instant openTime,
            @RequestParam(required = false) @ApiParam("截止时间") Instant deadline) {
        return stockCaseService.getLastStockCaseDeal(companyType, channelId, openTime, deadline);
    }

    @GetMapping("lastCase/processDays")
    @ApiOperation("根据入池天数获取案例")
    public BaseResult<List<TdStockCase>> findCaseByProcessDays(
            @RequestParam(required = false) @ApiParam("是否关闭") Boolean isClosed,
            @RequestParam(required = false) @ApiParam("入池开始天数") Integer startProcessDays,
            @RequestParam @ApiParam("入池天数") Integer processDays
    ) {
        List<TdStockCase> stockCaseList = stockCaseService.findCaseByProcessDays(isClosed, processDays, startProcessDays);
        return BaseResult.success(stockCaseList);
    }

    @GetMapping("last_days")
    @ApiOperation("获取最近几天案例")
    public BaseResult<List<TdStockCase>> findCaseByCreateTime(
            @RequestParam @ApiParam("最近几天") Integer days
    ) {
        List<TdStockCase> stockCaseList = stockCaseService.findCaseByCreateTime(days);
        return BaseResult.success(stockCaseList);
    }

    @GetMapping("info")
    @ApiOperation("案例股票池-案例详情")
    public BaseResult<StockCaseDto> getStockCaseDetailInfo(@RequestParam @ApiParam("案例id") Long caseId) {
        return stockCaseService.getStockCaseDetailInfo(caseId);
    }

    @GetMapping("sub/list")
    @ApiOperation("案例股票池-订阅人数")
    public PageResult<List<TdCaseSubInfo>> getStockCaseSubInfoList(@RequestParam @ApiParam("案例id") Long caseId,
                                                                   @RequestParam(required = false) @ApiParam("搜索内容") String content,
                                                                   @RequestParam(required = false) Integer current,
                                                                   @RequestParam(required = false) Integer size) {
        return stockCaseService.getStockCaseSubInfoList(caseId, content, current, size);
    }

    @GetMapping("sub/recordList")
    @ApiOperation("案例股票池-订阅记录")
    public PageResult<List<TdCaseSubRecord>> getStockCaseSubRecordList(@RequestParam @ApiParam("案例id") Long caseId,
                                                                       @RequestParam(required = false) @ApiParam("搜索内容") String content,
                                                                       @RequestParam(required = false) Integer current,
                                                                       @RequestParam(required = false) Integer size) {
        return stockCaseService.getStockCaseSubRecordList(caseId, content, current, size);
    }

    @PostMapping("sub/editSubInfo")
    @ApiOperation("案例股票池-关注/取关")
    public BaseResult<String> editStockCaseSubInfo(@Validated @RequestBody EditStockCaseSubInfoReq req) {
        stockCaseService.editStockCaseSubInfo(req);
        return BaseResult.success();
    }

    @PostMapping("sub/batchEdit")
    @ApiOperation("案例股票池-批量关注")
    public BaseResult<String> batchEditCaseSub(@Validated @RequestBody BatchEditStockCaseSubInfoReq req) {
        stockCaseService.batchEditCaseSub(req);
        return BaseResult.success();
    }

    @PostMapping("sub/filterUser")
    @ApiOperation("案例股票池-获取订阅用户")
    public BaseResult<List<Integer>> filterStockCaseSubInfoUser(@RequestParam @ApiParam("案例id") Long caseId,
                                                                @RequestBody @Valid @ApiParam("用户id") BatchReq<Integer> req) {
        return stockCaseService.filterStockCaseSubInfoUser(caseId, req.getValues());
    }

    @PostMapping("sub/cancelUser")
    @ApiOperation("案例股票池-注销用户取关")
    public BaseResult<String> cancelUserStockCaseSub(@RequestParam @ApiParam("用户id") Integer userId) {
        stockCaseService.cancelUserStockCaseSub(userId);
        return BaseResult.success();
    }

    @GetMapping("mySubList")
    @ApiOperation("案例股票池-我的关注")
    public PageResult<List<StockCaseDealDto>> getMySubStockCaseList(@RequestParam @ApiParam("公司类型") Integer companyType,
                                                                    @RequestParam @ApiParam("用户id") Integer userId,
                                                                    @RequestParam @ApiParam("开通时间") Instant openTime,
                                                                    @RequestParam(required = false) Integer current,
                                                                    @RequestParam(required = false) Integer size) {
        return stockCaseService.getMySubStockCaseList(companyType, userId, openTime, current, size);
    }

    @GetMapping("customerSubList")
    @ApiOperation("企微后台-客户关注案例")
    public PageResult<List<CustomerStockCaseDto>> getCustomerSubStockCaseList(@RequestParam @ApiParam("公司类型") Integer companyType,
                                                                              @RequestParam @ApiParam("用户id") Integer userId,
                                                                              @RequestParam(required = false) Integer current,
                                                                              @RequestParam(required = false) Integer size) {
        return stockCaseService.getCustomerSubStockCaseList(companyType, userId, current, size);
    }

    @GetMapping("sub/filterSubByUserId")
    @ApiOperation("案例股票池-获取用户已关注列表")
    public BaseResult<List<TdCaseSubInfo>> filterSubByUserId(@RequestParam @ApiParam("用户id") Integer userId) {
        return stockCaseService.filterSubByUserId(userId);
    }

    @GetMapping("sub/count")
    @ApiOperation("案例股票池-关注案例个数")
    public BaseResult<SubStockCaseCountResp> getMySubStockCaseCount(@RequestParam @ApiParam("用户id") Integer userId,
                                                                    @RequestParam(required = false) @ApiParam("是否本人关注") Boolean isSelf) {
        return stockCaseService.getMySubStockCaseCount(userId, isSelf);
    }

    @GetMapping("filterStockCaseByChannel")
    @ApiOperation("根据频道分页获取案例")
    public PageResult<List<TdStockCase>> filterStockCaseByChannel(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("频道") Long channelId,
            @RequestParam(required = false) @ApiParam("搜索内容") String content,
            @RequestParam(required = false) @ApiParam("案例开始时间") Long caseStartTime,
            @RequestParam(required = false) @ApiParam("案例结束时间") Long caseEndTime,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size) {
        return stockCaseService.filterStockCaseByChannel(companyType, channelId, content, caseStartTime, caseEndTime, current, size);
    }

    @GetMapping("strategy/detail")
    @ApiOperation("策略组概览明细")
    public BaseResult<List<StrategyStockCaseResp>> getStrategyStockCaseRespList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("策略组频道ID") Long channelId
    ) {
        return stockCaseService.getStrategyStockCaseRespList(companyType, channelId);
    }

    @GetMapping("strategy/detailWithoutQuote")
    @ApiOperation("策略组概览明细(去掉行情调用)")
    public BaseResult<List<StrategyStockCaseResp>> getStrategyStockCaseWithoutQuoteRespList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("策略组频道ID") Long channelId,
            @RequestParam @ApiParam("开通时间") Instant openTime
    ) {
        return stockCaseService.getStrategyStockCaseWithoutQuoteRespList(companyType, channelId, openTime);
    }

    @GetMapping("strategy/profit/detail")
    @ApiOperation("策略组概览胜率详情")
    public PageResult<List<StrategyStockCaseResp>> getStrategyProfitDetailList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("策略组频道ID") Long channelId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return stockCaseService.getStrategyProfitDetailList(companyType, channelId, current, size);
    }

    @GetMapping("strategy/profit")
    @ApiOperation("根据频道获取收益信息")
    public List<StrategyStockCaseResp> getStrategyStockCaseRespList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("策略组频道ID") Collection<Long> channelIds
    ) {
        return stockCaseService.getStrategyStockCaseRespList(companyType, channelIds);
    }

    @PostMapping("strategy/profit")
    @ApiOperation("批量更新收益胜率")
    public BaseResult<String> batchUpdateProfitRatio(@RequestBody @Valid BatchReq<TdStrategyChannelExt> req) {
        stockCaseService.batchUpdateProfitRatio(req.getValues());
        return BaseResult.success();
    }

    @GetMapping("strategy/stockList")
    @ApiOperation("策略组持仓列表")
    public BaseResult<List<StrategyStockCaseResp>> getStrategyStock(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("策略组频道ID") Long channelId,
            @RequestParam @ApiParam("开通时间") Instant openTime
    ) {
        return stockCaseService.getStrategyStock(companyType, channelId, openTime);
    }

    @GetMapping("user/case_list")
    @ApiOperation("获取用户订阅案例列表")
    public BaseResult<List<UserStockCaseSubInfoResp>> getUserSubInfoList(
            @RequestParam @ApiParam("用户列表") List<Integer> userIdList) {
        List<UserStockCaseSubInfoResp> stockCaseSubInfoList = stockCaseService.getUserSubInfoList(userIdList);
        return BaseResult.success(stockCaseSubInfoList);
    }

    @PostMapping("user/batchSub")
    @ApiOperation("订阅案例-用户批量关注")
    public BaseResult<String> userBatchCaseSub(@RequestBody @Validated UserBatchStockCaseSubReq req) {
        stockCaseService.userBatchCaseSub(req);
        return BaseResult.success();
    }

    @GetMapping("case_list/channel_type")
    @ApiOperation("根据频道类型获取案例列表")
    public PageResult<List<CaseChannelResp>> findStockCaseByChannelType(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("频道类型") Integer channelType,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("数量") Integer size) {
        return stockCaseService.findStockCaseByChannelType(companyType, channelType, current, size);
    }


    @GetMapping("strategy/stockDealList")
    @ApiOperation("策略组持仓操作列表")
    public BaseResult<List<TdStockCaseDeal>> getStrategyStockDealList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("策略组频道ID") Long channelId,
            @RequestParam @ApiParam("开通时间") Instant openTime,
            @RequestParam(required = false) @ApiParam("结束时间") Instant endTime,
            @RequestParam(required = false, defaultValue = "5") @ApiParam("获取天数") Integer day
    ) {
        return stockCaseService.getStrategyStockDealList(companyType, channelId, openTime, endTime, day);
    }

    @GetMapping("batch/stockCase")
    @ApiOperation("批量获取案例股票")
    public BaseResult<List<TdStockCase>> batchStockCase(@RequestParam List<Long> caseIdList) {
        return stockCaseService.batchStockCase(caseIdList);
    }

    @PostMapping("dzStockCaseCreate")
    @ApiOperation("点证入选股票池")
    public BaseResult<Void> dzStockCaseCreate(@Validated @RequestBody DzStockPoolReq req) {
        stockCaseService.dzStockCaseCreate(req);
        return BaseResult.successful();
    }

    @GetMapping("record/list")
    @ApiOperation("获取案例操作记录列表")
    public PageResult<List<TdStockCaseRecord>> getStockCaseRecordList(
            @RequestParam @ApiParam("案例id") Long caseId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "10") @ApiParam("每页条数") Integer size) {
        return stockCaseRecordService.getStockCaseRecordList(caseId, current, size);
    }

    @PostMapping("refresh")
    @ApiOperation("后台手动筛选case收益")
    BaseResult<Void> refreshCaseProfit(@RequestParam Long caseId) {
        stockCaseService.refreshCaseProfit(caseId);
        return BaseResult.successful();
    }

    @GetMapping("profit-refresh-list")
    BaseResult<List<TdStockCase>> getProfitRefreshCaseList() {
        return BaseResult.success(stockCaseService.getProfitRefreshCaseList());
    }

}
