package cn.shrise.radium.tradeservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.tradeservice.entity.TdRecommendStockNotice;
import cn.shrise.radium.tradeservice.req.EditRecommendStockNoticeReq;
import cn.shrise.radium.tradeservice.service.RecommendStockNoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Api
@RestController
@RequestMapping("recommend/stock/notice")
@RequiredArgsConstructor
public class RecommendStockNoticeController {

    private final RecommendStockNoticeService stockNoticeService;

    @GetMapping
    @ApiOperation("A股情报-公告管理")
    public PageResult<List<TdRecommendStockNotice>> getRecommendStockNoticeList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("old,new") String level,
            @RequestParam(required = false) @ApiParam("频道") Integer typeId,
            @RequestParam(required = false) @ApiParam("案例频道number") String channelNumber,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean isEnabled,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size) {
        return stockNoticeService.getRecommendStockNoticeList(level, companyType, typeId, channelNumber, isEnabled, current, size);
    }

    @GetMapping("case")
    @ApiOperation("案例公告管理")
    public PageResult<List<TdRecommendStockNotice>> getCaseChannelNoticeList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("案例频道number") List<String> channelNumbers,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean isEnabled,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size) {
        return stockNoticeService.getRecommendStockNoticeList(companyType, channelNumbers, isEnabled, current, size);
    }

    @PostMapping("create")
    @ApiOperation("A股情报-公告管理-创建")
    public BaseResult<String> createRecommendStockNotice(@Validated @RequestBody EditRecommendStockNoticeReq req) {
        stockNoticeService.createRecommendStockNotice(req);
        return BaseResult.success();
    }

    @PostMapping("update")
    @ApiOperation("A股情报-公告管理-编辑")
    public BaseResult<String> updateRecommendStockNotice(@Validated @RequestBody EditRecommendStockNoticeReq req) {
        stockNoticeService.updateRecommendStockNotice(req);
        return BaseResult.success();
    }

    @DeleteMapping("{noticeId}")
    @ApiOperation("A股情报-公告管理-删除")
    public BaseResult<Boolean> deleteRecommendStockNotice(@PathVariable @ApiParam("公告Id") Integer noticeId) {
        Boolean aBoolean = stockNoticeService.deleteRecommendStockNotice(noticeId);
        return BaseResult.success(aBoolean);
    }
}
