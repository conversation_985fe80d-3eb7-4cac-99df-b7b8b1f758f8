package cn.shrise.radium.tradeservice.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@Slf4j
@Component
public class StockHelper {
    private final static String QT_WEB_SERVER_URL = "https://q-api.shrise.cn";
    private String token;
    private Instant expireTime;

    private String getToken() {
        if (this.expireTime == null || this.expireTime.isBefore(Instant.now())) {
            this.updateToken();
        }
        return this.token;
    }

    private void updateToken() {
        String url = QT_WEB_SERVER_URL + "/auth/update_session.json";
        Map<String, Object> body = new HashMap<>();
        body.put("dz-web-sess", this.token);
        String response = HttpUtil.post(url, body);
        log.info(response);
        JSONObject jsonRes = JSONUtil.parseObj(response);
        if (jsonRes.getInt("errorCode") == 1) {
            this.token = jsonRes.getStr("tokenId");
            this.expireTime = Instant.now().plusSeconds(110 * 60);
        }
    }

    public JSONArray reqStockList(String searchContent, Collection<String> labelList, Integer pageIndex, Integer pageSize) {
        String url = QT_WEB_SERVER_URL + "/quote/stock_list.json";
        Map<String, Object> body = new HashMap<>();
        body.put("dz-web-sess", getToken());
        if (ObjectUtil.isAllNotEmpty(pageIndex, pageSize)) {
            body.put("page_index", pageIndex);
            body.put("page_size", pageSize);
        }
        if (ObjectUtil.isNotEmpty(searchContent)) {
            body.put("search_content", searchContent);
        }
        if (ObjectUtil.isNotEmpty(labelList)) {
            body.put("label_list", CollectionUtil.join(labelList, ","));
        }
        String response = HttpUtil.post(url, body);
        log.info(response);
        JSONObject jsonRes = JSONUtil.parseObj(response);
        if (jsonRes.getInt("errorCode") == 1) {
            return JSONUtil.parseArray(jsonRes.get("infoList"));
        } else {
            throw new RuntimeException("获取股票信息失败");
        }
    }

    public JSONArray reqDynamicQuote(Collection<String> labelList) {
        String url = QT_WEB_SERVER_URL + "/quote/dynamic_info.json";
        Map<String, Object> body = new HashMap<>();
        body.put("dz-web-sess", getToken());
        body.put("label_list", CollectionUtil.join(labelList, ","));
        String response = HttpUtil.post(url, body);
        log.info(response);
        JSONObject jsonRes = JSONUtil.parseObj(response);
        if (jsonRes.getInt("errorCode") == 1) {
            return JSONUtil.parseArray(jsonRes.get("infoList"));
        } else {
            throw new RuntimeException("获取行情失败");
        }
    }


    public static void main(String[] args) {
        StockHelper helper = new StockHelper();
        List<String> labels = Collections.singletonList("000001.SZ");
//        helper.updateToken();
        JSONArray objects = helper.reqDynamicQuote(labels);
        String name = objects.getJSONObject(0).getStr("name");
        System.out.println(helper.getToken());
    }
}
