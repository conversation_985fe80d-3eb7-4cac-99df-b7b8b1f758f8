package cn.shrise.radium.tradeservice.controller;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.tradeservice.entity.TdPortfolioAnalystRelation;
import cn.shrise.radium.tradeservice.service.PortfolioAnalystService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Api
@RestController
@RequestMapping("stock/portfolio/analyst")
@RequiredArgsConstructor
public class PortfolioAnalystController {

    private final PortfolioAnalystService portfolioAnalystService;

    @GetMapping("relation")
    @ApiOperation("获取投资组合下的分析师列表")
    public BaseResult<List<TdPortfolioAnalystRelation>> getPortfolioAnalystRelationList(@RequestParam Integer portfolioId) {
        List<TdPortfolioAnalystRelation> relationList = portfolioAnalystService.getPortfolioAnalystRelationList(portfolioId);
        return BaseResult.success(relationList);
    }

    @PostMapping("relation/batch")
    @ApiOperation("批量获取投资组合下的分析师列表")
    public BaseResult<List<TdPortfolioAnalystRelation>> getPortfolioAnalystRelationList(@RequestBody @Valid BatchReq<Integer> req) {
        List<TdPortfolioAnalystRelation> relationList = portfolioAnalystService.getPortfolioAnalystRelationList(req.getValues());
        return BaseResult.success(relationList);
    }
}
