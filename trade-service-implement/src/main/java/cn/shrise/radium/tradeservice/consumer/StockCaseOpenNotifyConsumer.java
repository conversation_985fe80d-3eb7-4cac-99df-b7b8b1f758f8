package cn.shrise.radium.tradeservice.consumer;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.annotation.OnsConsumer;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.constant.RocketMQConstant;
import cn.shrise.radium.common.properties.ServerProperties;
import cn.shrise.radium.common.util.RocketMqUtils;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.ImChatRoom;
import cn.shrise.radium.notificationservice.constant.PriorityConstant;
import cn.shrise.radium.notificationservice.constant.ProductTypeEnum;
import cn.shrise.radium.notificationservice.constant.PushMQConstant;
import cn.shrise.radium.notificationservice.constant.PushMsgTypeEnum;
import cn.shrise.radium.notificationservice.req.CallPushProcessReq;
import cn.shrise.radium.notificationservice.req.WxPushReq;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.quoteservice.QuoteClient;
import cn.shrise.radium.quoteservice.resp.StockDetailResp;
import cn.shrise.radium.tradeservice.constant.PortfolioSendConst;
import cn.shrise.radium.tradeservice.constant.TradeServiceConst;
import cn.shrise.radium.tradeservice.entity.TdStockCase;
import cn.shrise.radium.tradeservice.entity.TdStockCaseChannel;
import cn.shrise.radium.tradeservice.entity.TdStockCaseDeal;
import cn.shrise.radium.tradeservice.properties.PortfolioTemplateProperties;
import cn.shrise.radium.tradeservice.resp.PushMsgContent;
import cn.shrise.radium.tradeservice.resp.StockCaseNotifyResp;
import cn.shrise.radium.tradeservice.resp.SymbolDetailResponse;
import cn.shrise.radium.tradeservice.service.DzDynamicInfoService;
import cn.shrise.radium.tradeservice.service.MyStockService;
import cn.shrise.radium.tradeservice.util.StockCaseNotifyUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@OnsConsumer(topic = TradeServiceConst.TRADE_TOPIC, consumerGroup = TradeServiceConst.MqGroupType.GID_STOCK_CASE_OPEN_NOTIFY,
        selectorType = ExpressionType.TAG, selectorExpression = TradeServiceConst.MqTagType.STOCK_CASE_OPEN_NOTIFY,
        maxReconsumeTimes = "3")
public class StockCaseOpenNotifyConsumer implements MessageListener {

    private final StockCaseNotifyUtil stockCaseNotifyUtil;
    private final PortfolioTemplateProperties templateProperties;
    private final OrderClient orderClient;
    private final ImClient imClient;
    private final QuoteClient quoteClient;

    private final RocketMqUtils rocketMqUtils;
    private final DzDynamicInfoService dzDynamicInfoService;

    @Override
    public Action consume(Message message, ConsumeContext consumeContext) {
        StockCaseNotifyResp resp = JSON.parseObject(new String(message.getBody()), StockCaseNotifyResp.class);
        TdStockCase stockCase = resp.getDto().getCaseInfo() != null ? resp.getDto().getCaseInfo() : new TdStockCase();
        TdStockCaseDeal stockCaseDeal = resp.getDto().getDealInfo() != null ? resp.getDto().getDealInfo() : new TdStockCaseDeal();
        TdStockCaseChannel stockCaseChannel = resp.getDto().getChannelInfo() != null ? resp.getDto().getChannelInfo() : new TdStockCaseChannel();
        log.info("stock_case_open_notify, stockCaseId:{}, channelName{}", stockCase.getId(), stockCaseChannel.getName());
        // 基础属性
        Integer companyType = stockCase.getCompanyType();
        Integer pushMsgType = PushMsgTypeEnum.PMT_Open.getCode();
        Integer productType = ProductTypeEnum.PT_WebAdmin.getCode();
        Integer priority = PriorityConstant.PR_Open;
        if (ObjectUtil.isNotEmpty(stockCaseDeal.getId())) {
            String eventName = stockCaseDeal.getId().toString();
            ImChatRoom chatRoom = imClient.getChatRoomByCaseChannel(stockCaseChannel.getId()).orElse(null);
            if (ObjectUtil.isEmpty(chatRoom)) {
                return Action.CommitMessage;
            }
            if (ObjectUtil.isEmpty(chatRoom.getIsCasePush()) || !chatRoom.getIsCasePush()) {
                // 未开启推送
                return Action.CommitMessage;
            }
            BaseResult<Set<String>> result = orderClient.getChatVipPackageNumber(companyType, chatRoom.getNumber());
            if (result.getData() == null || result.getData().size() == 0) {
                return Action.CommitMessage;
            }
            Set<String> vipPackageSet = result.getData();

//            StockDetailResp stockDetailResp = quoteClient.getStockInfo(stockCase.getLabelCode()).orElse(null);
            List<SymbolDetailResponse.Symbol> stockDetailResp = dzDynamicInfoService.getSymbolDetail(stockCase.getLabelCode());
//            String stockName = stockDetailResp == null ? null : stockDetailResp.getStockName();
            String stockName = ObjectUtil.isEmpty(stockDetailResp) ? null : stockDetailResp.get(0).getName();
            log.info("stock_case_open_notify, vipPackage{}, channelId{}, channelName{},stockName{}", vipPackageSet, stockCaseChannel.getChannelType(), stockCaseChannel.getName(), stockName);

            // 获取模板消息配置
            PortfolioTemplateProperties.PortfolioTempInfo openTempInfo = templateProperties.getTemplates().getOrDefault(PortfolioSendConst.PORTFOLIO_BUY_TEMP, new PortfolioTemplateProperties.PortfolioTempInfo());
            Map<Integer, String> portfolioOpenMap = openTempInfo.getTemplateId();
            Set<Integer> openAccountTypes = portfolioOpenMap.keySet();
            Set<Integer> accountTypes = new HashSet<>(openAccountTypes);
            List<PortfolioTemplateProperties.TemplateProperties> openTempData = openTempInfo.getTemplateList();
            Map<Integer, String> tempType = templateProperties.getType();

            // 模板消息
            for (Integer accountType : accountTypes) {
                log.info("组合发送模板消息,id:{}, account_type: {}", stockCase.getId(), accountType);
                // 生成模板消息内容
                WxMpTemplateMessage tempMsg = stockCaseNotifyUtil.generateStockCaseOpenTemplate(accountType, companyType, chatRoom, stockCase.getId(), stockCaseChannel.getId(),
                        stockName, openTempData, portfolioOpenMap, tempType);
                // 模板消息
                Map<String, PushMsgContent.TemplateData> dataMap = tempMsg.getData().stream()
                        .collect(Collectors.toMap(WxMpTemplateData::getName, i -> PushMsgContent.TemplateData.builder().color(i.getColor()).value(i.getValue()).build()));
                PushMsgContent content = PushMsgContent.builder().templateId(tempMsg.getTemplateId()).url(tempMsg.getUrl()).data(dataMap).build();
                WxPushReq wxPushReq = WxPushReq.builder()
                        .accountType(accountType)
                        .chatRoomId(chatRoom.getId())
                        .caseId(stockCase.getId())
                        .vipPackageNumber(vipPackageSet)
                        .productType(productType)
                        .templateMsg(tempMsg)
                        .msgType(pushMsgType)
                        .companyType(companyType)
                        .priority(priority)
                        .msgBody(JSONObject.toJSONString(content))
                        .eventName(eventName)
                        .isStockCasePush(true)
                        .build();
                // 模板消息推送
                rocketMqUtils.send(PushMQConstant.PUSH_TOPIC, PushMQConstant.TAG_PUSH_WX, wxPushReq);
            }
            // 短信消息 - 去掉产品二档调仓和建仓时的短信通知
//            SmsPushReq smsPushReq = SmsPushReq.builder()
//                    .vipPackageNumber(vipPackageSet)
//                    .portfolioId(null)
//                    .productType(productType)
//                    .content(StrUtil.format(PortfolioSendConst.ADJUST_SMS_TEMPLATE, channelInfo.getShowName()))
//                    .msgType(pushMsgType)
//                    .companyType(companyType)
//                    .priority(priority)
//                    .msgBody(StrUtil.format(PortfolioSendConst.ADJUST_SMS_TEMPLATE, channelInfo.getShowName()))
//                    .eventName(eventName)
//                    .build();
//            rocketMqUtils.send(PushMQConstant.PUSH_TOPIC, PushMQConstant.TAG_PUSH_SMS, smsPushReq);
            // 语音电话
            CallPushProcessReq callPushProcessReq = CallPushProcessReq.builder()
                    .companyType(companyType)
                    .priority(priority)
                    .pushMsgType(pushMsgType)
                    .caseId(stockCase.getId())
                    .content(stockName)
                    .eventName(eventName)
                    .vipPackageNumber(vipPackageSet)
                    .isStockCasePush(true)
                    .build();
            rocketMqUtils.send(RocketMQConstant.PUSH_TOPIC, RocketMQConstant.TAG_PUSH_CALL_PROCESS, callPushProcessReq);
        }

        return Action.CommitMessage;
    }
}
