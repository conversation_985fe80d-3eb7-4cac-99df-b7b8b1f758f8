package cn.shrise.radium.tradeservice.dao;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.tradeservice.entity.QTdStockChannelNoticeSub;
import cn.shrise.radium.tradeservice.entity.TdStockChannelNoticeSub;
import cn.shrise.radium.tradeservice.repository.TdStockChannelNoticeSubRepository;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class StockChannelNoticeSubDao {

    private final JPAQueryFactory jpaQueryFactory;
    private final TdStockChannelNoticeSubRepository tdStockChannelNoticeSubRepository;
    private final QTdStockChannelNoticeSub qTdStockChannelNoticeSub = QTdStockChannelNoticeSub.tdStockChannelNoticeSub;

    public TdStockChannelNoticeSub findOneByUser(Integer userId, Integer channelType, Integer deviceType) {
        return jpaQueryFactory.selectFrom(qTdStockChannelNoticeSub)
                .where(qTdStockChannelNoticeSub.userId.eq(userId))
                .where(qTdStockChannelNoticeSub.channelType.eq(channelType))
                .where(qTdStockChannelNoticeSub.deviceType.eq(deviceType))
                .fetchOne();
    }

    public List<TdStockChannelNoticeSub> findIsSubList(Integer channelType, Integer deviceType, Collection<Integer> userIds) {
        return jpaQueryFactory.selectFrom(qTdStockChannelNoticeSub)
                .where(qTdStockChannelNoticeSub.channelType.eq(channelType))
                .where(qTdStockChannelNoticeSub.deviceType.eq(deviceType))
                .where(qTdStockChannelNoticeSub.userId.in(userIds))
                .where(qTdStockChannelNoticeSub.isSubscribe.eq(true))
                .fetch();
    }

    @Transactional(rollbackOn = Exception.class)
    public void createOrUpdateOne(TdStockChannelNoticeSub info) {
        TdStockChannelNoticeSub existed = jpaQueryFactory.selectFrom(qTdStockChannelNoticeSub)
                .where(qTdStockChannelNoticeSub.userId.eq(info.getUserId()))
                .where(qTdStockChannelNoticeSub.channelType.eq(info.getChannelType()))
                .where(qTdStockChannelNoticeSub.deviceType.eq(info.getDeviceType()))
                .fetchOne();
        if (ObjectUtil.isNotEmpty(existed)) {
            jpaQueryFactory.update(qTdStockChannelNoticeSub)
                    .set(qTdStockChannelNoticeSub.isSubscribe, info.getIsSubscribe())
                    .where(qTdStockChannelNoticeSub.id.eq(existed.getId()))
                    .execute();
        }else {
            tdStockChannelNoticeSubRepository.save(info);
        }
    }

    @Transactional
    public void cancelUserSub(@NonNull Integer userId, Integer deviceType) {
        jpaQueryFactory.update(qTdStockChannelNoticeSub)
                .set(qTdStockChannelNoticeSub.isSubscribe, false)
                .where(qTdStockChannelNoticeSub.userId.eq(userId))
                .where(qTdStockChannelNoticeSub.deviceType.eq(deviceType))
                .execute();
    }

}
