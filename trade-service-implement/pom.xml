<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.shrise.radium</groupId>
        <artifactId>trade-service</artifactId>
        <version>5.061902.1.0</version>
    </parent>

    <artifactId>trade-service-implement</artifactId>
    <name>trade-service-implement</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>trade-service-interface</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>content-service-interface</artifactId>
            <version>3.0831.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.schedulerx</groupId>
            <artifactId>schedulerx2-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>order-service-interface</artifactId>
            <version>4.0111.1.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>user-service-interface</artifactId>
            <version>4.1127.4.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>wx-service-interface</artifactId>
            <version>2.0623.4.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>notification-service-interface</artifactId>
            <version>4.0705.4.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>push-service-app-interface</artifactId>
            <version>3.1027.1.0</version>
        </dependency>
        <dependency>
            <groupId>cn.shrise.radium</groupId>
            <artifactId>im-service-interface</artifactId>
            <version>3.1116.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.blazebit</groupId>
            <artifactId>blaze-persistence-integration-querydsl-expressions</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.blazebit</groupId>
            <artifactId>blaze-persistence-integration-hibernate-5.4</artifactId>
            <scope>runtime</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.parent.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
