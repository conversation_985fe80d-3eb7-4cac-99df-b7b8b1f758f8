package cn.shrise.radium.adminapi.entity;

import cn.shrise.radium.userservice.entity.UcRole;
import cn.shrise.radium.userservice.entity.UcStaffExt;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class User {

    @ApiModelProperty("用户id")
    private Integer id;

    @ApiModelProperty("用户code")
    private String userCode;

    @ApiModelProperty("用户类型")
    private Integer userType;

    @ApiModelProperty("公司类型")
    private Integer companyType;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("是否需要重置密码")
    private Boolean resetPassword;

    @ApiModelProperty("员工工号")
    private String workNumber;

    @ApiModelProperty("用户编号")
    private String number;

    @ApiModelProperty("用户昵称")
    private String nickName;

    @ApiModelProperty("真实姓名")
    private String realName;

    @ApiModelProperty("头像")
    private String avatarUrl;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("是否在职")
    private Boolean enabled;

    @ApiModelProperty("注册时间")
    private Instant createTime;

    @ApiModelProperty("微信信息")
    private WxExtInfo wxInfo;

    @ApiModelProperty("评测信息")
    private CustomerEvaluation evaluationInfo;

    @ApiModelProperty("角色信息")
    private UcRole role;

    @ApiModelProperty("员工信息")
    private UcStaffExt staffExt;

    @ApiModelProperty("性别")
    private Integer gender;

    @ApiModelProperty("出生日期")
    private Instant birthday;

    @ApiModelProperty(value = "是否部门管理员")
    private Boolean isManager;

    @ApiModelProperty("主账号id")
    private Integer staffId;

    public User(Integer id, Integer userType, Integer companyType, String userName, Boolean resetPassword, String number, String nickName, String realName, String avatarUrl, String remark, String department, Boolean enabled, Instant createTime, WxExtInfo wxInfo, CustomerEvaluation evaluationInfo) {
        this.id = id;
        this.userType = userType;
        this.companyType = companyType;
        this.userName = userName;
        this.resetPassword = resetPassword;
        this.number = number;
        this.nickName = nickName;
        this.realName = realName;
        this.avatarUrl = avatarUrl;
        this.remark = remark;
        this.department = department;
        this.enabled = enabled;
        this.createTime = createTime;
        this.wxInfo = wxInfo;
        this.evaluationInfo = evaluationInfo;
    }
}
