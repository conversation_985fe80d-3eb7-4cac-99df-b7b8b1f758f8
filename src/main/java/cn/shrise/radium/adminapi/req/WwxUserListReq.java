package cn.shrise.radium.adminapi.req;

import cn.shrise.radium.common.base.BasePageReq;
import cn.shrise.radium.common.util.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class WwxUserListReq extends BasePageReq {
    @ApiModelProperty(value = "企业微信公司类型", name = "accountType")
    private Integer accountType;

    @ApiModelProperty(value = "员工wxAccount", name = "wxAccountList")
    private List<String> wxAccountList;

    @ApiModelProperty(value = "账号状态", name = "status")
    private Integer status;

    @ApiModelProperty(value = "员工状态", name = "userStatus")
    private Integer userStatus;

    @ApiModelProperty(value = "是否关联用户", name = "isRelate")
    private Boolean isRelate;

    @ApiModelProperty(value = "归属销售", name = "belongIdList")
    private List<Integer> belongIdList;

    @ApiModelProperty(value = "搜索内容", name = "searchText")
    private String searchText;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE)
    private LocalDate startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE)
    private LocalDate endTime;
}
