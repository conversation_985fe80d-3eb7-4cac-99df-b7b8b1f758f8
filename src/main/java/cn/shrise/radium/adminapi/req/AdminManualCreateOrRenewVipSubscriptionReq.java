package cn.shrise.radium.adminapi.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminManualCreateOrRenewVipSubscriptionReq {

    @NotNull
    private String userCode;

    @ApiModelProperty(hidden = true)
    private Integer userId;

    @NotBlank
    private String number;

    @NotNull
    private Integer level;

    @NotNull
    @Min(value = 1)
    private Integer period;

    private String remark;
}
