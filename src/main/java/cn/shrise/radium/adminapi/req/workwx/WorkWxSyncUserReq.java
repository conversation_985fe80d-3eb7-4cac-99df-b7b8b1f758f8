package cn.shrise.radium.adminapi.req.workwx;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkWxSyncUserReq {

    @NotNull
    @ApiModelProperty("企业微信账号类型")
    private Integer accountType;

    @NotBlank
    @ApiModelProperty("企业微信员工账号")
    private String wxAccount;
}
