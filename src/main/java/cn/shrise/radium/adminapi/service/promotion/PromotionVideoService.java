package cn.shrise.radium.adminapi.service.promotion;

import cn.hutool.core.bean.BeanUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.constant.PromotionVideoTypeConstant;
import cn.shrise.radium.contentservice.entity.SsPromotionVideo;
import cn.shrise.radium.contentservice.resp.promotion.PromotionVideoInfoResp;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.req.promotion.PromotionVideoPublisherReq;
import cn.shrise.radium.contentservice.req.promotion.PromotionVideoPublisherResp;
import cn.shrise.radium.contentservice.req.promotion.PromotionVideoTagResp;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> zhangjianwu
 * @created : 2025/7/4, 星期五
 **/

@Slf4j
@Service
@RequiredArgsConstructor
public class PromotionVideoService {

    private final ContentClient contentClient;
    private final UserClient userClient;

    public PromotionVideoInfoResp getVideoInfo(Long videoId) {
        SsPromotionVideo ssPromotionVideo = contentClient.getPromotionVideoInfo(videoId).getData();
        PromotionVideoInfoResp promotionVideoInfoResp = BeanUtil.copyProperties(ssPromotionVideo, PromotionVideoInfoResp.class);
        List<PromotionVideoTagResp> tagRelationList = contentClient.getPromotionVideoTagList(videoId).getData();
        List<PromotionVideoInfoResp.VideoTagInfo> tagInfos = tagRelationList.stream()
                .map(e -> PromotionVideoInfoResp.VideoTagInfo.builder().tagId(e.getId()).tagName(e.getName()).build())
                .collect(Collectors.toList());
        promotionVideoInfoResp.setTagList(tagInfos);
        return promotionVideoInfoResp;
    }

    public PageResult<List<PromotionVideoInfoResp>> listVideos(Integer current, Integer size, Integer searchType, String searchContent,
                                                               LocalDate startDate, LocalDate endDate, List<Integer> auditStatusList, Integer isShow,
                                                               List<Long> tagList, List<Integer> creatorList, Integer videoType, Long publisherId, Integer userId) {
        PageResult<List<SsPromotionVideo>> result = contentClient.listPromotionVideo(current, size, searchType, searchContent,
                startDate, endDate, auditStatusList, isShow, tagList, creatorList, videoType, publisherId, userId);
        if (ObjectUtil.isEmpty(result.getData())) {
            return PageResult.empty(current, size);
        }
        List<SsPromotionVideo> ssPromotionVideos = result.getData();
        List<Long> videoIdList = ssPromotionVideos.stream().map(SsPromotionVideo::getId).collect(Collectors.toList());
        List<Long> publisherIdList = ssPromotionVideos.stream().map(SsPromotionVideo::getPublisherId).collect(Collectors.toList());
        List<Integer> userIdList = ssPromotionVideos.stream()
                .flatMap(e -> Stream.of(e.getAuditorId(), e.getCreatorId())).collect(Collectors.toList());
        Map<Long, List<PromotionVideoTagResp>> videoTagMap = contentClient.getPromotionVideoTagMap(videoIdList).getData();
        PromotionVideoPublisherReq publisherReq = PromotionVideoPublisherReq.builder().publisherIdList(publisherIdList).build();
        List<PromotionVideoPublisherResp> publisherResps = contentClient.publisherList(publisherReq).getData();
        Map<Long, PromotionVideoPublisherResp> publisherMap = publisherResps.stream()
                .collect(Collectors.toMap(PromotionVideoPublisherResp::getId, e -> e));
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userIdList)).getData();
        Map<Integer, String> deptMap = userClient.getDeptListByUsers(userIdList, 1).getData();
        List<PromotionVideoInfoResp> promotionVideoInfoResps = BeanUtil.copyToList(ssPromotionVideos, PromotionVideoInfoResp.class);
        promotionVideoInfoResps.forEach(i -> {
            i.setAuditorName(usersMap.getOrDefault(i.getAuditorId(), new UcUsers()).getRealName());
            i.setUploaderName(usersMap.getOrDefault(i.getCreatorId(), new UcUsers()).getRealName());
            i.setPublisherName(publisherMap.getOrDefault(i.getPublisherId(), new PromotionVideoPublisherResp()).getNickname());
            i.setDepartment(ObjectUtil.isEmpty(i.getCreatorId()) ? null : deptMap.get(i.getCreatorId()));
            populateTagInfos(i, videoTagMap);
        });
        return PageResult.success(promotionVideoInfoResps, current, size, result.getPagination().getTotal());
    }

    public PageResult<List<PromotionVideoInfoResp>> listShareVideos(Integer current, Integer size, Integer searchType,
                                                                    String searchContent, List<Long> tagList, LocalDate startDate, LocalDate endDate, Integer userId) {
        PageResult<List<SsPromotionVideo>> result = contentClient.listSharePromotionVideo(current, size, searchType, searchContent, tagList, startDate, endDate, userId);
        if (ObjectUtil.isEmpty(result.getData())) {
            return PageResult.empty(current, size);
        }
        List<SsPromotionVideo> ssPromotionVideos = result.getData();
        List<Long> videoIdList = ssPromotionVideos.stream().map(SsPromotionVideo::getId).collect(Collectors.toList());
        List<Integer> userIdList = ssPromotionVideos.stream()
                .flatMap(e -> Stream.of(e.getAuditorId(), e.getCreatorId())).collect(Collectors.toList());
        List<Long> publisherIdList = ssPromotionVideos.stream().map(SsPromotionVideo::getPublisherId).collect(Collectors.toList());
        PromotionVideoPublisherReq publisherReq = PromotionVideoPublisherReq.builder().publisherIdList(publisherIdList).build();
        Map<Long, List<PromotionVideoTagResp>> videoTagMap = contentClient.getPromotionVideoTagMap(videoIdList).getData();
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userIdList)).getData();
        List<PromotionVideoPublisherResp> publisherResps = contentClient.publisherList(publisherReq).getData();
        Map<Long, PromotionVideoPublisherResp> publisherMap = publisherResps.stream()
                .collect(Collectors.toMap(PromotionVideoPublisherResp::getId, e -> e));
        List<PromotionVideoInfoResp> promotionVideoInfoResps = BeanUtil.copyToList(ssPromotionVideos, PromotionVideoInfoResp.class);
        promotionVideoInfoResps.forEach(i -> {
            i.setAuditorName(usersMap.getOrDefault(i.getAuditorId(), new UcUsers()).getRealName());
            i.setUploaderName(usersMap.getOrDefault(i.getCreatorId(), new UcUsers()).getRealName());
            i.setPublisherName(publisherMap.getOrDefault(i.getPublisherId(), new PromotionVideoPublisherResp()).getNickname());
            populateTagInfos(i, videoTagMap);
        });
        return PageResult.success(promotionVideoInfoResps, current, size, result.getPagination().getTotal());
    }

    private void populateTagInfos(PromotionVideoInfoResp videoInfoResp, Map<Long, List<PromotionVideoTagResp>> videoTagMap) {
        if (ObjectUtil.isNotEmpty(videoTagMap.get(videoInfoResp.getId()))) {
            List<PromotionVideoTagResp> tagRespList = videoTagMap.get(videoInfoResp.getId());
            List<PromotionVideoInfoResp.VideoTagInfo> tagInfos = tagRespList.stream()
                    .map(tagResp ->
                            PromotionVideoInfoResp.VideoTagInfo.builder().tagId(tagResp.getId()).tagName(tagResp.getName()).build()
                    )
                    .collect(Collectors.toList());
            videoInfoResp.setTagList(tagInfos);
        } else {
            videoInfoResp.setTagList(new ArrayList<>());
        }
    }

    public PageResult<List<PromotionVideoPublisherResp>> publisherList(PromotionVideoPublisherReq req) {
        PageResult<List<PromotionVideoPublisherResp>> result = contentClient.publisherList(req);
        if (ObjectUtil.isEmpty(result.getData())) {
            return PageResult.empty();
        }
        Set<Integer> userSet = result.getData().stream()
                .flatMap(i -> Stream.of(i.getAuditorId(), i.getCreatorId()))
                .collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
        result.getData().forEach(i -> {
            if (ObjectUtil.isNotEmpty(usersMap)) {
                i.setCreatorName(usersMap.getOrDefault(i.getCreatorId(), new UcUsers()).getRealName());
                i.setAuditorName(usersMap.getOrDefault(i.getAuditorId(), new UcUsers()).getRealName());
            }
        });
        return result;
    }

    public BaseResult<List<PromotionVideoPublisherResp>> publisherSalesList(Integer salesId, Integer auditStatus) {
        List<PromotionVideoPublisherResp> result = contentClient.publisherSalesList(salesId, auditStatus).getData();
        if (ObjectUtil.isEmpty(result)) {
            return BaseResult.success(null);
        }
        Set<Integer> userSet = result.stream()
                .flatMap(i -> Stream.of(i.getAuditorId(), i.getCreatorId()))
                .collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userSet)).getData();
        result.forEach(i -> {
            if (ObjectUtil.isNotEmpty(usersMap)) {
                i.setCreatorName(usersMap.getOrDefault(i.getCreatorId(), new UcUsers()).getRealName());
                i.setAuditorName(usersMap.getOrDefault(i.getAuditorId(), new UcUsers()).getRealName());
            }
        });
        return BaseResult.success(result);
    }

    public PageResult<List<PromotionVideoTagResp>> videoTagList(Integer current, Integer size) {
        PageResult<List<PromotionVideoTagResp>> result = contentClient.videoTagList(current, size);
        if (ObjectUtil.isEmpty(result.getData())) {
            return PageResult.empty();
        }
        Set<Integer> creatorSet = result.getData().stream().map(PromotionVideoTagResp::getCreatorId).collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(creatorSet)).getData();
        result.getData().forEach(i -> {
            if (ObjectUtil.isNotEmpty(usersMap)) {
                i.setCreatorName(usersMap.getOrDefault(i.getCreatorId(), new UcUsers()).getRealName());
            }
        });
        return result;
    }
}
