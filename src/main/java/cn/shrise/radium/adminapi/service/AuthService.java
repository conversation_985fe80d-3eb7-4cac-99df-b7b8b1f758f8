package cn.shrise.radium.adminapi.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.entity.User;
import cn.shrise.radium.adminapi.properties.WwxRobotLimitProperties;
import cn.shrise.radium.adminapi.req.ActiveDeviceReq;
import cn.shrise.radium.adminapi.req.DdBindUserReq;
import cn.shrise.radium.adminapi.req.GenerateAdminTokenReq;
import cn.shrise.radium.adminapi.req.RefreshAdminTokenReq;
import cn.shrise.radium.adminapi.resp.*;
import cn.shrise.radium.adminapi.util.IpUtils;
import cn.shrise.radium.authservice.AuthClient;
import cn.shrise.radium.authservice.constant.ErrorConstant;
import cn.shrise.radium.authservice.entity.AccessToken;
import cn.shrise.radium.authservice.entity.AccessTokenPayload;
import cn.shrise.radium.authservice.req.*;
import cn.shrise.radium.authservice.resp.AdminLoginResp;
import cn.shrise.radium.authservice.resp.AdminVerifyResp;
import cn.shrise.radium.authservice.resp.GenerateTokenResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.entity.MainCompany;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.common.properties.CompanyProperties;
import cn.shrise.radium.common.properties.VersionLimitProperties;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.dingdingservice.DingDingClient;
import cn.shrise.radium.dingdingservice.entity.DdStaffInfo;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.constant.LoginPlatformConstant;
import cn.shrise.radium.userservice.constant.StaffTrackEventTypeConstant;
import cn.shrise.radium.userservice.entity.UcMainStaff;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.req.CreateAccountActivityReq;
import cn.shrise.radium.userservice.req.CreateStaffTrackEventReq;
import cn.shrise.radium.userservice.resp.UserBaseInfoResp;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.maven.artifact.versioning.ComparableVersion;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

import static cn.shrise.radium.adminapi.constant.ErrorConstant.LOGIN_SECURITY_VALIDATION;
import static cn.shrise.radium.authservice.constant.TokenTypeConstant.ADMIN;
import static cn.shrise.radium.common.constant.ProductTypeConstant.WEB_ADMIN;
import static cn.shrise.radium.common.constant.ProductTypeConstant.WWX_ROBOT;
import static cn.shrise.radium.common.util.LockUtils.getMobileLockKey;
import static cn.shrise.radium.common.util.LockUtils.locked;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final AuthClient authClient;
    private final RestTemplate restTemplate;
    private final CommonProperties commonProperties;
    private static final Integer PRODUCT_TYPE = 100;
    private static final Integer COMPANY_TYPE = 45;
    private final UserClient userClient;
    private final RedissonClient redissonClient;
    private final WwxRobotLimitProperties wwxRobotLimitProperties;
    private final DingDingClient dingDingClient;
    private final CompanyProperties companyProperties;
    private final VersionLimitProperties versionLimitProperties;

    public LoginResp login(Integer companyType, String userName, String password,
                           Boolean remember, String validateCode, Integer productType, HttpServletRequest request,
                           String wwxRobotVersion, String appId, String deviceNumber) {

        productType = Objects.nonNull(productType) ? productType : WEB_ADMIN;
        if (Objects.equals(productType, WWX_ROBOT)) {
            wwxRobotVersionLimit(wwxRobotVersion);
        }

        AdminLoginReq req = AdminLoginReq.builder()
                .userName(userName)
                .password(password)
                .companyType(companyType)
                .remember(remember)
                .productType(productType)
                .appId(appId)
                .deviceNumber(deviceNumber)
                .build();
        BaseResult<AdminLoginResp> loginResult = authClient.adminLogin(req);
        if (!loginResult.isSuccess()) {
            throw new BusinessException(loginResult);
        }

        AdminLoginResp loginResp = loginResult.getData();
        AdminLoginResp.StaffInfo staffInfo = loginResp.getStaff();
//        if (staff.getIsSecurityVerify()) {
//            String staffMobile = AESUtil.decrypt(staff.getMobile());
//            if (ObjectUtils.isEmpty(staffMobile)) {
//                throw new BusinessException("该用户未绑定手机号");
//            }
//            checkValidateCode(staffMobile, validateCode);
//        }

        AccessToken accessToken = loginResp.getAccessToken();
        AdminLoginResp.UserInfo userInfo = loginResp.getUser();
        String token = getWebAdminToken(productType, userInfo.getId(), accessToken.getValue(), accessToken.getExpire().toEpochMilli());
        User user = new User();
        BeanUtils.copyProperties(userInfo, user);

        createLoginActivity(request, companyType, user.getId(), productType);
        // 新增登录行为
//        createLoginTrackEvent(request, companyType, user.getId(), appId);

        // 更新登录设备信息
        if (ObjectUtil.equals(productType, WEB_ADMIN)){
            updateLoginDeviceInfo(staffInfo.getId(), req.getAppId(), req.getDeviceNumber(), request);
        }

        return LoginResp.builder()
                .user(user)
                .accessToken(loginResp.getAccessToken())
                .refreshToken(loginResp.getRefreshToken())
                .token(token)
                .build();
    }


    public LoginResp ddLogin(Integer companyType, Long ddId, Boolean remember,
                             Integer productType, HttpServletRequest request, String wwxRobotVersion, String appId,
                             String deviceNumber) {

        productType = Objects.nonNull(productType) ? productType : WEB_ADMIN;
        if (Objects.equals(productType, WWX_ROBOT)) {
            wwxRobotVersionLimit(wwxRobotVersion);
        }

        AdminDdLoginReq req = AdminDdLoginReq.builder()
                .ddId(ddId)
                .companyType(companyType)
                .remember(remember)
                .productType(productType)
                .appId(appId)
                .deviceNumber(deviceNumber)
                .build();
        BaseResult<AdminLoginResp> loginResult = authClient.adminDdLogin(req);
        if (!loginResult.isSuccess()) {
            throw new BusinessException(loginResult);
        }

        AdminLoginResp loginResp = loginResult.getData();
        AccessToken accessToken = loginResp.getAccessToken();
        AdminLoginResp.UserInfo userInfo = loginResp.getUser();
        String token = getWebAdminToken(productType, userInfo.getId(), accessToken.getValue(), accessToken.getExpire().toEpochMilli());
        User user = new User();
        BeanUtils.copyProperties(userInfo, user);

        createLoginActivity(request, companyType, user.getId(), productType);
        // 新增登录行为
        createLoginTrackEvent(request, companyType, user.getId(), appId);

        // 更新登录设备信息
        if (ObjectUtil.equals(productType, WEB_ADMIN)){
            updateLoginDeviceInfo(loginResp.getStaff().getId(), req.getAppId(), req.getDeviceNumber(), request);
        }

        return LoginResp.builder()
                .user(user)
                .accessToken(loginResp.getAccessToken())
                .refreshToken(loginResp.getRefreshToken())
                .token(token)
                .build();
    }

    public DingDingUserInfoResp getDingDingUserInfoByCode(Integer companyType, Integer accountType, String agentFor, String authCode) {
        BaseResult<DdStaffInfo> staffInfoRes = dingDingClient.getStaffInfoByCode(companyType, accountType, agentFor, authCode);
        if (staffInfoRes.isFail()) {
            throw new BusinessException(staffInfoRes);
        }
        DdStaffInfo staffInfo = staffInfoRes.getData();
        String staffUserName = null;
        BaseResult<UcUsers> userRes = userClient.getUserByDd(staffInfo.getId());
        if (userRes.isPresent() && ObjectUtil.isNotNull(userRes.getData())) {
            UcUsers user = userRes.getData();
            BaseResult<UcMainStaff> staffRes = userClient.getStaffByUserId(user.getCompanyType(), user.getId());
            if (staffRes.isPresent() && ObjectUtil.isNotNull(staffRes.getData())) {
                staffUserName = staffRes.getData().getUserName();
            }
        }
        return DingDingUserInfoResp.builder()
                .ddId(staffInfo.getId())
                .name(staffInfo.getName())
                .staffUserName(staffUserName)
                .build();
    }

    public void ddBindUser(DdBindUserReq req) {
        AdminVerifyReq adminVerifyReq = AdminVerifyReq.builder()
                .companyType(req.getCompanyType())
                .userName(req.getUserName())
                .password(req.getPassword())
                .build();
        BaseResult<AdminVerifyResp> verifyResp = authClient.adminVerify(adminVerifyReq);
        if (verifyResp.isFail()) {
            throw new BusinessException(verifyResp);
        }
        AdminVerifyResp.UserInfo user = verifyResp.getData().getUser();
        BaseResult<Void> result = userClient.ddBindUser(user.getId(), req.getDdId(), req.getCompanyType(), user.getId());
        if (result.isFail()) {
            throw new BusinessException(result);
        }
    }

    public void wwxRobotVersionLimit(String wwxRobotVersion) {
        if (ObjectUtils.isEmpty(wwxRobotVersion)) {
            throw new BusinessException("请升级安装最新版本的营销助手");
        }
        //version compare
        VersionLimitProperties.LimitVersion limitVersion = versionLimitProperties.getConfig().get(WWX_ROBOT);
        if (limitVersion != null) {
            if (ObjectUtil.equals(limitVersion.getForceOut(), true)) {
                throw new BusinessException("登录失败，统一关闭登录");
            }
            ComparableVersion comparableVersion = new ComparableVersion(wwxRobotVersion);
            String min = limitVersion.getMin();
            String max = limitVersion.getMax();

            if (!ObjectUtils.isEmpty(min)) {
                ComparableVersion minVersion = new ComparableVersion(min);
                if (comparableVersion.compareTo(minVersion) < 0) {
                    throw new BusinessException("请升级安装最新版本的营销助手");
                }
            }

//            if (!ObjectUtils.isEmpty(max)) {
//                ComparableVersion maxVersion = new ComparableVersion(max);
//                if (comparableVersion.compareTo(maxVersion) > 0) {
//                    throw new BusinessException("您的版本过高，请先降级");
//                }
//            }
        }
    }

    private void createLoginActivity(HttpServletRequest request, Integer companyType, Integer userId, Integer productType) {
        String clientIp = IpUtils.getClientIp(request);
        String ua = request.getHeader("user-agent");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("ua", ua);
        CreateAccountActivityReq createAccountActivityReq = CreateAccountActivityReq.builder()
                .ip(clientIp)
                .companyType(companyType)
                .productType(productType)
                .userId(userId)
                .platform(LoginPlatformConstant.WEB)
                .deviceInfo(jsonObject.toJSONString())
                .build();
        userClient.createAccountActivity(createAccountActivityReq);
    }

    private void createLoginTrackEvent(HttpServletRequest request, Integer companyType, Integer userId, String appId) {
        String clientIp = IpUtils.getClientIp(request);
        String ua = request.getHeader("user-agent");
        CreateStaffTrackEventReq createStaffTrackEventReq = CreateStaffTrackEventReq.builder()
                .companyType(companyType)
                .staffId(userId)
                .appId(appId)
                .pageId("login")
                .ip(clientIp)
                .userAgent(ua)
                .trackType(StaffTrackEventTypeConstant.LOGIN)
                .clientVersion(request.getHeader("x-version"))
                .build();
        userClient.uploadStaffTrackEvent(createStaffTrackEventReq);
    }

    private void updateLoginDeviceInfo(Integer staffId, String appId, String deviceNumber, HttpServletRequest request) {
        String clientIp = IpUtils.getClientIp(request);
        String ua = request.getHeader("user-agent");
        userClient.updateDevice(staffId, appId, deviceNumber, clientIp, ua);
    }

    public BaseResult<GenerateTokenResp> generateToken(GenerateAdminTokenReq req) {
        BaseResult<UcUsers> result = userClient.getUser(req.getUserId());
        if (!result.isSuccess()) {
            throw new RecordNotExistedException();
        }
        UcUsers user = result.getData();
        GenerateAccessTokenReq generateTokenReq = GenerateAccessTokenReq.builder()
                .companyType(user.getCompanyType())
                .productType(PRODUCT_TYPE)
                .userId(user.getId())
                .userType(user.getUserType())
                .tokenType(ADMIN)
                .ext(req.getExt())
                .build();
        return authClient.generateToken(generateTokenReq);
    }

    public String getWebAdminToken(Integer productType, Integer userId, String accessToken,
                                   Long accessTokenExpire) {
        String prefix = commonProperties.getPythonWebServer();
        String url = String.format("%s/auth/token.json", prefix);
        MultiValueMap<String, Object> param = new LinkedMultiValueMap<>(4);
        param.add("product_type", productType);
        param.add("user_id", userId);
        param.add("access_token", accessToken);
        param.add("access_token_expire", accessTokenExpire);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
        HttpEntity<MultiValueMap<String, Object>> httpEntity = new HttpEntity<>(param, headers);
        ResponseEntity<GetTokenResp> responseEntity = restTemplate.postForEntity(url, httpEntity, GetTokenResp.class);
        log.info("GetTokenResp {}", responseEntity.getBody());
        String token = null;
        int statusCodeValue = responseEntity.getStatusCodeValue();
        if (statusCodeValue == HttpStatus.OK.value()) {
            GetTokenResp getTokenResp = responseEntity.getBody();
            if (getTokenResp != null && getTokenResp.getErrorCode() == 1) {
                token = getTokenResp.getTokenId();
            }
        }
        return token;
    }

    public RefreshTokenResp refreshToken(RefreshAdminTokenReq req) {
        RefreshTokenReq refreshTokenReq = RefreshTokenReq.builder()
                .userId(req.getUserId())
                .refreshToken(req.getRefreshToken())
                .productType(PRODUCT_TYPE)
                .tokenType(ADMIN)
                .ext(req.getExt())
                .build();

        final BaseResult<AccessToken> accessTokenResult = authClient.refreshToken(refreshTokenReq);
        if (accessTokenResult.isFail()) {
            throw new BusinessException(accessTokenResult);
        }
        final AccessToken accessToken = accessTokenResult.getData();
        String token = getWebAdminToken(PRODUCT_TYPE, req.getUserId(), accessToken.getValue(), accessToken.getExpire().toEpochMilli());
        return RefreshTokenResp.builder()
                .accessToken(accessToken)
                .token(token)
                .build();
    }

    public RefreshTokenResp sessionLogin(RefreshAdminTokenReq req, HttpServletRequest request) {
        RefreshTokenReq refreshTokenReq = RefreshTokenReq.builder()
                .userId(req.getUserId())
                .refreshToken(req.getRefreshToken())
                .productType(PRODUCT_TYPE)
                .tokenType(ADMIN)
                .ext(req.getExt())
                .build();

        final BaseResult<AccessToken> accessTokenResult = authClient.refreshToken(refreshTokenReq);
        if (accessTokenResult.isFail()) {
            throw new BusinessException(accessTokenResult);
        }
        final AccessToken accessToken = accessTokenResult.getData();
        String token = getWebAdminToken(PRODUCT_TYPE, req.getUserId(), accessToken.getValue(), accessToken.getExpire().toEpochMilli());
        // 新增登录行为
        UcUsers users = userClient.getUser(req.getUserId()).orElse(null);
        Integer companyType = users == null ? COMPANY_TYPE : users.getCompanyType();
        createLoginTrackEvent(request, companyType, req.getUserId(), req.getAppId());
        return RefreshTokenResp.builder()
                .accessToken(accessToken)
                .token(token)
                .build();
    }

    public Map<String, Object> getTokenPayload(String accessToken) {
        BaseResult<AccessTokenPayload> decodeAccessTokenResult = authClient.decodeAccessToken(ADMIN, accessToken);
        if (decodeAccessTokenResult.isFail()) {
            return Collections.emptyMap();
        }
        AccessTokenPayload payload = decodeAccessTokenResult.getData();
        return payload.toHeaderMap();
    }

    public AccessToken refreshAccessToken(String accessToken, String refreshToken) {
        RefreshAccessTokenReq refreshAccessTokenReq = RefreshAccessTokenReq.builder()
                .tokenType(ADMIN)
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .build();
        BaseResult<AccessToken> result = authClient.refreshAccessToken(refreshAccessTokenReq);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        return result.getData();
    }

    public BaseResult<Void> adminVerify(AdminVerifyReq req) {
        AdminVerifyResp verifyResp = authClient.adminVerify(req).orElseThrow();
        return BaseResult.successful();
    }

    public void ddUnBindUser(Integer userId, Integer companyType, Integer operatorId) {
        BaseResult<Void> result = userClient.ddUnBindUser(userId, companyType, operatorId);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
    }

    public void checkValidateCode(String mobile, String validateCode) {
        if (ObjectUtils.isEmpty(validateCode)) {
            throw new BusinessException(LOGIN_SECURITY_VALIDATION);
        }
        String mobileKey = getMobileLockKey(mobile);
        RLock lock = redissonClient.getLock(mobileKey);
        BaseResult<Boolean> lockedRes = locked(lock, () -> userClient.checkCode(mobile, validateCode));
        if (lockedRes.isFail()) {
            throw new BusinessException(lockedRes);
        }
        if (!lockedRes.getData()) {
            throw new BusinessException(ErrorConstant.VERIFY_NO_INVALID);
        }
    }

    public String activeDevice(ActiveDeviceReq req) {
        MainCompany mainCompany = companyProperties.getMainCompanyFromCompanyType(req.getCompanyType())
                .orElseThrow(RecordNotExistedException::new);
        BaseResult<UcMainStaff> staffResult = userClient.getStaff(mainCompany.getCompanyType(), req.getUserName(), null, null);
        if (!staffResult.isSuccess()) {
            throw new BusinessException(staffResult);
        }
        UcMainStaff staff = staffResult.getData();
        if (ObjectUtil.isNull(staff)) {
            throw new BusinessException(ErrorConstant.ACCOUNT_INVALID);
        }
        String mobile = AESUtil.decrypt(staff.getMobile());
        if (ObjectUtils.isEmpty(mobile)) {
            throw new BusinessException("该用户未绑定手机号");
        }
        checkValidateCode(mobile, req.getValidateCode());
        BaseResult<String> deviceInfoRes = authClient.getDeviceInfo(req.getActiveCode());
        if (deviceInfoRes.isFail()) {
            throw new BusinessException(deviceInfoRes);
        }
        String deviceInfo = deviceInfoRes.getData();
        JSONObject jsonObject = JSONObject.parseObject(deviceInfo);
        if (!ObjectUtil.equals(staff.getId(), jsonObject.getInteger("staff_id"))) {
            throw new BusinessException("账号与激活码不匹配");
        }
        if (!ObjectUtil.equals(req.getAppId(), jsonObject.getString("app_id"))) {
            throw new BusinessException("应用与激活码不匹配");
        }
        String deviceNumber = jsonObject.getString("device_number");
        BaseResult<Void> activeResult = userClient.activeDevice(staff.getId(), req.getAppId(), deviceNumber);
        if (activeResult.isFail()) {
            throw new BusinessException(activeResult);
        }
        return deviceNumber;
    }

    public AdminAppLoginResp appLogin(Integer companyType, String userName, String password, Boolean remember, String validateCode, Integer productType, HttpServletRequest request, String wwxRobotVersion, String appId, String deviceNumber) {
        productType = Objects.nonNull(productType) ? productType : WEB_ADMIN;
        if (Objects.equals(productType, WWX_ROBOT)) {
            wwxRobotVersionLimit(wwxRobotVersion);
        }

        AdminLoginReq req = AdminLoginReq.builder()
                .userName(userName)
                .password(password)
                .companyType(companyType)
                .remember(remember)
                .productType(productType)
                .appId(appId)
                .deviceNumber(deviceNumber)
                .build();
        BaseResult<AdminLoginResp> loginResult = authClient.adminLogin(req);
        if (!loginResult.isSuccess()) {
            throw new BusinessException(loginResult);
        }

        AdminLoginResp loginResp = loginResult.getData();
        AdminLoginResp.StaffInfo staffInfo = loginResp.getStaff();

        AccessToken accessToken = loginResp.getAccessToken();
        AdminLoginResp.UserInfo userInfo = loginResp.getUser();
        String token = getWebAdminToken(productType, userInfo.getId(), accessToken.getValue(), accessToken.getExpire().toEpochMilli());
        UserBaseInfoResp user = new UserBaseInfoResp();
        BeanUtils.copyProperties(userInfo, user);

        createLoginActivity(request, companyType, user.getId(), productType);

        // 更新登录设备信息
        if (ObjectUtil.equals(productType, WEB_ADMIN)){
            updateLoginDeviceInfo(staffInfo.getId(), req.getAppId(), req.getDeviceNumber(), request);
        }

        return AdminAppLoginResp.builder()
                .userBaseInfo(user)
                .accessToken(loginResp.getAccessToken())
                .refreshToken(loginResp.getRefreshToken())
                .token(token)
                .build();

    }
}
