package cn.shrise.radium.adminapi.service.im;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.im.*;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.entity.*;
import cn.shrise.radium.imservice.properties.ImPageMappingProperties;
import cn.shrise.radium.imservice.req.UpdateImChatRoomOperationReq;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.entity.TdStockCaseChannel;
import cn.shrise.radium.tradeservice.req.UpdatePortfolioChannelReq;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.entity.UcWxExt;
import cn.shrise.radium.userservice.resp.PageStatistics;
import cn.shrise.radium.userservice.resp.UserAndWxInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static cn.shrise.radium.tradeservice.constant.StockChannelType.SCT_PORTFOLIO;

@Slf4j
@Service
@RequiredArgsConstructor
public class ImChatRoomService {

    private final ImClient imClient;
    private final TradeClient tradeClient;

    private final ContentClient contentClient;

    private final UserClient userClient;

    private final OrderClient orderClient;

    public ChatRoomResp getChatRoom(Long roomId) {
        BaseResult<ImChatRoom> result = imClient.getChatRoom(roomId);
        BaseResult<TdStockCaseChannel> channelByFilter = tradeClient.getStockCaseChannelByFilter(null, null, roomId);
        if (result.isPresent()) {
            ChatRoomResp resp = ChatRoomResp.of(result.getData());
            if (channelByFilter.isPresent()) {
                TdStockCaseChannel caseChannel = channelByFilter.getData();
//                BaseResult<List<TdCaseChannelManagerRelation>> channelManagerRelations = tradeClient.getCaseChannelManagerRelations(userId, caseChannel.getId());
//                if (ObjectUtil.isNotEmpty(channelManagerRelations.getData())) {
                resp.setCaseId(caseChannel.getId());
                resp.setCaseName(caseChannel.getName());
                resp.setIsFollowHide(caseChannel.getIsFollowHide());
//                }
            }
            if (ObjectUtil.isNotEmpty(resp.getCaseChannelId())) {
                BaseResult<TdStockCaseChannel> channelBaseResult = tradeClient.getStockCaseChannelByFilter(resp.getCaseChannelId(), null, null);
                if (channelBaseResult.isPresent()) {
                    TdStockCaseChannel stockCaseChannel = channelBaseResult.getData();
                    resp.setCaseChannelName(stockCaseChannel.getName());
                    resp.setCaseChannelRemark(stockCaseChannel.getRemark());
                }
            }
            return resp;
        }
        return null;
    }

    public ImChatRoomDecorateResp getChatRoomDecorate(Long roomId) {
        BaseResult<ImChatRoom> result = imClient.getChatRoom(roomId);
        if (result.isPresent()) {
            return ImChatRoomDecorateResp.of(result.getData());
        }
        return null;
    }

    public void updateChatRoom(UpdateImChatRoomOperationReq req) {
        if (ObjectUtil.isNotNull(req.getChannelId())) {
            tradeClient.updatePortfolioChannel(
                    UpdatePortfolioChannelReq.builder().id(req.getChannelId()).showName(req.getShowName()).build());
        }
//        if (ObjectUtil.isAllNotEmpty(req.getCaseChannelId(), req.getCaseChannelName())) {
//            EditStockCaseChannelReq caseChannelReq = new EditStockCaseChannelReq();
//            caseChannelReq.setChannelId(req.getCaseChannelId());
//            caseChannelReq.setName(req.getCaseChannelName());
//            tradeClient.createOrUpdateCaseChannel(caseChannelReq);
//        }
        imClient.updateChatRoom(req);
    }

    public List<ChatManagerResp> getChatManagerAnalyst(Long chatId, Boolean enable) {
        BaseResult<List<ImChatManager>> baseResult = imClient.getChatRoomManagerInfo(chatId, enable);
        List<ChatManagerResp> respList = new ArrayList<>();
        if (baseResult.isPresent()) {
            List<ImChatManager> managerList = baseResult.getData();
            Set<Integer> analystSet = managerList.stream()
                    .map(ImChatManager::getAnalystId)
                    .filter(ObjectUtil::isNotEmpty)
                    .collect(Collectors.toSet());
            Set<Integer> managerSet = managerList.stream()
                    .map(ImChatManager::getManagerId)
                    .filter(ObjectUtil::isNotEmpty)
                    .collect(Collectors.toSet());
            Map<Integer, SsAnalystInfo> analystInfoMap = new HashMap<>(16);
            if (CollectionUtil.isNotEmpty(analystSet)) {
                BaseResult<List<SsAnalystInfo>> analystResult = contentClient.getAnalystInfoList(BatchReq.create(analystSet));
                if (analystResult.isPresent()) {
                    analystInfoMap = analystResult.getData().stream().collect(Collectors.toMap(SsAnalystInfo::getId, x -> x));
                }
            }
            Map<Integer, UcUsers> managerInfoMap = new HashMap<>(16);
            BaseResult<Map<Integer, UcUsers>> managerResult = userClient.batchGetUserMap(BatchReq.create(managerSet));
            if (managerResult.isPresent()) {
                managerInfoMap = managerResult.getData();
            }
            for (ImChatManager chatManager : managerList) {
                ChatManagerResp resp = ChatManagerResp.of(chatManager);
                if (analystInfoMap.containsKey(resp.getAnalystId())) {
                    resp.setAnalystInfo(analystInfoMap.get(resp.getAnalystId()));
                }
                if (managerInfoMap.containsKey(resp.getManagerId())) {
                    resp.setManagerName(managerInfoMap.get(resp.getManagerId()).getRealName());
                }
                respList.add(resp);
            }
        }
        return respList;
    }

    public BaseResult<Void> updateManagerAnalyst(Long chatManagerId, Integer analystId) {
        return imClient.updateManagerAnalyst(chatManagerId, analystId);
    }

    public BaseResult<PageStatistics> getPageStatistics(Long chatId, Long startTime, Long endTime) {
        BaseResult<ImPageMappingProperties.ChatPage> result = imClient.getChatPageInfo(chatId);
        if (result.isPresent()) {
            ImPageMappingProperties.ChatPage page = result.getData();
            return userClient.getPageStatistics(page.getAppId(), page.getPageId(), startTime, endTime);
        }
        return null;
    }

    public BaseResult<PageStatistics> getPageStatistics(Long chatId, Long flagTime) {
        BaseResult<ImPageMappingProperties.ChatPage> result = imClient.getChatPageInfo(chatId);
        if (result.isPresent()) {
            ImPageMappingProperties.ChatPage page = result.getData();
            return userClient.getPageStatistics(page.getAppId(), page.getPageId(), flagTime);
        }
        return null;
    }

    public PageResult<List<ImChatNoticeSubRecordResp>> getNoticeSubRecordPage(Long chatId, Integer deviceType, Long startTime, Long endTime, Integer userId, Integer companyType, Integer current, Integer size) {
        PageResult<List<ImChatNoticeSubRecord>> result = imClient.getNoticeSubRecordPage(chatId, deviceType, companyType, startTime, endTime, userId, current, size);
        if (result.isPresent()) {
            List<ImChatNoticeSubRecordResp> respList = new ArrayList<>();
            Set<Integer> userSet = result.getData().stream().map(ImChatNoticeSubRecord::getUserId).collect(Collectors.toSet());
            Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userSet)).orElse(null);
            for (ImChatNoticeSubRecord record : result.getData()) {
                ImChatNoticeSubRecordResp resp = new ImChatNoticeSubRecordResp();
                resp.setId(record.getId());
                resp.setUserId(record.getUserId());
                resp.setIsCancel(record.getIsCancel());
                resp.setGmtCreate(record.getGmtCreate());
                if (usersMap.containsKey(resp.getUserId())) {
                    resp.setNickname(usersMap.get(resp.getUserId()).getNickName());
                }
                if (ObjectUtil.isNotEmpty(record.getNoticeType())) {
                    resp.setNoticeType(record.getNoticeType());
                }
                respList.add(resp);
            }
            return PageResult.success(respList, result.getPagination());
        }
        return PageResult.empty();
    }

    public PageResult<List<TdStockCaseChannel>> getChatStockCaseChannelList(Integer companyType, Integer current, Integer size) {
        BaseResult<List<ImChatRoom>> baseResult = imClient.filterChatRoomList(companyType, null, null, null, true);
        List<Long> caseChannelIds = new ArrayList<>();
        if (baseResult.isPresent()) {
            caseChannelIds = baseResult.getData().stream().map(ImChatRoom::getCaseChannelId).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
        }
        return tradeClient.getChatStockCaseChannelList(caseChannelIds, SCT_PORTFOLIO, companyType, current, size);
    }

    public PageResult<List<ChatBlackInfoResp>> getChatBlackList(Long chatId, String searchContent, Integer current, Integer size) {
        PageResult<List<ImChatBlackInfo>> chatBlackList = imClient.getChatBlackList(chatId, searchContent, current, size);
        if (ObjectUtil.isEmpty(chatBlackList.getData())) {
            return PageResult.success(null, chatBlackList.getPagination());
        }
        List<Integer> userIdList = chatBlackList.getData().stream().filter(Objects::nonNull).map(ImChatBlackInfo::getUserId).collect(Collectors.toList());
        List<UserAndWxInfoResp> userAndWxInfoRespList = userClient.getUserAndWxInfoBatch(userIdList).orElse(new ArrayList<>());
        Map<Integer, UserAndWxInfoResp> respMap = new HashMap<>();
        userAndWxInfoRespList.forEach(i -> {
            if (i != null && i.getUcUsers() != null) {
                respMap.put(i.getUcUsers().getId(), i);
            }
        });
        // Map<Integer, UserAndWxInfoResp> respMap = userAndWxInfoRespList.stream().collect(Collectors.toMap(i -> i.getUcUsers().getId(), i -> i));
        List<ChatBlackInfoResp> respList = chatBlackList.getData().stream().filter(Objects::nonNull).map(info -> {
            UserAndWxInfoResp infoResp = respMap.getOrDefault(info.getUserId(), new UserAndWxInfoResp());
            UcUsers ucUsers = infoResp.getUcUsers() != null ? infoResp.getUcUsers() : new UcUsers();
            UcWxExt ucWxExt = infoResp.getUcWxExt() != null ? infoResp.getUcWxExt() : new UcWxExt();
            return ChatBlackInfoResp.builder()
                    .id(info.getId())
                    .createTime(info.getGmtModified())
                    .userCode(DesensitizeUtil.idToMask(info.getUserId()))
                    .avatarUrl(ucUsers.getAvatarUrl())
                    .nickName(ucUsers.getNickName())
                    .maskMobile(ucUsers.getMaskMobile())
                    .wxId(ucWxExt.getId())
                    .remark(info.getRemark())
                    .build();
        }).collect(Collectors.toList());
        return PageResult.success(respList, chatBlackList.getPagination());
    }

    public List<ChatUserBlackInfoResp> searchChatBlackInfo(Long chatId, String searchContent) {
        List<UserAndWxInfoResp> userAndWxInfoRespList = userClient.getUserAndWxInfo(searchContent).orElseThrow();
        List<Integer> userIdList = userAndWxInfoRespList.stream()
                .filter(o -> o != null && o.getUcUsers() != null)
                .map(o -> o.getUcUsers().getId())
                .collect(Collectors.toList());
        List<ImChatBlackInfo> imChatBlackInfos = imClient.getChatBlackListByUserList(chatId, userIdList).orElse(new ArrayList<>());
        Map<Integer, ImChatBlackInfo> infoMap = new HashMap<>();
        imChatBlackInfos.forEach(i -> {
            if (i != null) {
                infoMap.put(i.getUserId(), i);
            }
        });
        // Map<Integer, ImChatBlackInfo> infoMap = imChatBlackInfos.stream().collect(Collectors.toMap(ImChatBlackInfo::getUserId, i -> i));
        return userAndWxInfoRespList.stream().filter(Objects::nonNull)
                .map(o -> {
                    UcUsers ucUsers = o.getUcUsers() != null ? o.getUcUsers() : new UcUsers();
                    UcWxExt ucWxExt = o.getUcWxExt() != null ? o.getUcWxExt() : new UcWxExt();
                    ImChatBlackInfo blackInfo = infoMap.getOrDefault(ucUsers.getId(), new ImChatBlackInfo());
                    return ChatUserBlackInfoResp.builder()
                            .userCode(DesensitizeUtil.idToMask(ucUsers.getId()))
                            .nickName(ucUsers.getNickName())
                            .wxNickname(ucWxExt.getNickname())
                            .avatarUrl(ucUsers.getAvatarUrl())
                            .id(blackInfo.getId())
                            .enabled(ObjectUtil.equal(blackInfo.getEnabled(), true))
                            .build();
                }).collect(Collectors.toList());
    }

    public BaseResult<List<ChatBlackModifyRecordResp>> getBlackOperateRecord(Long chatId, String userCode) {
        if (!DesensitizeUtil.isValidUserCode(userCode)) {
            throw new BusinessException("用户编码格式错误");
        }
        int userId = DesensitizeUtil.maskToId(userCode);
        List<ImChatBlackRecord> imChatBlackRecords = imClient.getChatBlackOperateRecord(chatId, userId).orElseThrow();
        List<Integer> operatorIdList = imChatBlackRecords.stream().filter(Objects::nonNull)
                .map(ImChatBlackRecord::getOperatorId).collect(Collectors.toList());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(operatorIdList)).orElse(new HashMap<>());
        return BaseResult.success(imChatBlackRecords.stream().map(record -> {
            Integer operatorId = record.getOperatorId();
            UcUsers operator = usersMap.getOrDefault(operatorId, new UcUsers());
            return ChatBlackModifyRecordResp.builder()
                    .operatorId(operatorId)
                    .operatorName(operator.getRealName())
                    .operatorAvatarUrl(operator.getAvatarUrl())
                    .remark(record.getRemark())
                    .enabled(record.getEnabled())
                    .gmtCreate(record.getGmtCreate())
                    .userCode(DesensitizeUtil.idToMask(record.getUserId()))
                    .build();
        }).collect(Collectors.toList()));
    }
}
