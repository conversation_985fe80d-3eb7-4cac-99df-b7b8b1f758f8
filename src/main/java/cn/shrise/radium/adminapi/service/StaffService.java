package cn.shrise.radium.adminapi.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.constant.ErrorConstant;
import cn.shrise.radium.authservice.AuthClient;
import cn.shrise.radium.authservice.req.AdminLoginReq;
import cn.shrise.radium.authservice.resp.AdminLoginResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.entity.MainCompany;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.properties.CompanyProperties;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.*;
import cn.shrise.radium.userservice.req.ModifyPasswordReq;
import cn.shrise.radium.userservice.req.SwitchPasswordReq;
import cn.shrise.radium.userservice.req.UpdateStaffReq;
import cn.shrise.radium.userservice.resp.StaffInfoResp;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.resp.ContactUserRelationResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static cn.shrise.radium.userservice.constant.UserErrorCode.PASSWORD_CANNOT_CONTAIN_USERNAME;
import static cn.shrise.radium.userservice.constant.UserTypeConstant.STAFF;

@Slf4j
@Service
@RequiredArgsConstructor
public class StaffService {

    private final AuthClient authClient;
    private final UserClient userClient;

    private final WorkwxClient workwxClient;
    private final AuthService authService;
    private final CompanyProperties companyProperties;

    /*public void resetPassword(Integer companyType, String userName, String oldPassword, String newPassword) {
        if (isContainUserName(userName, newPassword)) {
            throw new BusinessException(PASSWORD_CANNOT_CONTAIN_USERNAME);
        }
        if (Objects.equals(oldPassword, newPassword)) {
            throw new BusinessException(ErrorConstant.RESET_PASSWORD_REPEAT);
        }

        AdminLoginReq req = AdminLoginReq.builder()
                .companyType(companyType)
                .userName(userName)
                .password(oldPassword)
                .build();
        final BaseResult<AdminLoginResp> loginResult = authClient.adminLogin(req);

        if (!loginResult.isSuccess()) {
            throw new BusinessException(loginResult);
        }
        final AdminLoginResp loginResp = loginResult.getData();
        final UcMainStaff staff = loginResp.getStaff();

        UpdateStaffReq updateStaffReq = UpdateStaffReq.builder()
                .password(newPassword)
                .resetPassword(false)
                .build();

        final BaseResult<UcMainStaff> updateStaffResult = userClient.updateStaff(staff.getId(), updateStaffReq);

        if (!updateStaffResult.isSuccess()) {
            throw new BusinessException(updateStaffResult);
        }

    }*/

    public BaseResult<Boolean> updatePassword(Integer companyType, Integer userId, String oldPassword, String newPassword) {
        UcMainStaff staff = userClient.getStaffByUserId(companyType, userId).orElseThrow(RecordNotExistedException::new);
        if (isContainUserName(staff.getUserName(), newPassword)) {
            throw new BusinessException(PASSWORD_CANNOT_CONTAIN_USERNAME);
        }

        if (Objects.equals(oldPassword, newPassword)) {
            throw new BusinessException(ErrorConstant.RESET_PASSWORD_REPEAT);
        }

        if (ObjectUtil.isEmpty(userId)) {
            throw new BusinessException("员工id不能为空");
        }

        return userClient.updatePassword(userId, oldPassword, newPassword);
    }

    public BaseResult<Boolean> modifyPassword(ModifyPasswordReq req) {
        MainCompany mainCompany = companyProperties.getMainCompanyFromCompanyType(req.getCompanyType())
                .orElseThrow(RecordNotExistedException::new);
        UcMainStaff staff = userClient.getStaff(mainCompany.getCompanyType(), req.getMobile(), null)
                .orElseThrow(RecordNotExistedException::new);
        if (isContainUserName(staff.getUserName(), req.getNewPassword())) {
            throw new BusinessException(PASSWORD_CANNOT_CONTAIN_USERNAME);
        }

        authService.checkValidateCode(req.getMobile(), req.getValidateCode());
        return userClient.modifyPassword(req);
    }

    public BaseResult<Boolean> switchPassword(SwitchPasswordReq req) {
        if (isContainUserName(req.getUserName(), req.getNewPassword())) {
            throw new BusinessException(PASSWORD_CANNOT_CONTAIN_USERNAME);
        }
        return userClient.switchPassword(req);
    }

    public StaffInfoResp getStaffInfo(Integer staffId) {
        BaseResult<UcUsers> userResult = userClient.getUser(staffId);
        if (userResult.isPresent()) {
            UcUsers users = userResult.getData();
            if (users.getUserType().equals(STAFF)) {
                Integer companyType = users.getCompanyType();
                StaffInfoResp staffInfo = userClient.getStaffInfo(staffId, companyType).getData();
                BaseResult<UcRole> roleResult = userClient.getRoleInfo(staffId);
                staffInfo.setRealName(users.getRealName());
                staffInfo.setNickname(users.getNickName());
                BaseResult<List<UcPermissionPackage>> packageResult = userClient.getUserPermissionPackageList(staffId, true);
                if (roleResult.isPresent()) {
                    staffInfo.setRoleName(roleResult.getData().getName());
                }
                if (packageResult.isPresent()) {
                    staffInfo.setPermissionPackageList(packageResult.getData());
                }
                return staffInfo;
            }
            throw new BusinessException("员工账号异常");
        }
        return null;
    }

    public PageResult<List<ContactUserRelationResp>> getFriendRelationPage(Integer salesId, Integer companyType, Integer current, Integer size) {
        return workwxClient.getFriendRelationPage(salesId, companyType, null, current, size);
    }

    public BaseResult<Boolean> checkLoginStaff(Integer staffId, Integer userId, String appId, String deviceNumber) {
        UcStaffDevice device = userClient.findStaffLoginDevice(staffId, appId, deviceNumber).getData();
        UcUsers user = userClient.getUser(userId).getData();
        UcMainStaff staff = userClient.getStaff(staffId).getData();
        return BaseResult.success(device.getEnabled() && user.getEnabled() && staff.getEnabled());
    }

    public Boolean isContainUserName(String userName, String passWord) {
        String lowerCasePassword = passWord.toLowerCase();
        String lowerCaseUserName = userName.toLowerCase();
        return lowerCasePassword.contains(lowerCaseUserName);
    }

}
