package cn.shrise.radium.adminapi.service;

import cn.shrise.radium.adminapi.resp.TrackEventStatResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcTrackPage;
import cn.shrise.radium.userservice.lindorm.entity.LdCustomerTrackEventStat;
import cn.shrise.radium.userservice.resp.AppPageInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class TrackService {

    private final UserClient userClient;


    public BaseResult<List<TrackEventStatResp>> getCustomerTrackEventStat(String appId, String pageId, Long startTime, Long endTime) {

        BaseResult<List<LdCustomerTrackEventStat>> result = userClient.getCustomerTrackEventStat(appId, pageId, startTime, endTime);
        if (result.isFail()){
            throw new BusinessException(result);
        }
        if (!result.isPresent()){
            return BaseResult.success(Collections.EMPTY_LIST);
        }
        AppPageInfoResp pageInfoResp = userClient.getAppPageInfo(appId).orElseThrow();
        List<UcTrackPage> pages = pageInfoResp.getPageList();
        Map<String, String> pageMap = pages.stream().collect(Collectors.toMap(UcTrackPage::getPageId, UcTrackPage::getName));
        List<LdCustomerTrackEventStat> resultData = result.getData();
        List<TrackEventStatResp> resps = resultData.stream().map(track -> {
            TrackEventStatResp resp = new TrackEventStatResp();
            BeanUtils.copyProperties(track, resp);
            resp.setPageName(pageMap.getOrDefault(resp.getPageId(), null));
            return resp;
        }).collect(Collectors.toList());
        return BaseResult.success(resps);
    }
}
