package cn.shrise.radium.adminapi.service.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.entity.SkuActivityInfo;
import cn.shrise.radium.adminapi.entity.SkuInfo;
import cn.shrise.radium.adminapi.resp.SalesSkuItem;
import cn.shrise.radium.adminapi.resp.order.*;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.properties.CommonProperties;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.req.GetOrCreateUserSkuInviteUrlReq;
import cn.shrise.radium.marketingservice.resp.UserSkuInviteUrlResp;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.dto.SkuDto;
import cn.shrise.radium.orderservice.entity.*;
import cn.shrise.radium.orderservice.properties.vip.SkuVipPackage;
import cn.shrise.radium.orderservice.properties.vip.VipPackage;
import cn.shrise.radium.orderservice.req.*;
import cn.shrise.radium.adminapi.resp.order.ProductLevelAgeRecordResp;
import cn.shrise.radium.adminapi.resp.order.ProductLevelAgeResp;
import cn.shrise.radium.orderservice.resp.SkuAndMaterialResp;
import cn.shrise.radium.orderservice.resp.SkuStrategy;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcDepartment;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.wxservice.WxClient;
import cn.shrise.radium.wxservice.constant.WxAuthorizerCategoryConstant;
import cn.shrise.radium.wxservice.properties.WxMpProperties;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.orderservice.constant.SkuChangeTypeConstant.CONF_SERVICE;
import static cn.shrise.radium.orderservice.constant.SkuChangeTypeConstant.DEL_SERVICE;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SkuService {

    private final OrderClient orderClient;

    public final UserClient userClient;
    private final MarketingClient marketingClient;
    private final WxClient wxClient;
    private final CommonProperties commonProperties;

    public PageResult<List<SkuFullInfoResp>> getSkuList(SkuManageReq req) {
        PageResult<List<RsSku>> result = orderClient.getSkuList(req);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (ObjectUtil.isEmpty(result.getData())) {
            return PageResult.empty();
        }
        List<RsSku> skuList = result.getData();
        List<Integer> skuIdList = skuList.stream().map(RsSku::getId).collect(Collectors.toList());
        List<SkuStrategy> strategyList = orderClient.batchSkuStrategyBySku(BatchReq.of(skuIdList)).orElseThrow();
        Map<Integer, SkuStrategy> strategyMap = strategyList.stream().collect(Collectors.toMap(SkuStrategy::getSkuId, Function.identity()));
        List<SkuFullInfoResp> resps = skuList.stream().map(e -> {
            SkuFullInfoResp resp = new SkuFullInfoResp();
            BeanUtil.copyProperties(e, resp);
            resp.setSkuStrategy(strategyMap.getOrDefault(e.getId(), null));
            return resp;
        }).collect(Collectors.toList());
        return PageResult.success(resps, result.getPagination());
    }

    public BaseResult<RsSku> createSku(CreateSkuAndSkuMaterialReq req) {
        return orderClient.createSku(req);
    }

    public List<SkuChangeRecordResp> getSkuChangeRecord(Integer skuId, Integer companyType) {
        List<Integer> changeTypes = Arrays.asList(CONF_SERVICE, DEL_SERVICE);
        List<VipPackage> vipPackages = orderClient.getVipPackageList(companyType).orElse(null);
        HashMap<String, String> packageMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(vipPackages)) {
            Map<String, String> map = vipPackages.stream().collect(Collectors.toMap(VipPackage::getNumber, VipPackage::getName));
            packageMap.putAll(map);
        }
        BaseResult<List<SkuChangeRecord>> result = orderClient.getSkuChangeRecord(skuId);
        List<SkuChangeRecordResp> skuChangeRecordRespList = new ArrayList<>();
        if (result.isSuccess()) {
            Set<Integer> collect = result.getData().stream().map(SkuChangeRecord::getOperatorId).filter(Objects::nonNull).collect(Collectors.toSet());
            BaseResult<List<UcUsers>> userResult = userClient.batchGetUserList(BatchReq.create(collect));
            if (userResult.isSuccess()) {
                Map<Integer, UcUsers> map = userResult.getData().stream().collect(Collectors.toMap(UcUsers::getId, x -> x, (oldVal, newVal) -> newVal));
                for (SkuChangeRecord skuChangeRecord : result.getData()) {
                    if (skuChangeRecord.getOperatorId() == null) {
                        SkuChangeRecordResp build = SkuChangeRecordResp.builder()
                                .changeType(skuChangeRecord.getChangeType())
                                .id(skuChangeRecord.getId())
                                .skuId(skuChangeRecord.getSkuId())
                                .createTime(skuChangeRecord.getCreateTime())
                                .operatorName("系统")
                                .build();
                        if (changeTypes.contains(skuChangeRecord.getChangeType())) {
                            build.setPackageNumber(skuChangeRecord.getNumber());
                            build.setPackageName(packageMap.getOrDefault(skuChangeRecord.getNumber(), null));
                            build.setPeriod(skuChangeRecord.getPeriod());
                        }
                        skuChangeRecordRespList.add(build);
                    }
                    if (map.containsKey(skuChangeRecord.getOperatorId())) {
                        SkuChangeRecordResp build = SkuChangeRecordResp.builder()
                                .changeType(skuChangeRecord.getChangeType())
                                .id(skuChangeRecord.getId())
                                .skuId(skuChangeRecord.getSkuId())
                                .createTime(skuChangeRecord.getCreateTime())
                                .operatorId(skuChangeRecord.getOperatorId())
                                .operatorName(map.get(skuChangeRecord.getOperatorId()).getRealName())
                                .avatarUrl(map.get(skuChangeRecord.getOperatorId()).getAvatarUrl())
                                .build();
                        if (changeTypes.contains(skuChangeRecord.getChangeType())) {
                            build.setPackageNumber(skuChangeRecord.getNumber());
                            build.setPackageName(packageMap.getOrDefault(skuChangeRecord.getNumber(), null));
                            build.setPeriod(skuChangeRecord.getPeriod());
                        }
                        skuChangeRecordRespList.add(build);
                    }
                }
            }
        }
        return skuChangeRecordRespList;
    }

    public BaseResult<SkuAndMaterialResp> getSkuAndMaterial(Integer skuId) {
        return orderClient.getSkuAndMaterial(skuId);
    }

    public BaseResult<Boolean> updateSku(UpdateSkuReq req) {
        return orderClient.updateSku(req);
    }

    public BaseResult<Boolean> setUserVisible(SkuUserVisibleReq req) {
        return orderClient.setUserVisible(req);
    }

    public BaseResult<Boolean> setDeptVisible(SkuDeptVisibleReq req) {
        return orderClient.setDeptVisible(req);
    }

    public BaseResult<Boolean> onSale(Integer skuId, Integer operatorId) {
        return orderClient.onSale(skuId, operatorId);
    }

    public BaseResult<Boolean> offSale(Integer skuId, Integer operatorId) {
        return orderClient.offSale(skuId, operatorId);
    }

    public BaseResult<Boolean> configService(SkuServiceReq req) {
        return orderClient.configService(req);
    }

    public List<SkuUserVisibleResp> getUserList(Integer skuId) {
        BaseResult<List<RsSkuUserRelation>> baseResult = orderClient.getUserList(skuId);
        Set<Integer> userList = baseResult.getData().stream().map(RsSkuUserRelation::getUserId).collect(Collectors.toSet());
        Map<Integer, UcUsers> userMap = userClient.batchGetUserMap(BatchReq.create(userList)).getData();
        List<SkuUserVisibleResp> respList = new ArrayList<>();
        for (RsSkuUserRelation record : baseResult.getData()) {
            SkuUserVisibleResp build = SkuUserVisibleResp.builder()
                    .userId(record.getUserId())
                    .skuId(record.getSkuId())
                    .createTime(record.getCreateTime())
                    .build();
            if (userMap.containsKey(record.getUserId())) {
                build.setUserName(userMap.get(record.getUserId()).getNickName());
            }
            respList.add(build);
        }
        return respList;
    }

    public List<SkuDeptVisibleResp> getDeptList(Integer skuId) {
        BaseResult<List<SkuDeptRelation>> baseResult = orderClient.getDeptList(skuId);
        List<Integer> deptIdList = baseResult.getData().stream().map(SkuDeptRelation::getDepartment).collect(Collectors.toList());
        List<UcDepartment> deptList = userClient.batchGetDeptList(deptIdList).getData();
        List<SkuDeptVisibleResp> respList = new ArrayList<>();
        Map<Integer, UcDepartment> deptMap = deptList.stream().collect(Collectors.toMap(UcDepartment::getId, x -> x, (oldVal, newVal) -> newVal));
        for (SkuDeptRelation record : baseResult.getData()) {
            SkuDeptVisibleResp build = SkuDeptVisibleResp.builder()
                    .id(record.getId())
                    .deptId(record.getDepartment())
                    .skuId(record.getSku())
                    .createTime(record.getGmtCreate())
                    .build();
            if (deptMap.containsKey(record.getDepartment())) {
                build.setDeptName(deptMap.get(record.getDepartment()).getName());
            }
            respList.add(build);
        }
        return respList;
    }

    public PageResult<List<SalesSkuItem>> getUserSkuList(Integer userId, Integer status, String skuName, Integer current, Integer size) {
        PageResult<List<SkuDto>> result = orderClient.getUserSkuList(userId, status, skuName, current, size);
        if (result.isFail()) {
            throw new BusinessException(result);
        }

        List<SkuDto> skuList = result.getData();

        List<SalesSkuItem> items = skuList.stream().map(s -> SalesSkuItem.builder()
                        .skuInfo(SkuInfo.of(s.getSku()))
                        .activityInfo(SkuActivityInfo.of(s.getSkuActivity()))
                        .build())
                .collect(Collectors.toList());
        return PageResult.success(items, result.getPagination());
    }

    public UserSkuInviteUrlResp getSkuUrl(Integer companyType, Integer userId, Integer skuId) {
        List<WxMpProperties> authorizerConfig = wxClient.getAuthorizerConfig(companyType, WxAuthorizerCategoryConstant.PRODUCT)
                .orElseThrow();

        if (ObjectUtils.isEmpty(authorizerConfig)) {
            throw new RecordNotExistedException("找不到当前公司的产品授权号");
        }

        WxMpProperties wxMpProperties = authorizerConfig.get(0);

        GetOrCreateUserSkuInviteUrlReq req = GetOrCreateUserSkuInviteUrlReq.builder()
                .accountType(wxMpProperties.getAccountType())
                .companyType(companyType)
                .salesId(userId)
                .skuId(skuId)
                .build();
        BaseResult<UserSkuInviteUrlResp> getInviteUrlResult = marketingClient.getOrCreateUserSkuInviteUrl(req);
        if (getInviteUrlResult.isFail()) {
            throw new BusinessException(getInviteUrlResult);
        }
        return getInviteUrlResult.getData();
    }

    public String getSkuInviteUrl(Integer companyType, Integer userId, Integer skuId) {
        String number = getSkuUrl(companyType, userId, skuId).getNumber();
        return UriComponentsBuilder.fromUriString(commonProperties.getGcPayShortUrl())
                .path(String.format("/i/%s", number))
                .build()
                .toUriString();
    }

    public BaseResult<RsSku> createPromotionSku(CreatePromotionSkuReq req) {
        return orderClient.createPromotionSku(req);
    }

    public BaseResult<Boolean> updatePromotionSku(UpdatePromotionSkuReq req) {
        return orderClient.updatePromotionSku(req);
    }

    public List<SkuVipPackage> getSkuVipPackageList(Integer companyType, Integer skuId) {
        return orderClient.getSkuVipPackageList(companyType, skuId).orElse(Collections.emptyList());
    }

    public BaseResult<Void> createOrUpdateSkuVipSubscription(CreateOrUpdateSkuVipSubscriptionReq req) {
        return orderClient.createOrUpdateSkuVipSubscription(req);
    }

    public PageResult<List<SkuResp>> getSkuList(Integer companyType, Integer status, Integer productLevel, String content, Boolean isFilterStrategy, Integer current, Integer size) {
        if (ObjectUtil.isEmpty(companyType)) {
            throw new BusinessException("公司类型不能为空");
        }
        List<SkuResp> list = new ArrayList<>();
        PageResult<List<RsSku>> result = orderClient.getPage(companyType, status, productLevel, content, isFilterStrategy, current, size);
        if (result.isPresent()) {
            List<RsSku> skuList = result.getData();
            for (RsSku sku : skuList) {
                list.add(SkuResp.of(sku));
            }
            return PageResult.success(list, result.getPagination());
        }
        return PageResult.empty();
    }

    public List<ProductLevelAgeResp> getProductLevelAgeList() {
        BaseResult<List<SkuProductLevelAge>> productLevelAgeListRes = orderClient.getProductLevelAgeList();
        if (!productLevelAgeListRes.isPresent() || ObjectUtil.isEmpty(productLevelAgeListRes.getData())) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(productLevelAgeListRes.getData(), ProductLevelAgeResp.class);
    }

    public List<ProductLevelAgeRecordResp> getProductLevelAgeRecordList(Integer productLevel) {
        BaseResult<List<SkuProductLevelAgeRecord>> productLevelAgeRecordListRes = orderClient.getProductLevelAgeRecordList(productLevel);
        if (!productLevelAgeRecordListRes.isPresent() || ObjectUtil.isEmpty(productLevelAgeRecordListRes.getData())) {
            return Collections.emptyList();
        }
        List<SkuProductLevelAgeRecord> productLevelAgeRecordList = productLevelAgeRecordListRes.getData();
        Set<Integer> userIdSet = productLevelAgeRecordList.stream()
                .map(SkuProductLevelAgeRecord::getOperatorId)
                .collect(Collectors.toSet());
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).getData();
        List<ProductLevelAgeRecordResp> productLevelAgeRecordResps = productLevelAgeRecordList.stream()
                .map(e -> ProductLevelAgeRecordResp.builder()
                        .id(e.getId())
                        .gmtCreate(e.getGmtCreate())
                        .gmtModified(e.getGmtModified())
                        .productLevel(e.getProductLevel())
                        .content(e.getContent())
                        .operatorName(usersMap.getOrDefault(e.getOperatorId(), new UcUsers()).getRealName())
                        .avatarUrl(usersMap.getOrDefault(e.getOperatorId(), new UcUsers()).getAvatarUrl())
                        .build()
                ).collect(Collectors.toList());
        return productLevelAgeRecordResps;
    }

}
