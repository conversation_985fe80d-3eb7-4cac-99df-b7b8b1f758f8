package cn.shrise.radium.adminapi.service.user;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.shrise.radium.adminapi.constant.CustomerSearchTypeEnum;
import cn.shrise.radium.adminapi.entity.UserInfo;
import cn.shrise.radium.adminapi.entity.WxInfo;
import cn.shrise.radium.adminapi.resp.SearchCustomerAggregateItem;
import cn.shrise.radium.adminapi.resp.SearchCustomerItem;
import cn.shrise.radium.adminapi.resp.SearchWorkWxRelationItem;
import cn.shrise.radium.adminapi.resp.user.*;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.common.util.StringUtil;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.entity.RsPayCompany;
import cn.shrise.radium.orderservice.entity.VipSubscriptionRecord;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.*;
import cn.shrise.radium.userservice.error.UsErrorCode;
import cn.shrise.radium.userservice.req.GlobalSearchFakeReq;
import cn.shrise.radium.userservice.req.SetCustomerWhitelistReq;
import cn.shrise.radium.userservice.resp.*;
import cn.shrise.radium.workwxservice.WorkwxClient;
import cn.shrise.radium.workwxservice.bean.WorkWxFullContactUserRelation;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContact;
import cn.shrise.radium.workwxservice.entity.NpWorkWxContactUserRelation;
import cn.shrise.radium.workwxservice.entity.NpWorkWxUser;
import cn.shrise.radium.workwxservice.entity.NpWwxUnionTag;
import cn.shrise.radium.wxservice.WxClient;
import cn.shrise.radium.wxservice.entity.UcWxExt;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.constant.ProductTypeConstant.WEB_ADMIN;
import static cn.shrise.radium.adminapi.constant.CustomerSearchTypeEnum.USER_CODE;
import static cn.shrise.radium.contentservice.constant.IdVerifyStatusConstant.PASS;
import static cn.shrise.radium.userservice.constant.IdVerifyType.IVT_THREE;
import static cn.shrise.radium.userservice.constant.IdentityTypeConstant.ID_CARD;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerService {

    private final UserClient userClient;
    private final WxClient wxClient;
    private final OrderClient orderClient;
    private final WorkwxClient workwxClient;
    private final ContentClient contentClient;

    public BaseResult<List<UcIdCardRecord>> getRecord(String mobile) {
        BaseResult<List<UcIdCardRecord>> result = userClient.getRecord(mobile);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (!result.isPresent()) {
            return result;
        }
        List<UcIdCardRecord> records = result.getData();
        List<UcIdCardRecord> resps = records.stream().peek(r -> {
            if (ObjectUtil.isNotEmpty(r.getIdNumber())) {
                r.setIdNumber(DesensitizedUtil.idCardNum(r.getIdNumber(), 4, 4));
            }
        }).collect(Collectors.toList());
        return BaseResult.success(resps);
    }

    public BaseResult<UsErrorCode> unbindWx(Integer wxId, Integer operatorId) {
        return userClient.unbindWx(null, wxId, operatorId, WEB_ADMIN);
    }

    public BaseResult<List<CustomerInterestTagResp>> findCustomerInterestTag(String userCode) {
        int userId = DesensitizeUtil.maskToId(userCode);
        ArrayList<Integer> userList = new ArrayList<>();
        userList.add(userId);
        PageResult<List<VipSubscriptionRecord>> recordList = orderClient.getVipSubscriptionRecordList(userList, null, null, null, null, null, null);
        if (recordList.isFail()) {
            throw new BusinessException(recordList);
        }
        if (!recordList.isPresent()) {
            return BaseResult.success(Collections.EMPTY_LIST);
        }
        Optional<VipSubscriptionRecord> first = recordList.getData().stream().filter(e -> ObjectUtil.equal(e.getLevel(), 1)).findFirst();
        if (!first.isPresent()) {
            return BaseResult.success(Collections.EMPTY_LIST);
        }
        return userClient.findCustomerInterestTag(userId);
    }

    public PageResult<List<SearchCustomerAggregateItem>> searchCustomerAggregate(Integer companyType, String appName, String keyword, Integer current, Integer size, boolean isGlobalSearch) {
        OpenSearchResult<CustomerAggregateItem> result = userClient.searchCustomerAggregate(appName, companyType, keyword, current, size, isGlobalSearch).orElse(null);
        if (result == null) {
            return PageResult.empty(current, size);
        }

        List<CustomerAggregateItem> items = result.getItems();
        long total = result.getTotal();

        if (total == 0) {
            return PageResult.empty(current, size);
        }
        Set<Integer> userIdSet = items.stream().map(CustomerAggregateItem::getUserId).collect(Collectors.toSet());
        Set<Integer> wxIdSet = items.stream().map(CustomerAggregateItem::getWxId).collect(Collectors.toSet());

        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(userIdSet)).orElse(Collections.emptyMap());
        List<UcWxExt> wxExtList = wxClient.batchGetUserWxExt(null, new ArrayList<>(wxIdSet)).orElse(Collections.emptyList());
        Map<Integer, UcWxExt> wxExtMap = wxExtList.stream().collect(Collectors.toMap(UcWxExt::getId, Function.identity()));

        Set<Integer> relationSet = items.stream()
                .map(CustomerAggregateItem::getRelationId)
                .map(this::safeSplit)
                .flatMap(Arrays::stream)
                .map(Integer::valueOf)
                .collect(Collectors.toSet());

        List<WorkWxFullContactUserRelation> userRelations = workwxClient.getFullFriendRelationList(BatchReq.of(relationSet)).orElse(Collections.emptyList());
        Map<Integer, WorkWxFullContactUserRelation> relationMap = userRelations.stream().collect(Collectors.toMap(e -> e.getRelation().getPkId(), Function.identity()));

        List<SearchCustomerAggregateItem> aggregateItems = items.stream().map(item -> {
            Integer customerId = item.getUserId();
            Integer wxId = item.getWxId();
            String unionId = item.getUnionId();
            String[] relationIdStrings = safeSplit(item.getRelationId());
            List<Integer> relationIds = Arrays.stream(relationIdStrings).map(Integer::valueOf).collect(Collectors.toList());
            List<WorkWxFullContactUserRelation> relationList = relationIds.stream().map(relationMap::get).collect(Collectors.toList());


            List<SearchWorkWxRelationItem> relationItems = relationList.stream().filter(Objects::nonNull).map(full -> {
                NpWorkWxContactUserRelation relation = full.getRelation();
                NpWorkWxContact contact = full.getContact();
                NpWorkWxUser workWxUser = full.getWorkWxUser();
                String externalUserId = relation.getExternalUserId();
                String wxAccount = relation.getWxAccount();

                String contactName = Objects.nonNull(contact) ? contact.getName() : null;
                String wxAccountName = Objects.nonNull(workWxUser) ? workWxUser.getName() : null;

                return SearchWorkWxRelationItem.builder()
                        .id(relation.getPkId())
                        .accountType(relation.getAccountType())
                        .contactName(contactName)
                        .wxAccountName(wxAccountName)
                        .externalUserId(externalUserId)
                        .wxAccount(wxAccount)
                        .cropName(full.getCorpName())
                        .remark(relation.getRemark())
                        .addTime(relation.getAddTime())
                        .enabled(relation.getContactEnabled() != null && relation.getUserEnabled() != null && (relation.getContactEnabled() && relation.getUserEnabled()))
                        .chatCount(full.getChatCount())
                        .build();
            }).collect(Collectors.toList());

            relationItems = relationItems.stream()
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(SearchWorkWxRelationItem::getAddTime, Comparator.nullsLast(Comparator.reverseOrder())))
                    .collect(Collectors.toList());

            UcUsers user = usersMap.getOrDefault(customerId, new UcUsers());
            UcWxExt wxUser = wxExtMap.getOrDefault(wxId, new UcWxExt());

            UserInfo userInfo = UserInfo.of(user);
            WxInfo wxInfo = WxInfo.of(wxUser);

            return SearchCustomerAggregateItem.builder()
                    .id(item.getId())
                    .companyType(companyType)
                    .userId(customerId)
                    .userCode(DesensitizeUtil.idToMask(customerId))
                    .wxId(wxId)
                    .relationList(relationItems)
                    .unionId(unionId)
                    .userInfo(userInfo)
                    .wxInfo(wxInfo)
                    .build();
        }).collect(Collectors.toList());

        return PageResult.success(aggregateItems, current, size, total);
    }

    private String[] safeSplit(String str) {
        if (StringUtil.isEmpty(str)) {
            return new String[0];
        }
        return str.split("\\|");
    }

    public PageResult<List<CustomerWhitelistOperateRecordResp>> getCustomerWhitelistOperateRecordList(String userCode, Integer current, Integer size) {
        int userId = DesensitizeUtil.maskToId(userCode);
        PageResult<List<UcCustomerWhitelistOperateRecord>> pageResult = userClient.getWhitelistOperateRecordList(userId, current, size);
        if (pageResult.isPresent()) {
            List<UcCustomerWhitelistOperateRecord> operateRecordList = pageResult.getData();
            Set<Integer> operatorSet = operateRecordList.stream().map(UcCustomerWhitelistOperateRecord::getOperatorId).collect(Collectors.toSet());
            Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.of(operatorSet)).orElse(Collections.emptyMap());
            List<CustomerWhitelistOperateRecordResp> respList = new ArrayList<>();
            operateRecordList.forEach(operateRecord -> {
                UcUsers ucUsers = usersMap.getOrDefault(operateRecord.getOperatorId(), new UcUsers());
                CustomerWhitelistOperateRecordResp resp = CustomerWhitelistOperateRecordResp.builder()
                        .id(operateRecord.getId())
                        .userId(operateRecord.getUserId())
                        .userCode(DesensitizeUtil.idToMask(operateRecord.getUserId()))
                        .gmtCreate(operateRecord.getGmtCreate())
                        .operatorId(operateRecord.getOperatorId())
                        .operatorName(ucUsers.getRealName())
                        .operatorAvatarUrl(ucUsers.getAvatarUrl())
                        .operatorContent(operateRecord.getOperatorContent())
                        .build();
                respList.add(resp);
            });
            return PageResult.success(respList, pageResult.getPagination());
        }
        return PageResult.empty(current, size);
    }

    public BaseResult<Void> setIsWhiteList(SetCustomerWhitelistReq req) {
        String userCode = req.getUserCode();
        req.setUserId(DesensitizeUtil.maskToId(userCode));
        return userClient.setIsServer(req);
    }

    public CustomerExtResp getCustomerExt(String userCode) {
        int userId = DesensitizeUtil.maskToId(userCode);
        BaseResult<UcCustomerExt> baseResult = userClient.getUserExt(userId);
        if (baseResult.isSuccess()) {
            UcCustomerExt customerExt = baseResult.getData();
            CustomerExtResp customerExtResp = CustomerExtResp.builder()
                    .id(customerExt.getId())
                    .userCode(DesensitizeUtil.idToMask(customerExt.getId()))
                    .serverId(customerExt.getServerId())
                    .remark(customerExt.getServerRemark())
                    .build();
            if (ObjectUtil.isNotEmpty(customerExt.getServerId())) {
                UcServerExt ucServerExt = userClient.getServerExtById(customerExt.getServerId()).orElse(new UcServerExt());
                UcUsers users = userClient.getUser(customerExt.getServerId()).orElse(new UcUsers());
                customerExtResp.setRealName(users.getRealName());
                customerExtResp.setWorkNumber(ucServerExt.getWorkNumber());
            }
            return customerExtResp;
        }
        return null;
    }

    public PageResult<List<SeverResp>> searchServer(String searchContent, Integer companyType, Integer current, Integer size) {
        return userClient.searchServer(searchContent, companyType, current, size);
    }

    public PageResult<List<SearchCustomerItem>> globalSearch(Integer companyType, Integer searchType, String content, Integer current, Integer size) {
        CustomerSearchTypeEnum typeEnum = CustomerSearchTypeEnum.getSearchTypeEnum(searchType);
        PageResult<List<UserAndWxInfoResp>> userAndWxInfo = null;
        PageResult<List<WorkWxFullContactUserRelation>> weComUserRelation = null;
        switch (typeEnum) {
            case USER_CODE:
                userAndWxInfo = userClient.searchUserAndWxInfo(companyType, content, null, null, null, null, null, current, size);
                break;
            case MOBILE:
                userAndWxInfo = userClient.searchUserAndWxInfo(companyType, null, content, null, null, null, null, current, size);
                break;
            case REAL_NAME:
                userAndWxInfo = userClient.searchUserAndWxInfo(companyType, null, null, content, null, null, null, current, size);
                break;
            case USER_NICK_NAME:
                userAndWxInfo = userClient.searchUserAndWxInfo(companyType, null, null, null, content, null, null, current, size);
                break;
            case WX_NICK_NAME:
                userAndWxInfo = userClient.searchUserAndWxInfo(companyType, null, null, null, null, content, null, current, size);
                break;
            case RELATION_CODE:
                if (!NumberUtil.isInteger(content)) {
                    return PageResult.empty();
                }
                weComUserRelation = workwxClient.getWeComUserRelation(companyType, Integer.valueOf(content), null, null, null, null, current, size);
                break;
            case WORK_WX_ACCOUNT_NAME:
                weComUserRelation = workwxClient.getWeComUserRelation(companyType, null, content, null, null, null, current, size);
                break;
            case WORK_WX_ACCOUNT:
                weComUserRelation = workwxClient.getWeComUserRelation(companyType, null, null, content, null, null, current, size);
                break;
            case WORK_WX_REMARK:
                weComUserRelation = workwxClient.getWeComUserRelation(companyType, null, null, null, content, null, current, size);
                break;
            case UNION_ID:
                if(content.contains(" ")){
                    return PageResult.empty();
                }
                Set<String> unionIdSet = new HashSet<>();
                unionIdSet.add(content);
                weComUserRelation = workwxClient.getWeComUserRelation(companyType, null, null, null, null, unionIdSet, current, size);
                break;
            default:
                throw new BusinessException("Invalid SearchType: " + searchType);
        }
        if (userAndWxInfo != null && userAndWxInfo.isPresent()) {
            return getSearchResultByUserWxInfo(companyType, userAndWxInfo);
        } else if (weComUserRelation != null && ObjectUtil.isNotEmpty(weComUserRelation.getData())) {
            PageResult<List<SearchCustomerItem>> searchResultByWorkWx = getSearchResultByWorkWx(companyType, weComUserRelation);
            if (typeEnum.getCode().equals(CustomerSearchTypeEnum.WORK_WX_ACCOUNT.getCode())) {
                List<String> unionIdSortList = weComUserRelation.getData().stream()
                        .map(e -> e.getContact().getUnionId())
                        .filter(ObjectUtil::isNotEmpty)
                        .collect(Collectors.toList());
                List<SearchCustomerItem> sortedList = sortCustomerItemsByUnionIdOrder(searchResultByWorkWx.getData(), unionIdSortList);
                return PageResult.success(sortedList, searchResultByWorkWx.getPagination());
            }
            return searchResultByWorkWx;
        }
        if (typeEnum.getCode().equals(CustomerSearchTypeEnum.UNION_ID.getCode())) {
            Set<String> unionIdSet = new HashSet<>();
            unionIdSet.add(content);
            userAndWxInfo = userClient.searchUserAndWxInfo(companyType, null, null, null, null, null, unionIdSet, current, size);
            if (userAndWxInfo != null && userAndWxInfo.isPresent()) {
                return getSearchResultByUserWxInfo(companyType, userAndWxInfo);
            }
        }
        return PageResult.empty();
    }

    public PageResult<List<SearchCustomerItem>> globalSearchRelation(Integer companyType, Integer userId, Integer searchType, String content, Integer current, Integer size) {
        List<SearchCustomerItem> resultList = new ArrayList<>();
        Pagination pagination = null;

        if (USER_CODE.getCode().equals(searchType) && !DesensitizeUtil.isValidUserCode(content)) {//禁用userId进行搜索
            return PageResult.empty();
        }
        PageResult<List<SearchCustomerItem>> pageResult = globalSearch(companyType, searchType, content, current, size);
        if (!pageResult.isPresent()) {
            return PageResult.empty();
        }
        resultList = pageResult.getData();
        pagination = pageResult.getPagination();
        Set<String> wxAccounts = resultList.stream()
                .filter(i -> ObjectUtil.isNotEmpty(i.getRelationList()))
                .flatMap(i -> i.getRelationList().stream()
                        .map(SearchWorkWxRelationItem::getWxAccount))
                .collect(Collectors.toSet());
        if (ObjectUtil.isNotEmpty(wxAccounts)) { //若搜索出的好友关系不为空，则根据聊天记录可见的销售来过滤好友关系
            List<NpWorkWxUser> workWxUserList = workwxClient.getAccountInfoList(wxAccounts);
            Map<String, NpWorkWxUser> belongMap = workWxUserList.stream().collect(Collectors.toMap(x -> x.getAccountType() + x.getWxAccount(), x -> x));
            Set<Integer> belongIds = new HashSet<>();
            resultList.forEach(i -> {
                if (ObjectUtil.isNotEmpty(i.getRelationList())) {
                    i.getRelationList().forEach(e -> {
                        belongIds.add(belongMap.getOrDefault(e.getAccountType() + e.getWxAccount(), new NpWorkWxUser()).getBelongId());
                    });
                }
            });
            Map<Integer, UcUsers> salesMap = userClient.batchGetUserMap(BatchReq.create(belongIds)).getData();
            Map<Integer, String> deptMap = userClient.getDeptListByUsers(belongIds).getData();
            resultList.forEach(i -> {
                if (ObjectUtil.isNotEmpty(i.getRelationList())) {
                    i.getRelationList().forEach(e -> {
                        e.setDeptName(deptMap.getOrDefault(belongMap.getOrDefault(e.getAccountType() + e.getWxAccount(), new NpWorkWxUser()).getBelongId(), null));
                        e.setSalesName(salesMap.getOrDefault(belongMap.getOrDefault(e.getAccountType() + e.getWxAccount(), new NpWorkWxUser()).getBelongId(), new UcUsers()).getRealName());
                    });
                }
            });
            if (ObjectUtil.isNotEmpty(userId)) {
                //查询当前可见聊天记录部门销售
                List<Integer> visibleSalesIds = userClient.findDeptChatVisibleSales(userId).getData();
                resultList.forEach(i -> {
                    //过滤出可见销售的好友关系
                    if (ObjectUtil.isNotEmpty(i.getRelationList())) {
                        List<SearchWorkWxRelationItem> fillterRelationList = i.getRelationList().stream().filter(e -> {
                            return visibleSalesIds.contains(belongMap.getOrDefault(e.getAccountType() + e.getWxAccount(), new NpWorkWxUser()).getBelongId());
                        }).collect(Collectors.toList());
                        i.setRelationList(fillterRelationList);
                    }
                });
            }
        }
        return PageResult.success(resultList, pagination);
    }

    private PageResult<List<SearchCustomerItem>> getSearchResultByUserWxInfo(Integer companyType, PageResult<List<UserAndWxInfoResp>> userAndWxInfo) {
        Set<String> unionIdSet = userAndWxInfo.getData().stream()
                .filter(Objects::nonNull)
                .map(UserAndWxInfoResp::getUcWxExt)
                .map(cn.shrise.radium.userservice.entity.UcWxExt::getUnionId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        Map<String, List<WorkWxFullContactUserRelation>> relationMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(unionIdSet)) {
            List<WorkWxFullContactUserRelation> workRelations = new ArrayList<>();
            int cur = 1;
            int si = 100;
            while (true) {
                PageResult<List<WorkWxFullContactUserRelation>> pageResult = workwxClient.getWeComUserRelation(companyType, null, null, null, null, unionIdSet, cur, si);
                if (pageResult.isFail()) {
                    break;
                }
                List<WorkWxFullContactUserRelation> data = pageResult.getData();
                if (ObjectUtils.isEmpty(data)) {
                    break;
                }
                workRelations.addAll(data);
                Pagination pagination = pageResult.getPagination();
                if ((long) cur * si >= pagination.getTotal()) {
                    break;
                }
                cur++;
            }
            relationMap = workRelations.stream()
                    .filter(o -> o != null && o.getContact() != null && StringUtils.isNotBlank(o.getContact().getUnionId()))
                    .collect(Collectors.groupingBy(
                            relation -> relation.getContact().getUnionId()
                    ));
        }
        List<SearchCustomerItem> items = getSearchCustomerItems(userAndWxInfo.getData(), relationMap);
        items.forEach(item -> {
            if (ObjectUtil.isNotEmpty(item.getWxInfo()) && ObjectUtil.isNotEmpty(item.getWxInfo().getUnionId())) {
                item.setUnionId(item.getWxInfo().getUnionId());
            }
            List<SearchWorkWxRelationItem> relationList = item.getRelationList();
            if (relationList != null) {
                relationList = relationList.stream()
                        .filter(Objects::nonNull)
                        .sorted(Comparator.comparing(SearchWorkWxRelationItem::getAddTime, Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList());
                item.setRelationList(relationList);
            }
        });
        return PageResult.success(items, userAndWxInfo.getPagination());
    }

    private PageResult<List<SearchCustomerItem>> getSearchResultByWorkWx(Integer companyType, PageResult<List<WorkWxFullContactUserRelation>> weComUserRelation) {
        List<WorkWxFullContactUserRelation> workRelations = weComUserRelation.getData();
        Set<String> unionIdSet = workRelations.stream()
                .filter(Objects::nonNull)
                .map(WorkWxFullContactUserRelation::getContact)
                .map(NpWorkWxContact::getUnionId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        List<UserAndWxInfoResp> userAndWxInfoRespList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(unionIdSet)) {
            int cur = 1;
            int si = 100;
            while (true) {
                PageResult<List<UserAndWxInfoResp>> pageResult = userClient.searchUserAndWxInfo(companyType, null, null, null, null, null, unionIdSet, cur, si);
                if (pageResult.isFail()) {
                    break;
                }
                List<UserAndWxInfoResp> data = pageResult.getData();
                if (ObjectUtils.isEmpty(data)) {
                    break;
                }
                userAndWxInfoRespList.addAll(data);
                Pagination pagination = pageResult.getPagination();
                if ((long) cur * si >= pagination.getTotal()) {
                    break;
                }
                cur++;
            }
        }
        List<SearchCustomerItem> items = getSearchCustomerItemsByWorkWx(workRelations, userAndWxInfoRespList);
        items.forEach(item -> {
            if (ObjectUtil.isNotEmpty(item.getWxInfo()) && ObjectUtil.isNotEmpty(item.getWxInfo().getUnionId())) {
                item.setUnionId(item.getWxInfo().getUnionId());
            }
            List<SearchWorkWxRelationItem> relationList = item.getRelationList();
            if (relationList != null) {
                relationList = relationList.stream()
                        .filter(Objects::nonNull)
                        .sorted(Comparator.comparing(SearchWorkWxRelationItem::getAddTime, Comparator.nullsLast(Comparator.reverseOrder())))
                        .collect(Collectors.toList());
                item.setRelationList(relationList);
            }
        });
        return PageResult.success(items, weComUserRelation.getPagination());
    }

    private List<SearchCustomerItem> getSearchCustomerItemsByWorkWx(List<WorkWxFullContactUserRelation> workRelations, List<UserAndWxInfoResp> userAndWxInfoRespList) {
        Map<String, UserAndWxInfoResp> userAndWxInfoRespMap = userAndWxInfoRespList.stream()
                .filter(o -> o != null && o.getUcWxExt() != null)
                .collect(Collectors.toMap(
                        resp -> resp.getUcWxExt().getUnionId(),
                        resp -> resp,
                        (existing, replacement) -> existing
                ));
        Map<String, SearchCustomerItem> map = new HashMap<>();
        List<SearchCustomerItem> list = new ArrayList<>();
        for (WorkWxFullContactUserRelation workRelation : workRelations) {
            if (workRelation != null) {
                // 有unionid，聚合用户信息
                if (workRelation.getContact() != null && StringUtils.isNotEmpty(workRelation.getContact().getUnionId())) {
                    String unionId = workRelation.getContact().getUnionId();
                    // unionid list已经添加过
                    if (map.containsKey(unionId)) {
                        SearchCustomerItem searchCustomerItem = map.get(unionId);
                        searchCustomerItem.getRelationList().add(SearchWorkWxRelationItem.of(workRelation));
                    } else {
                        UserAndWxInfoResp userAndWxInfo = userAndWxInfoRespMap.get(unionId);
                        if (userAndWxInfo != null) {
                            UserInfo userInfo = UserInfo.of(userAndWxInfo.getUcUsers());
                            WxInfo wxInfo = WxInfo.of(userAndWxInfo.getUcWxExt());
                            ArrayList<SearchWorkWxRelationItem> itemList = new ArrayList<>();
                            itemList.add(SearchWorkWxRelationItem.of(workRelation));
                            SearchCustomerItem searchCustomerItem = SearchCustomerItem.builder()
                                    .userInfo(userInfo)
                                    .wxInfo(wxInfo)
                                    .relationList(itemList)
                                    .build();
                            map.put(unionId, searchCustomerItem);
                            list.add(searchCustomerItem);
                        } else {
                            // 有unionId，没有用户信息，直接添加
                            ArrayList<SearchWorkWxRelationItem> itemList = new ArrayList<>();
                            itemList.add(SearchWorkWxRelationItem.of(workRelation));
                            SearchCustomerItem searchCustomerItem = SearchCustomerItem.builder()
                                    .userInfo(null)
                                    .wxInfo(WxInfo.builder().unionId(unionId).build())
                                    .relationList(itemList)
                                    .build();
                            map.put(unionId, searchCustomerItem);
                            list.add(searchCustomerItem);
                        }
                    }
                } else {
                    // 没unionId，直接返回workWx
                    ArrayList<SearchWorkWxRelationItem> itemList = new ArrayList<>();
                    itemList.add(SearchWorkWxRelationItem.of(workRelation));
                    SearchCustomerItem searchCustomerItem = SearchCustomerItem.builder()
                            .userInfo(null)
                            .wxInfo(null)
                            .relationList(itemList)
                            .build();
                    list.add(searchCustomerItem);
                }
            }
        }
        return list;
    }

    private List<SearchCustomerItem> getSearchCustomerItems(List<UserAndWxInfoResp> userAndWxInfo, Map<String, List<WorkWxFullContactUserRelation>> relationMap) {
        return userAndWxInfo.stream()
                .filter(Objects::nonNull)
                .map(o -> {
                    UserInfo userInfo = UserInfo.of(o.getUcUsers());
                    WxInfo wxInfo = WxInfo.of(o.getUcWxExt());
                    List<WorkWxFullContactUserRelation> relationList = relationMap.get(o.getUcWxExt().getUnionId());
                    List<SearchWorkWxRelationItem> itemList = null;
                    if (ObjectUtil.isNotEmpty(relationList)) {
                        itemList = relationList.stream()
                                .filter(Objects::nonNull)
                                .map(SearchWorkWxRelationItem::of).collect(Collectors.toList());
                    }
                    return SearchCustomerItem.builder()
                            .userInfo(userInfo)
                            .wxInfo(wxInfo)
                            .relationList(itemList)
                            .build();
                }).collect(Collectors.toList());
    }

    /**
     * 根据传入的unionId顺序对items进行排序
     */
    private List<SearchCustomerItem> sortCustomerItemsByUnionIdOrder(List<SearchCustomerItem> items, List<String> unionIdList) {
        Map<String, Integer> unionIdOrderMap = new HashMap<>();
        for (int i = 0; i < unionIdList.size(); i++) {
            unionIdOrderMap.put(unionIdList.get(i), i);
        }
        return items.stream()
                .sorted(Comparator.comparingInt(item -> {
                    String unionId = item.getUnionId();
                    return unionIdOrderMap.getOrDefault(unionId, Integer.MAX_VALUE);
                }))
                .collect(Collectors.toList());
    }

    public PageResult<List<CustomerChangeRecordResp>> getCustomerChangeRecordList(String searchContent, Integer current, Integer size) {
        PageResult<List<CustomerChangeRecordResp>> result = userClient.getCustomerChangeRecordList(searchContent, current, size);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (ObjectUtil.isEmpty(result.getData())) {
            return result;
        }
        List<CustomerChangeRecordResp> resps = result.getData();
        Set<Integer> userIds = resps.stream().map(CustomerChangeRecordResp::getUserId).collect(Collectors.toSet());
        List<UcUsers> usersList = userClient.batchGetUserList(BatchReq.of(userIds)).orElseThrow();
        Map<Integer, UcUsers> usersMap = usersList.stream().collect(Collectors.toMap(UcUsers::getId, Function.identity()));
        resps.stream().forEach(item -> {
            item.setUserCode(DesensitizeUtil.idToMask(item.getUserId()));
            item.setUserNickName(DesensitizeUtil.desensitizeName(usersMap.getOrDefault(item.getUserId(), new UcUsers()).getNickName()));
        });

        return PageResult.success(resps, result.getPagination());
    }

    public PageResult<List<WxChangeRecordResp>> getWxChangeRecordList(String searchContent, Integer current, Integer size) {
        PageResult<List<WxChangeRecordResp>> result = userClient.getWxChangeRecordList(searchContent, current, size);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (ObjectUtil.isEmpty(result.getData())) {
            return result;
        }
        List<WxChangeRecordResp> resps = result.getData();
        List<Integer> wxIds = resps.stream().map(WxChangeRecordResp::getWxId).collect(Collectors.toList());
        Set<Integer> userIds = resps.stream().map(WxChangeRecordResp::getUserId).collect(Collectors.toSet());
        Set<Integer> operatorIds = resps.stream().map(WxChangeRecordResp::getOperatorId).collect(Collectors.toSet());
        userIds.addAll(operatorIds);
        List<UcUsers> usersList = userClient.batchGetUserList(BatchReq.of(userIds)).orElseThrow();
        Map<Integer, UcUsers> usersMap = usersList.stream().collect(Collectors.toMap(UcUsers::getId, Function.identity()));
        Map<Integer, UcWxExt> wxExtMap = wxClient.batchGetUserWxExt(null, wxIds).orElseThrow().stream().collect(Collectors.toMap(UcWxExt::getId, Function.identity()));
        resps.stream().forEach(item -> {
            item.setUserCode(DesensitizeUtil.idToMask(item.getUserId()));
            item.setWxNickName(wxExtMap.getOrDefault(item.getWxId(), new UcWxExt()).getNickname());
            if (item.getUserId().equals(item.getOperatorId())) {
                item.setOperatorName("用户");
            } else {
                item.setOperatorName(usersMap.getOrDefault(item.getOperatorId(), new UcUsers()).getRealName());
            }
        });

        return PageResult.success(resps, result.getPagination());
    }

    public PageResult<List<NewCustomerInfoResp>> getCustomerInfoList(String searchText, Integer current, Integer size) {
        if (StringUtils.isBlank(searchText)) {
            return PageResult.empty();
        }
        PageResult<List<CustomerInfoResp>> result = userClient.getCustomerList(searchText, current, size);
        if (result.isFail()) {
            throw new BusinessException(result);
        }
        if (ObjectUtil.isNotEmpty(result) && ObjectUtil.isNotEmpty(result.getData())) {
            List<CustomerInfoResp> data = result.getData();
            List<Integer> userIdList = data.stream().map(CustomerInfoResp::getUserId).collect(Collectors.toList());
            Map<Integer, UcEvaluationInfo> evaluationInfoMap = userClient.getEvaluationInfoList(BatchReq.of(userIdList)).getData().stream().collect(Collectors.toMap(UcEvaluationInfo::getUserId, x -> x, (x, y) -> x));
            Map<Integer, UcVerifyInfo> verifyInfoMap = userClient.getVerificationInfoList(BatchReq.of(userIdList)).getData().stream().collect(Collectors.toMap(UcVerifyInfo::getUserId, x -> x, (x, y) -> x));
            Map<Integer, UcRewardSummary> rewardCountMap = userClient.getRewardSummaryList(BatchReq.of(userIdList)).getData().stream().collect(Collectors.toMap(UcRewardSummary::getUserId, x -> x, (x, y) -> x));
            Map<Integer, UcCustomerExt> customerExtMap = userClient.batchUserExt(BatchReq.of(userIdList)).getData().stream().collect(Collectors.toMap(UcCustomerExt::getId, x -> x, (x, y) -> x));
            List<Long> payCompanyIds = customerExtMap.values().stream().map(UcCustomerExt::getPayCompanyId).collect(Collectors.toList());
            Map<Long, RsPayCompany> payCompanyMap = orderClient.batchPayCompanyList(BatchReq.of(payCompanyIds)).getData().stream().collect(Collectors.toMap(RsPayCompany::getId, x -> x, (x, y) -> x));
            Map<Integer,UcProfileInfo> profileInfoMap=userClient.getProfileInfoList(BatchReq.of(userIdList)).getData().stream().collect(Collectors.toMap(UcProfileInfo::getUserId, x -> x, (x, y) -> x));
            List<NewCustomerInfoResp> respList = data.stream().map(r -> {
                NewCustomerInfoResp resp = new NewCustomerInfoResp();

                NewCustomerInfoResp.UserDetailResp userInfo= new NewCustomerInfoResp.UserDetailResp();
                BeanUtils.copyProperties(r, userInfo);
                userInfo.setWxId(ObjectUtil.isNotEmpty(r.getWxId()) ? Long.valueOf(r.getWxId()) : null);
                userInfo.setUserCode(DesensitizeUtil.idToMask(r.getUserId()));
                userInfo.setPayCompanyId(customerExtMap.getOrDefault(r.getUserId(), new UcCustomerExt()).getPayCompanyId());
                userInfo.setPayCompany(payCompanyMap.getOrDefault(userInfo.getPayCompanyId(), new RsPayCompany()).getName());
                userInfo.setIsWhitelist(customerExtMap.getOrDefault(r.getUserId(), new UcCustomerExt()).getIsServer());
                userInfo.setRewardCount(rewardCountMap.getOrDefault(r.getUserId(), new UcRewardSummary()).getRewardCount());
                resp.setUserInfo(userInfo);

                UcEvaluationInfo evaluationInfo = evaluationInfoMap.getOrDefault(r.getUserId(), new UcEvaluationInfo());
                NewCustomerInfoResp.UcEvaluationInfoResp ucEvaluationInfoResp = new NewCustomerInfoResp.UcEvaluationInfoResp();
                BeanUtils.copyProperties(evaluationInfo, ucEvaluationInfoResp);
                resp.setEvaluationInfo(ucEvaluationInfoResp);

                UcVerifyInfo verifyInfo = verifyInfoMap.getOrDefault(r.getUserId(), new UcVerifyInfo());
                NewCustomerInfoResp.UcVerifyInfoResp ucVerifyInfoResp = new NewCustomerInfoResp.UcVerifyInfoResp();
                BeanUtils.copyProperties(verifyInfo, ucVerifyInfoResp);
                if(ObjectUtil.isNotEmpty(ucVerifyInfoResp.getIdentityNumber())){
                    String identityNumber = AESUtil.decrypt(ucVerifyInfoResp.getIdentityNumber());
                    if (ObjectUtil.isEmpty(identityNumber)) {
                        // 兼容之前未加密情况
                        identityNumber = DesensitizedUtil.idCardNum(ucVerifyInfoResp.getIdentityNumber(), 4, 4);
                    } else {
                        identityNumber = DesensitizedUtil.idCardNum(identityNumber, 4, 4);
                    }
                    ucVerifyInfoResp.setIdentityNumber(identityNumber);
                }
                resp.setVerifyInfo(ucVerifyInfoResp);

                NewCustomerInfoResp.UcProfileInfoResp ucProfileInfoResp = new NewCustomerInfoResp.UcProfileInfoResp();
                BeanUtils.copyProperties(profileInfoMap.getOrDefault(r.getUserId(), new UcProfileInfo()), ucProfileInfoResp);
                resp.setProfileInfo(ucProfileInfoResp);
                return resp;
            }).collect(Collectors.toList());
            return PageResult.success(respList, result.getPagination());
        }
        return PageResult.success();
    }

    public SimpleUserDetailResp getSimpleUserDetail(int userId) {
        SimpleUserDetailResp userDetailResp = new SimpleUserDetailResp();
        UcVerifyInfo verifyInfo = userClient.getVerifyInfo(userId).getData();
        if (ObjectUtil.isNotEmpty(verifyInfo)) {
            userDetailResp.setVerifyType(verifyInfo.getVerifyType());
            userDetailResp.setAge(verifyInfo.getAge());
        }
        UcEvaluationInfo evaluationInfo = userClient.getEvaluationInfo(userId).getData();
        if (ObjectUtil.isNotEmpty(evaluationInfo)) {
            userDetailResp.setLevel(evaluationInfo.getLevel());
        }
        Map<Integer, String> userUnionIdMap = userClient.batchSearchUnionIdMapByUserId(BatchReq.of(Collections.singleton(userId))).getData();
        if (ObjectUtil.isNotEmpty(userUnionIdMap) && userUnionIdMap.containsKey(userId)) {
            String unionId = userUnionIdMap.get(userId);
            List<NpWwxUnionTag> tagList = workwxClient.getUnionTagListBuUnionId(45, unionId).getData();
            if (ObjectUtil.isNotEmpty(tagList)) {
                userDetailResp.setTagList(tagList.stream().map(NpWwxUnionTag::getName).collect(Collectors.toList()));
            }
        }
        return userDetailResp;
    }

    public PageResult<List<NewCustomerInfoResp>> getCustomerFakeInfoList(String searchText, Integer current, Integer size) {
        if (StringUtils.isBlank(searchText)) {
            return PageResult.empty();
        }
        String mobile = "";
        String userCode = "";
        Long userId = null;
        Long wxId = null;
        if (PhoneUtil.isMobile(searchText)) {
            mobile = searchText;
        } else if (NumberUtil.isNumber(searchText) && !PhoneUtil.isMobile(searchText)) {
            userId = Long.valueOf(searchText);
            wxId = Long.valueOf(searchText);
        } else {
            userCode = searchText;
        }
        GlobalSearchFakeReq req = GlobalSearchFakeReq.builder()
                .isAnd(false)
                .userId(userId)
                .wxId(wxId)
                .userCode(userCode)
                .mobile(mobile)
                .current(current)
                .size(size)
                .build();
        PageResult<List<UcFakeUser>> customerFakeInfoList = userClient.globalSearchFakeByLakeHouse(req);
        if (ObjectUtil.isEmpty(customerFakeInfoList.getData())) {
            return PageResult.empty();
        }
        Set<Long> payCompanyIdSet = customerFakeInfoList.getData().stream().map(UcFakeUser::getPayCompanyId).collect(Collectors.toSet());
        Map<Long, RsPayCompany> payCompanyMap = orderClient.batchPayCompanyList(BatchReq.of(payCompanyIdSet)).getData().stream().collect(Collectors.toMap(RsPayCompany::getId, x -> x, (x, y) -> x));
        List<NewCustomerInfoResp> respList = new ArrayList<>();
        customerFakeInfoList.getData().forEach(i -> {
            NewCustomerInfoResp resp = new NewCustomerInfoResp();
            String decryptFakeMobile = AESUtil.decryptFake(i.getMobile());
            NewCustomerInfoResp.UserDetailResp userDetailResp = NewCustomerInfoResp.UserDetailResp.builder()
                    .userId(i.getUserId().intValue())
                    .userCode(i.getUserCode())
                    .createTime(i.getCreateTime())
                    .nickName(i.getNickName())
                    .maskMobile(DesensitizeUtil.mobilePhone(decryptFakeMobile))
                    .avatarUrl(i.getAvatar())
                    .wxId(i.getWxId())
                    .payCompanyId(i.getPayCompanyId())
                    .payCompany(payCompanyMap.getOrDefault(i.getPayCompanyId(), new RsPayCompany()).getName())
                    .build();
            resp.setUserInfo(userDetailResp);
            NewCustomerInfoResp.UcEvaluationInfoResp evaluationInfoResp = NewCustomerInfoResp.UcEvaluationInfoResp.builder()
                    .surveyScore(74)
                    .expireTime(i.getEvaluationExpireTime())
                    .level(i.getEvaluationLevel())
                    .build();
            resp.setEvaluationInfo(evaluationInfoResp);
            String identityNumber = AESUtil.decryptFake(i.getIdentityNumber());
            NewCustomerInfoResp.UcVerifyInfoResp verifyInfoResp = NewCustomerInfoResp.UcVerifyInfoResp.builder()
                    .name(i.getRealName())
                    .identityNumber(identityNumber)
                    .identityType(ID_CARD)
                    .verifyType(IVT_THREE)
                    .age(DateUtil.ageOfNow(identityNumber.subSequence(6, 14).toString()))
                    .build();
            resp.setVerifyInfo(verifyInfoResp);
            respList.add(resp);
        });
        return PageResult.success(respList, Pagination.of(current, size, (long) respList.size()));
    }

    public PageResult<List<SearchCustomerItem>> globalSearchFake(Integer searchType, String content, Integer current, Integer size) {
        CustomerSearchTypeEnum typeEnum = CustomerSearchTypeEnum.getSearchTypeEnum(searchType);
        GlobalSearchFakeReq req = new GlobalSearchFakeReq();
        req.setIsAnd(true);
        req.setCurrent(current);
        req.setSize(size);
        switch (typeEnum) {
            case USER_CODE:
                if (NumberUtil.isNumber(content)) {
                    req.setUserId(Long.valueOf(content));
                } else {
                    req.setUserCode(content);
                }
                break;
            case MOBILE:
                req.setMobile(content);
                break;
            case REAL_NAME:
                req.setRealName(content);
                break;
            case USER_NICK_NAME:
                req.setNickName(content);
                break;
            case WX_NICK_NAME:
                req.setNickName(content);
                break;
            case RELATION_CODE:
                return PageResult.empty();
            case WORK_WX_ACCOUNT_NAME:
                return PageResult.empty();
            case WORK_WX_ACCOUNT:
                return PageResult.empty();
            case WORK_WX_REMARK:
                return PageResult.empty();
            case UNION_ID:
                return PageResult.empty();
            default:
                throw new BusinessException("Invalid SearchType: " + searchType);
        }
        PageResult<List<UcFakeUser>> result = userClient.globalSearchFakeByLakeHouse(req);
        if (ObjectUtil.isEmpty(result)) {
            return PageResult.empty();
        }
        List<UcFakeUser> data = result.getData();
        List<SearchCustomerItem> itemList = new ArrayList<>();
        data.forEach(i -> {
            SearchCustomerItem item = new SearchCustomerItem();
            String decryptFakeMobile = AESUtil.decryptFake(i.getMobile());
            UserInfo userInfo = UserInfo.builder()
                    .id(i.getUserId().intValue())
                    .userCode(i.getUserCode())
                    .userName(i.getNickName())
                    .realName(i.getRealName())
                    .nickName(i.getNickName())
                    .avatarUrl(i.getAvatar())
                    .maskMobile(DesensitizeUtil.mobilePhone(decryptFakeMobile))
                    .build();
            WxInfo wxInfo = WxInfo.builder()
                    .wxId(i.getWxId())
                    .userId(i.getUserId().intValue())
                    .userCode(i.getUserCode())
                    .nickname(i.getNickName())
                    .headImgUrl(i.getAvatar())
                    .build();
            item.setUserInfo(userInfo);
            item.setWxInfo(wxInfo);
            itemList.add(item);
        });
        return PageResult.success(itemList, Pagination.of(current, size, (long) itemList.size()));
    }
}
