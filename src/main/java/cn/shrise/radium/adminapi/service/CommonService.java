package cn.shrise.radium.adminapi.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsCustomerEvaluation;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.dto.RefundSubInfoDto;
import cn.shrise.radium.orderservice.entity.RsCourseOrder;
import cn.shrise.radium.orderservice.entity.RsCourseRefundOrder;
import cn.shrise.radium.orderservice.entity.RsCourseRefundOrderSub;
import cn.shrise.radium.roboadviserservice.RoboAdviserServiceClient;
import cn.shrise.radium.roboadviserservice.entity.RaCustomer;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcJustCallRecord;
import cn.shrise.radium.userservice.entity.UcUsers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.shrise.radium.userservice.constant.UserTypeConstant.CUSTOMER;
import static cn.shrise.radium.userservice.constant.UserTypeConstant.STAFF;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommonService {

    private final OrderClient orderClient;
    private final ContentClient contentClient;
    private final UserClient userClient;
    private final RoboAdviserServiceClient roboAdviserServiceClient;

    public Integer getOrderIdByNumber(String orderNumber) {
        BaseResult<RsCourseOrder> result = orderClient.getOrder(orderNumber);
        if (result.isPresent() && ObjectUtil.isNotEmpty(result.getData())) {
            return result.getData().getId();
        }
        throw new BusinessException("无效的订单编号!");
    }

    public Integer getOrderIdByCompanyNumber(Integer companyType, String orderNumber) {
        BaseResult<RsCourseOrder> result = orderClient.getOrder(companyType, orderNumber);
        if (result.isPresent() && ObjectUtil.isNotEmpty(result.getData())) {
            return result.getData().getId();
        }
        throw new BusinessException("无效的订单编号!");
    }

    public List<Integer> getBatchOrderIdByNumber(List<String> orderNumbers) {
        BaseResult<List<RsCourseOrder>> result = orderClient.getOrderList(orderNumbers);
        if (result.isPresent() && ObjectUtil.isNotEmpty(result.getData())) {
            return result.getData().stream().map(RsCourseOrder::getId).collect(Collectors.toList());
        }
        throw new BusinessException("无效的订单编号!");
    }

    public Integer getRefundIdByNumber(String refundNumber) {
        BaseResult<RsCourseRefundOrder> result = orderClient.getRefundOrder(refundNumber);
        if (result.isPresent() && ObjectUtil.isNotEmpty(result.getData())) {
            return result.getData().getId();
        }
        throw new BusinessException("无效的退款单编号!");
    }

    public Integer getEvaluationIdByNumber(String evaluationNumber) {
        BaseResult<SsCustomerEvaluation> result = contentClient.findByNumber(evaluationNumber);
        if (result.isPresent() && ObjectUtil.isNotEmpty(result.getData())) {
            return result.getData().getId();
        }
        throw new BusinessException("无效的测评编号!");
    }

    public Integer getUserIdByNumber(Integer companyType, String userNumber) {
        BaseResult<UcUsers> result = userClient.getUserByNumber(companyType, CUSTOMER, userNumber, true);
        if (result.isPresent() && ObjectUtil.isNotEmpty(result.getData())) {
            return result.getData().getId();
        }
        throw new BusinessException("无效的用户编号!");
    }

    public Integer getUserIdByNumber(Integer companyType, String userNumber, Boolean enable) {
        BaseResult<UcUsers> result = userClient.getUserByNumber(companyType, CUSTOMER, userNumber, enable);
        if (result.isPresent() && ObjectUtil.isNotEmpty(result.getData())) {
            return result.getData().getId();
        }
        throw new BusinessException("无效的用户编号!");
    }

    public Long getUserMobileByNumber(Integer companyType, String userNumber) {
        BaseResult<UcUsers> result = userClient.getUserByNumber(companyType, CUSTOMER, userNumber, null);
        if (result.isPresent() && ObjectUtil.isNotEmpty(result.getData())) {
            return result.getData().getMobileId();
        }
        throw new BusinessException("无效的用户编号!");
    }

    public Integer getStaffIdByNumber(Integer companyType, String userNumber) {
        BaseResult<UcUsers> result = userClient.getUserByNumber(companyType, STAFF, userNumber, null);
        if (result.isPresent() && ObjectUtil.isNotEmpty(result.getData())) {
            return result.getData().getId();
        }
        throw new BusinessException("无效的用户编号!");
    }

    public String getCustomerIdByUserId(Integer userId) {
        RaCustomer raCustomer = roboAdviserServiceClient.findOneByUserId(userId).orElse(new RaCustomer());
        if (ObjectUtil.isEmpty(raCustomer.getCustomerId())) {
            throw new BusinessException("无效的用户id");
        }
        return raCustomer.getCustomerId();
    }

    public UcJustCallRecord getJustCallRecordByCallId(String callId) {
        UcJustCallRecord ucJustCallRecord = userClient.getJustCallRecord(callId).orElse(new UcJustCallRecord());
        if (ObjectUtil.isEmpty(ucJustCallRecord)) {
            throw new BusinessException("无效的callId");
        }
        return ucJustCallRecord;
    }


    public Integer getUserIdByCustomerId(String customerId) {
        RaCustomer raCustomer = roboAdviserServiceClient.findOneByCustomerId(customerId).orElse(new RaCustomer());
        if (ObjectUtil.isEmpty(raCustomer.getUserId())) {
            throw new BusinessException("无效的客户id");
        }
        return raCustomer.getUserId();
    }
    public Long getUserMobileIdByNumber(Integer companyType, String userNumber) {
        BaseResult<UcUsers> result = userClient.getUserByNumber(companyType, CUSTOMER, userNumber, null);
        if (result.isPresent() && ObjectUtil.isNotEmpty(result.getData())) {
            return result.getData().getMobileId();
        }
        throw new BusinessException("无效的用户编号!");
    }

    public void checkMarkRefund(String refundNumber) {
        RsCourseRefundOrderSub refundOrderSub = orderClient.getRefundSubInfo(refundNumber).orElse(null);
        if (ObjectUtil.isEmpty(refundOrderSub)) {
            throw new BusinessException("无效的退款子订单");
        }
        List<RefundSubInfoDto> refundSubInfoDtos = orderClient.getUnfinishedOnlineRefundList(refundOrderSub.getRefundOrderId()).orElse(null);
        if (ObjectUtil.isNotEmpty(refundSubInfoDtos)) {
            List<RsCourseRefundOrderSub> processingOnlineRefundList = refundSubInfoDtos.stream().filter(Objects::nonNull).map(RefundSubInfoDto::getRefundOrderSub).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(processingOnlineRefundList)) {
                throw new BusinessException("当前系统正在执行线上单退款，请稍后再试");
            }
        }
    }

    public Map<Integer, UcUsers> getUserMapByIds(Collection<Integer> userIdList) {
        if (ObjectUtil.isEmpty(userIdList)) {
            return Collections.emptyMap();
        }
        List<UcUsers> userList = userClient.batchGetUserList(new BatchReq<>(userIdList)).getData();
        return Optional.ofNullable(userList)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(UcUsers::getId, Function.identity()));
    }

}
