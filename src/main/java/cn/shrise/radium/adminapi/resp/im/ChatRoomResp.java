package cn.shrise.radium.adminapi.resp.im;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.imservice.entity.ImChatRoom;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ChatRoomResp {

    @ApiModelProperty(value = "房间id")
    private Long id;

    @ApiModelProperty(value = "房间号")
    private String number;

    @ApiModelProperty(value = "房间名称")
    private String name;

    @ApiModelProperty(value = "公司类型")
    private Integer companyType;

    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;

    @ApiModelProperty(value = "解盘消息展示天数(null-无限制)")
    private Integer streamDays;

    @ApiModelProperty(value = "是否有互动")
    private Boolean isChat;

    @ApiModelProperty(value = "是否开启消息审核")
    private Boolean isMsgAudit;

    @ApiModelProperty(value = "是否开启智能审核")
    private Boolean isSmartAudit;

    @ApiModelProperty(value = "是否有组合(实战跟投)")
    private Boolean isPortfolio;

    @ApiModelProperty(value = "组合频道ID")
    private Integer channelId;

    @ApiModelProperty(value = "案例频道ID")
    private Long caseId;

    @ApiModelProperty(value = "案例频道名称")
    private String caseName;

    @ApiModelProperty(value = "案例频道ID")
    private Long caseChannelId;

    @ApiModelProperty(value = "案例频道名称")
    private String caseChannelName;

    @ApiModelProperty(value = "案例频道备注")
    private String caseChannelRemark;

    @ApiModelProperty(value = "老师ID数组")
    private List<Integer> analystList;

    @ApiModelProperty(value = "快捷回复词条")
    private String replyWords;

    @ApiModelProperty(value = "是否案例推送")
    private Boolean isCasePush;

    @ApiModelProperty("股票池频道解盘股票跟踪前端是否隐藏")
    private Boolean isFollowHide;

    public static ChatRoomResp of(ImChatRoom imChatRoom) {
        List<Integer> analystList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(imChatRoom.getAnalystList())) {
            analystList = JSON.parseArray(imChatRoom.getAnalystList(), Integer.class);
        }
        return ChatRoomResp.builder()
                .id(imChatRoom.getId())
                .number(imChatRoom.getNumber())
                .name(imChatRoom.getName())
                .companyType(imChatRoom.getCompanyType())
                .enabled(imChatRoom.getEnabled())
                .streamDays(imChatRoom.getStreamDays())
                .isChat(imChatRoom.getIsChat())
                .isMsgAudit(imChatRoom.getIsMsgAudit())
                .isSmartAudit(imChatRoom.getIsSmartAudit())
                .isPortfolio(imChatRoom.getIsPortfolio())
                .channelId(imChatRoom.getChannelId())
                .analystList(analystList)
                .caseChannelId(imChatRoom.getCaseChannelId())
                .replyWords(imChatRoom.getReplyWords())
                .isCasePush(imChatRoom.getIsCasePush())
                .build();
    }
}
