package cn.shrise.radium.adminapi.resp.dingding;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DingDingNoticeConfigRecordResp {

    @ApiModelProperty("创建时间")
    private Instant gmtCreate;

    @ApiModelProperty("操作人名称")
    private String operatorName;

    @ApiModelProperty("操作明细")
    private String content;
}
