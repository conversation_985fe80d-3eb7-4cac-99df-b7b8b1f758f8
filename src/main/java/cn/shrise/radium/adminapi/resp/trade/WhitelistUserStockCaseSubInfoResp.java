package cn.shrise.radium.adminapi.resp.trade;

import cn.shrise.radium.tradeservice.resp.UserStockCaseSubInfoResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WhitelistUserStockCaseSubInfoResp {

    @ApiModelProperty("用户ID")
    private Integer userId;

    @ApiModelProperty("用户昵称")
    private String nickName;

    private List<UserStockCaseSubInfoResp> subInfoList;
}
