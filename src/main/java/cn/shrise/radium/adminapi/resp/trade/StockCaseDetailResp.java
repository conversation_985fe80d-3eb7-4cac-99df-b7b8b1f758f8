package cn.shrise.radium.adminapi.resp.trade;

import cn.shrise.radium.userservice.resp.UserBaseInfoResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * <AUTHOR>
 * @version 1.0
 * desc: 案例别表
 */
@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockCaseDetailResp {
    @ApiModelProperty("案例ID")
    private Long id;

    @ApiModelProperty("股票代码")
    private String labelCode;

    @ApiModelProperty("创建时间")
    private Instant gmtCreate;

    @ApiModelProperty("更新时间")
    private Instant gmtModified;

    @ApiModelProperty("是否清仓")
    private Boolean isClosed;

    @ApiModelProperty("频道ID")
    private Long channelId;

    @ApiModelProperty("频道名称")
    private String channelName;

    @ApiModelProperty("频道类型")
    private Integer channelType;

    @ApiModelProperty("当前仓位")
    private Integer count;

    @ApiModelProperty("价格")
    private Double price;

    @ApiModelProperty("最低价")
    private Double priceDown;

    @ApiModelProperty("最高价")
    private Double priceUp;

    @ApiModelProperty("防守位")
    private Double lossPrice;

    @ApiModelProperty("目标位")
    private Double targetPrice;

    @ApiModelProperty("止盈区间最高价")
    private Double takeProfitUp;

    @ApiModelProperty("止盈区间最低价")
    private Double takeProfitDown;

    @ApiModelProperty("止损区间最高价")
    private Double stopLossUp;

    @ApiModelProperty("止损区间最低价")
    private Double stopLossDown;

    @ApiModelProperty("创建人")
    private UserBaseInfoResp creatorInfo;

    @ApiModelProperty("审核人")
    private UserBaseInfoResp auditorInfo;

    @ApiModelProperty(value = "审核状态")
    private Integer auditStatus;

    @ApiModelProperty("审核时间")
    private Instant auditTime;

    @ApiModelProperty("审核拒绝原因")
    private String rejectReason;

    @ApiModelProperty("分析师Id")
    private Integer analystId;

    @ApiModelProperty("分析师")
    private String analystName;

    @ApiModelProperty("是否置顶")
    private Boolean isTop;

    @ApiModelProperty("置顶时间")
    private String topTime;

    @ApiModelProperty("案例来源类型")
    private Integer sourceType;

    @ApiModelProperty("复权因子")
    private Double sumFactor;

    @ApiModelProperty("入池7天最高涨幅")
    private Double max7Ratio;

    @ApiModelProperty("入池15天最高涨幅")
    private Double max15Ratio;

    @ApiModelProperty("入池30天最高涨幅")
    private Double max30Ratio;

    @ApiModelProperty("入池180天最高涨幅")
    private Double max180Ratio;

    @ApiModelProperty("持仓期间最高涨幅")
    private Double maxHoldRatio;

    @ApiModelProperty("入池后最高涨幅")
    private Double maxRatio;

    @ApiModelProperty("累计涨跌幅")
    private Double changeRatio;

    @ApiModelProperty("评分")
    private Double score;

    @ApiModelProperty("是否调研票")
    private Boolean isResearch;

    @ApiModelProperty("T+8天最高收益涨幅")
    private Double max9Ratio;

    @ApiModelProperty("T+8天最大收益跌幅")
    private Double min9Ratio;

    @ApiModelProperty(value = "股票池频道解盘股票跟踪前端是否隐藏")
    private Boolean isFollowHide;
}
