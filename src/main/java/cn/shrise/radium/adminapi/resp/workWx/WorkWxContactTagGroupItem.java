package cn.shrise.radium.adminapi.resp.workWx;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkWxContactTagGroupItem {

    private String groupId;

    private String groupName;

    private Integer order;

    private List<WorkWxContactTagItem> tags;
}
