package cn.shrise.radium.adminapi.resp.weimob;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class PointActivityResp {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "创建时间")
    private Instant gmtCreate;

    @ApiModelProperty(value = "更新时间")
    private Instant gmtModified;

    @ApiModelProperty(value = "编号")
    private String number;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "详情图")
    private String bannerUrl;

    @ApiModelProperty(value = "产品类型")
    private Integer productLevel;

    @ApiModelProperty(value = "开始时间")
    private Instant payStartTime;

    @ApiModelProperty(value = "结束时间")
    private Instant payEndTime;

    @ApiModelProperty(value = "积分系数")
    private Float factor;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "短链接url")
    private String shortUrl;

    @ApiModelProperty(value = "公司类型")
    private Integer companyType;

}
