package cn.shrise.radium.adminapi.resp.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class CustomerComplaintInfoResp {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "图像列表")
    private List<String> images;

    @ApiModelProperty(value = "是否处理")
    private Boolean isHandle;

    @ApiModelProperty(value = "wxId")
    private Integer wxId;

}
