package cn.shrise.radium.adminapi.resp.trade;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * @Author: tang<PERSON><PERSON><PERSON>
 * @Date: 2024/8/14 10:00
 * @Desc:
 **/
@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RecommendStockTypeRecordResp {
    @ApiModelProperty(value = "操作人id")
    private Integer operatorId;

    @ApiModelProperty(value = "操作人")
    private String operateName;

    @ApiModelProperty(value = "操作人头像")
    private String operateAvatarUrl;

    @ApiModelProperty(value = "操作内容")
    private String content;

    @ApiModelProperty(value = "创建时间")
    private Instant gmtCreate;
}
