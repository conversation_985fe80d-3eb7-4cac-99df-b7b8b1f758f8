package cn.shrise.radium.adminapi.resp.user;

import cn.shrise.radium.userservice.resp.evaluation.NewEvaluateInfoResp;
import cn.shrise.radium.userservice.resp.evaluation.VerifyInfoResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> z<PERSON><PERSON><PERSON><PERSON>
 * @created : 2025/5/27, 星期二
 **/
@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class SimpleUserDetailResp {

    @ApiModelProperty("验证类型")
    private Integer verifyType;

    @ApiModelProperty("年龄")
    private Integer age;

    @ApiModelProperty("测评等级")
    private String Level;

    @ApiModelProperty(value = "已完成订单数")
    private Integer finishedOrderCount;

    @ApiModelProperty(value = "标签列表")
    private List<String> tagList;

}
