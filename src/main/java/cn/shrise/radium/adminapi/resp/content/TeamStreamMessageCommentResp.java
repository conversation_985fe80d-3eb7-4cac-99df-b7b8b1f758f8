package cn.shrise.radium.adminapi.resp.content;

import cn.shrise.radium.contentservice.entity.SsStreamMessageComment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
@Builder
public class TeamStreamMessageCommentResp {

    @ApiModelProperty(value = "评论id")
    private Long id;

    @ApiModelProperty(value = "创建时间")
    private Instant gmtCreate;

    @ApiModelProperty(value = "消息id")
    private Long messageId;

    @ApiModelProperty(value = "用户id")
    private Integer customerId;

    @ApiModelProperty(value = "用户昵称")
    private String customerName;

    @ApiModelProperty(value = "评论内容")
    private String commentContent;

    @ApiModelProperty(value = "评论时间")
    private Instant commentTime;

    @ApiModelProperty(value = "审核人id")
    private Integer auditorId;

    @ApiModelProperty(value = "审核人名称")
    private String auditorName;

    @ApiModelProperty(value = "审核时间")
    private Instant auditTime;

    @ApiModelProperty(value = "审核状态")
    private Integer auditStatus;

    @ApiModelProperty(value = "是否通过")
    private Boolean passed;

    @ApiModelProperty(value = "机审结果")
    private TextScanResultResp resultResp;


    public static TeamStreamMessageCommentResp of(SsStreamMessageComment comment) {
        return TeamStreamMessageCommentResp.builder()
                .id(comment.getId())
                .gmtCreate(comment.getGmtCreate())
                .messageId(comment.getMessageId())
                .customerId(comment.getCustomerId())
                .commentContent(comment.getCommentContent())
                .commentTime(comment.getCommentTime())
                .auditorId(comment.getAuditorId())
                .auditTime(comment.getAuditTime())
                .auditStatus(comment.getAuditStatus())
                .build();
    }

}
