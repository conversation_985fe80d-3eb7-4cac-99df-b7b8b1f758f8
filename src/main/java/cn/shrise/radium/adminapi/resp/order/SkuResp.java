package cn.shrise.radium.adminapi.resp.order;

import cn.shrise.radium.orderservice.entity.RsSku;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class SkuResp {

    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("编号")
    private String number;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("公司类型")
    private Integer companyType;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态")
    private Integer productLevel;

    public static SkuResp of(RsSku sku) {
        return SkuResp.builder()
                .id(sku.getId())
                .number(sku.getNumber())
                .name(sku.getName())
                .companyType(sku.getCompanyType())
                .status(sku.getStatus())
                .productLevel(sku.getProductLevel())
                .build();
    }

    public static List<SkuResp> batchOf(List<RsSku> rsSkuList) {

        return rsSkuList.stream().map(sku->{
           return SkuResp.builder()
                    .id(sku.getId())
                    .number(sku.getNumber())
                    .name(sku.getName())
                    .companyType(sku.getCompanyType())
                    .status(sku.getStatus())
                    .productLevel(sku.getProductLevel())
                    .build();
        }).collect(Collectors.toList());
    }
}
