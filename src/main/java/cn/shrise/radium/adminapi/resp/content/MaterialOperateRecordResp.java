package cn.shrise.radium.adminapi.resp.content;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class MaterialOperateRecordResp {

    @ApiModelProperty(value = "时间")
    private Instant gmtCreate;

    @ApiModelProperty(value = "素材id")
    private Long materialId;

    @ApiModelProperty(value = "操作人id")
    private Integer operatorId;

    @ApiModelProperty(value = "操作人名称")
    private String operatorName;

    @ApiModelProperty(value = "操作内容")
    private String content;

    @ApiModelProperty(value = "操作人头像")
    private String avatarUrl;
}
