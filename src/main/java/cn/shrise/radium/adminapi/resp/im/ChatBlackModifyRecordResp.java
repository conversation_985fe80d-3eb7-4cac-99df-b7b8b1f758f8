package cn.shrise.radium.adminapi.resp.im;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ChatBlackModifyRecordResp {

    @ApiModelProperty("黑名单id")
    private Long blackId;

    @ApiModelProperty("操作人id")
    private Integer operatorId;

    @ApiModelProperty("操作人姓名")
    private String operatorName;

    @ApiModelProperty("头像")
    private String operatorAvatarUrl;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("黑名单状态")
    private Boolean enabled;

    @ApiModelProperty("创建时间")
    private Instant gmtCreate;

    @ApiModelProperty("用户code")
    private String userCode;
}
