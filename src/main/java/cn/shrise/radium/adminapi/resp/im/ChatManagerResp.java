package cn.shrise.radium.adminapi.resp.im;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.imservice.entity.ImChatManager;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatManagerResp {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "聊天室id")
    private Long chatId;

    @ApiModelProperty(value = "处理人id")
    private Integer managerId;

    @ApiModelProperty(value = "处理人名称")
    private String managerName;

    @ApiModelProperty(value = "老师id")
    private Integer analystId;

    @ApiModelProperty(value = "老师信息")
    private SsAnalystInfo analystInfo;

    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;

    public static ChatManagerResp of(ImChatManager imChatManager) {
        if (ObjectUtil.isEmpty(imChatManager)) {
            return new ChatManagerResp();
        }
        return ChatManagerResp.builder()
                .id(imChatManager.getId())
                .chatId(imChatManager.getChatId())
                .managerId(imChatManager.getManagerId())
                .analystId(imChatManager.getAnalystId())
                .enabled(imChatManager.getEnabled())
                .build();
    }

}
