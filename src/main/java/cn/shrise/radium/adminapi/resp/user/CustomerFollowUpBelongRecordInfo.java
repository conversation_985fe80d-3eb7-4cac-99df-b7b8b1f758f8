package cn.shrise.radium.adminapi.resp.user;

import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.userservice.entity.UcCustomerFollowUpBelongRecord;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerFollowUpBelongRecordInfo {

    private Long id;

    private Instant gmtCreate;

    private Instant gmtModified;

    private Integer userId;

    @ApiModelProperty("用户code")
    private String userCode;

    private Integer companyType;

    private Integer belongId;

    public static CustomerFollowUpBelongRecordInfo of(UcCustomerFollowUpBelongRecord record) {
        if (record == null) {
            return null;
        }
        return CustomerFollowUpBelongRecordInfo.builder()
                .id(record.getId())
                .gmtCreate(record.getGmtCreate())
                .gmtModified(record.getGmtModified())
                .userId(record.getUserId())
                .userCode(DesensitizeUtil.idToMask(record.getUserId()))
                .companyType(record.getCompanyType())
                .belongId(record.getBelongId())
                .build();
    }
}
