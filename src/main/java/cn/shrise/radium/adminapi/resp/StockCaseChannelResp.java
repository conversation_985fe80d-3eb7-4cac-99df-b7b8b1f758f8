package cn.shrise.radium.adminapi.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class StockCaseChannelResp {

    @ApiModelProperty(value = "频道ID")
    private Long id;

    @ApiModelProperty(value = "频道类型")
    private Integer channelType;

    @ApiModelProperty(value = "频道编号")
    private String number;

    @ApiModelProperty(value = "频道名称")
    private String name;

    @ApiModelProperty(value = "关联直播室名称")
    private String roomName;

    @ApiModelProperty(value = "关联栏目名称")
    private String articleSeriesName;

    @ApiModelProperty(value = "关联聊天室名称")
    private String chatRoomName;

    @ApiModelProperty(value = "创建时间")
    private Instant gmtCreate;

    @ApiModelProperty(value = "频道备注")
    private String remark;

    @ApiModelProperty(value = "是否决策小组审核")
    private Boolean needDecision;

    @ApiModelProperty(value = "股票池频道解盘股票跟踪前端是否隐藏")
    private Boolean isFollowHide;
}
