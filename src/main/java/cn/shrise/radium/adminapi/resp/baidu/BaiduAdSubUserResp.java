package cn.shrise.radium.adminapi.resp.baidu;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class BaiduAdSubUserResp {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("首次授权时间")
    private Instant gmtCreated;

    @ApiModelProperty("账户更新时间")
    private Instant gmtModified;

    @ApiModelProperty("账户ID")
    private Long userId;

    @ApiModelProperty("账户名称")
    private String userName;

    @ApiModelProperty("主账户ID")
    private Long masterUserId;

    @ApiModelProperty("主账户名称")
    private String masterUserName;

    @ApiModelProperty("账户主体ID")
    private Long cid;

    @ApiModelProperty("账户主体名称")
    private String liceName;

    @ApiModelProperty("账户余额")
    private Double balance;

    @ApiModelProperty("账户预算")
    private Double budget;

    @ApiModelProperty("资金包类型")
    private Integer balancePackage;

    @ApiModelProperty("账户状态")
    private Integer userStat;

    @ApiModelProperty("是否开通feed产品线权限")
    private Integer uaStatus;

    @ApiModelProperty("可投放流量")
    private List<Integer> validFlows;

    @ApiModelProperty("用户行业ID")
    private Integer tradeId;

    @ApiModelProperty("账户预算撞线时间")
    private String budgetOfflineTime;

    @ApiModelProperty("账户资金包类型")
    private Integer adType;
}
