package cn.shrise.radium.adminapi.resp.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class RefundProcessResp {
    @ApiModelProperty(value = "主键ID")
    private Integer id;

    /**
     * 退款单ID
     */
    @ApiModelProperty(value = "退款单ID")
    private Integer refundId;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operateType;

    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Integer operateId;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operator;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Instant createTime;

    public RefundProcessResp(Integer id, Integer refundId, String operateType, Integer operateId, Instant createTime) {
        this.id = id;
        this.refundId = refundId;
        this.operateType = operateType;
        this.operateId = operateId;
        this.createTime = createTime;
    }
}
