package cn.shrise.radium.adminapi.resp.order;

import cn.shrise.radium.adminapi.resp.user.DeptInfoResp;
import cn.shrise.radium.adminapi.resp.user.UserInfoResp;
import cn.shrise.radium.adminapi.resp.workWx.DzAdChannelResp;
import cn.shrise.radium.adminapi.resp.wx.WxExtResp;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class OrderDtoResp {

    @ApiModelProperty("订单信息")
    private OrderInfoResp orderInfo;

    @ApiModelProperty("sku信息")
    private SkuInfoResp skuInfo;

    @ApiModelProperty("优惠券信息")
    private CouponResp couponInfo;

    @ApiModelProperty("签字审核信息")
    private OrderSignInfoResp orderSignInfo;

    @ApiModelProperty("微信信息")
    private WxExtResp wxExtInfo;

    @ApiModelProperty("用户信息")
    private UserInfoResp userInfoResp;

    @ApiModelProperty("销售信息")
    private UserInfoResp salesInfoResp;

    @ApiModelProperty("审核人信息")
    private UserInfoResp auditorInfoResp;

    @ApiModelProperty("销售部门信息")
    private DeptInfoResp deptInfoResp;

    @ApiModelProperty("订单步骤信息")
    private List<OrderStepResp> steps;

    @ApiModelProperty("广告渠道信息")
    private DzAdChannelResp dzInfo;

    @ApiModelProperty("是否添加高级助教")
    private Boolean isMarkWxUser;
}
