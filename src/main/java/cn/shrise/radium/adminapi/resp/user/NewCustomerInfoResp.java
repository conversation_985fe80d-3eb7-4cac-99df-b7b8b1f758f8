package cn.shrise.radium.adminapi.resp.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewCustomerInfoResp {

    @ApiModelProperty("用户信息")
    private UserDetailResp userInfo;

    @ApiModelProperty("测评信息")
    private UcEvaluationInfoResp evaluationInfo;

    @ApiModelProperty("认证信息")
    private UcVerifyInfoResp verifyInfo;

    @ApiModelProperty("用户基础信息")
    private UcProfileInfoResp profileInfo;

    @Data
    @ApiModel
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserDetailResp {

        @ApiModelProperty("用户id")
        private Integer userId;

        @ApiModelProperty("用户code")
        private String userCode;

        @ApiModelProperty("用户编号")
        private String userNumber;

        @ApiModelProperty("创建时间")
        private Instant createTime;

        @ApiModelProperty("用户昵称")
        private String nickName;

        @ApiModelProperty("手机号掩码")
        private String maskMobile;

        @ApiModelProperty("头像")
        private String avatarUrl;

        @ApiModelProperty("微信id")
        private Long wxId;

        @ApiModelProperty("白名单")
        private Boolean isWhitelist;

        @ApiModelProperty("支付主体名称")
        private String payCompany;

        @ApiModelProperty("支付主体ID")
        private Long payCompanyId;

        @ApiModelProperty("积分")
        private Long rewardCount;
    }

    @Data
    @ApiModel
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UcEvaluationInfoResp {

        @ApiModelProperty("测评number")
        private String number;

        @ApiModelProperty("测评得分")
        private Integer surveyScore;

        @ApiModelProperty("等级")
        private String level;

        @ApiModelProperty("测评时间")
        private Instant surveyTime;

        @ApiModelProperty("过期时间")
        private Instant expireTime;

    }

    @Data
    @ApiModel
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UcVerifyInfoResp {

        @ApiModelProperty("认证id")
        private Long id;

        @ApiModelProperty("证件类型")
        private Integer identityType;

        @ApiModelProperty("身份认证号码")
        private String identityNumber;

        @ApiModelProperty("认证类型")
        private Integer verifyType;

        @ApiModelProperty("认证审核状态")
        private Integer verifyStatus;

        @ApiModelProperty("姓名")
        private String name;

        @ApiModelProperty("出生日期")
        private Instant birthday;

        @ApiModelProperty("性别(1：男 0：女)")
        private Integer sex;

        @ApiModelProperty("身份证所在地")
        private String region;

        @ApiModelProperty("年龄")
        private Integer age;

    }

    @Data
    @ApiModel
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UcProfileInfoResp {

        @ApiModelProperty("职业")
        private Integer job;

        @ApiModelProperty("其他职业")
        private String otherJob;

        @ApiModelProperty("诚信记录")
        private Integer badRecord;

        @ApiModelProperty("其他诚信记录")
        private String otherBadRecord;

    }


}
