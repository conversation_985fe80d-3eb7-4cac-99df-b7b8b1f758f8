package cn.shrise.radium.adminapi.resp.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class MerchantModifyRecordResp {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("商户id")
    private Long merchantId;

    @ApiModelProperty("操作人id")
    private Integer operatorId;

    @ApiModelProperty("操作人姓名")
    private String operatorName;

    @ApiModelProperty("修改内容")
    private String content;

    @ApiModelProperty("创建时间")
    private Instant gmtCreate;

    @ApiModelProperty("头像")
    private String avatarUrl;
}
