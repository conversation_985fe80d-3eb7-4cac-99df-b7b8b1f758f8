package cn.shrise.radium.adminapi.resp.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ChatRecordResp {

    @ApiModelProperty(value = "联系人昵称")
    private String nickName;

    @ApiModelProperty(value = "备注名")
    private String remark;

    @ApiModelProperty(value = "添加账号")
    private String belongName;

    @ApiModelProperty(value = "关系状态")
    private Boolean relationStatus;

    @ApiModelProperty(value = "关系ID")
    private Integer relationId;

    @ApiModelProperty(value = "企业微信归属")
    private String corpName;

    @ApiModelProperty(value = "WxAccount")
    private String wxAccount;

    @ApiModelProperty(value = "ExternalUserID")
    private String externalUserID;

    @ApiModelProperty(value = "AccountType")
    private Integer accountType;

}
