package cn.shrise.radium.adminapi.resp.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.Instant;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class VipSubscriptionRecordResp {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("服务包编号")
    private String number;

    @ApiModelProperty("服务包级别")
    private Integer level;

    @ApiModelProperty("操作类型")
    private Integer operateType;

    @ApiModelProperty("用户编号")
    private Integer userId;

    @ApiModelProperty("用户编号")
    private String userCode;

    @ApiModelProperty("用户昵称")
    private String nickName;

    @ApiModelProperty("操作人编号")
    private Integer operatorId;

    @ApiModelProperty("操作人")
    private String operatorName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("开通时长")
    private Integer period;

    @ApiModelProperty("操作时间")
    private Instant operateTime;
}
