package cn.shrise.radium.adminapi.resp.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class IpGeoResp {

    @ApiModelProperty(value = "ip")
    private String ip;

    @ApiModelProperty(value = "洲")
    private String continent;

    @ApiModelProperty(value = "国家编码")
    private String countryCode;

    @ApiModelProperty(value = "互联网数字分配机构")
    private String iana;

    @ApiModelProperty(value = "互联网数字分配机构英文缩写")
    private String ianaEn;

    @ApiModelProperty(value = "国家")
    private String country;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "地区")
    private String districts;

    @ApiModelProperty(value = "服务供应商")
    private String isp;

    @ApiModelProperty(value = "地理编码")
    private String geocode;

    @ApiModelProperty(value = "省份编码")
    private String provinceCode;

    @ApiModelProperty(value = "城市编码")
    private String cityCode;

    @ApiModelProperty(value = "地区编码")
    private String districtCode;
}
