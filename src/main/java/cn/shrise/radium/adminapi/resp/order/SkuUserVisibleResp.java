package cn.shrise.radium.adminapi.resp.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("员工sku可见范围")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SkuUserVisibleResp {

    @ApiModelProperty("skuId")
    private Integer skuId;

    @ApiModelProperty("userid")
    private Integer userId;

    @ApiModelProperty("用户昵称")
    private String userName;

    @ApiModelProperty("创建时间")
    private Instant createTime;
}
