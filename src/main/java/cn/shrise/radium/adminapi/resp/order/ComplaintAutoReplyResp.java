package cn.shrise.radium.adminapi.resp.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class ComplaintAutoReplyResp {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("是否开启自动回复")
    private Boolean isOpen;

    @ApiModelProperty("回复内容")
    private String content;

}
