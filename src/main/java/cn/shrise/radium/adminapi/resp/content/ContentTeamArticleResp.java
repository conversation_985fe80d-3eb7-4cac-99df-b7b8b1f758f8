package cn.shrise.radium.adminapi.resp.content;

import cn.shrise.radium.contentservice.entity.SsArticle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class ContentTeamArticleResp {

    private SsArticle article;

    @ApiModelProperty(value = "创建人")
    private String creatorName;

    @ApiModelProperty(value = "审核人")
    private String auditorName;

    @ApiModelProperty(value = "内容主头像")
    private String avatarUrl;

    @ApiModelProperty(value = "内容主名称")
    private String teamName;
}
