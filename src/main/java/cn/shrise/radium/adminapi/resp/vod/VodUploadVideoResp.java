package cn.shrise.radium.adminapi.resp.vod;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VodUploadVideoResp {

    @ApiModelProperty("视频id")
    private String videoId;

    @ApiModelProperty("上传地址")
    private String uploadAddress;

    @ApiModelProperty("上传凭证")
    private String uploadAuth;
}
