package cn.shrise.radium.adminapi.controller.order;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.entity.Option;
import cn.shrise.radium.adminapi.entity.OrderDetail;
import cn.shrise.radium.adminapi.entity.OrderInfo;
import cn.shrise.radium.adminapi.resp.AssignBatchRecordResp;
import cn.shrise.radium.adminapi.resp.order.*;
import cn.shrise.radium.adminapi.service.AuthService;
import cn.shrise.radium.adminapi.service.CommonService;
import cn.shrise.radium.adminapi.service.ExportExcelService;
import cn.shrise.radium.adminapi.service.OrderService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.base.Pagination;
import cn.shrise.radium.common.exception.RecordNotExistedException;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.orderservice.OrderClient;
import cn.shrise.radium.orderservice.constant.OrderAuditStatusConstant;
import cn.shrise.radium.orderservice.dto.OrderBelongDto;
import cn.shrise.radium.orderservice.dto.OrderBelongOperateRecordDto;
import cn.shrise.radium.orderservice.entity.RsFileExportRecord;
import cn.shrise.radium.orderservice.entity.RsOrderBelongOperateRecord;
import cn.shrise.radium.orderservice.req.BatchAssignReq;
import cn.shrise.radium.orderservice.req.BatchAverageAssignFilterReq;
import cn.shrise.radium.orderservice.req.BatchAverageAssignReq;
import cn.shrise.radium.orderservice.resp.AssignDetailResp;
import cn.shrise.radium.orderservice.resp.BatchAssignResp;
import cn.shrise.radium.orderservice.resp.BatchAverageAssignResp;
import cn.shrise.radium.statisticsservice.StatisticsClient;
import cn.shrise.radium.statisticsservice.bean.ExportFileInfo;
import cn.shrise.radium.statisticsservice.constant.ExportFileEnum;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.workwxservice.WorkwxClient;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;
import static cn.shrise.radium.common.util.DateUtils.*;

@RestController
@RequestMapping("orders")
@RequiredArgsConstructor
public class OrderController {

    private final OrderService orderService;
    private final CommonService commonService;
    private final AuthService authService;
    private final OrderClient orderClient;
    private final ExportExcelService exportExcelService;
    private final StatisticsClient statisticsClient;
    private final UserClient userClient;
    private final WorkwxClient workwxClient;

    @PostMapping("edit")
    @ApiOperation("订单归属信息表修改")
    public BaseResult<Void> updateOrderBelong(
            @RequestParam Integer orderId,
            @ApiIgnore @RequestHeader(COMPANY_TYPE) Integer companyType,
            @ApiIgnore @RequestHeader(USER_ID) Integer operateId,
            @RequestParam(required = false) @ApiParam("归属主任") Integer headId,
            @RequestParam(required = false) @ApiParam("归属经理") Integer managerId,
            @RequestParam(required = false) @ApiParam("归属总监") Integer directorId
    ) {
        return orderClient.updateOrderBelong(orderId, companyType, operateId, headId, managerId, directorId);
    }

    @ApiOperation("获取订单归属信息列表")
    @GetMapping("getOrderBelongList")
    PageResult<List<OrderBelongDto>> getOrderBelongList(
            @RequestParam(required = false) @ApiParam("创建开始时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate startCreateTime,
            @RequestParam(required = false) @ApiParam("创建结束时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate endCreateTime,
            @RequestParam(required = false) @ApiParam("订单号") String orderNumber,
            @RequestParam(required = false) @ApiParam("订单类型") Integer payType,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size
    ) {
        Instant startCreateInstant = startCreateTime == null ? null : localDateToInstant(startCreateTime);
        Instant endCreateInstant = endCreateTime == null ? null : localDateToInstant(endCreateTime.plusDays(1));
        PageResult<List<OrderBelongDto>> orderBelongList = orderClient.getOrderBelongList(startCreateInstant, endCreateInstant, orderNumber, payType, current, size);
        List<OrderBelongDto> data = orderBelongList.getData();
        Pagination pagination = orderBelongList.getPagination();
        if (data == null) {
            return PageResult.empty();
        }
        Set<Integer> salesIds = new HashSet<>();
        data.forEach(dto -> {
            salesIds.add(dto.getSalesId());
            salesIds.add(dto.getUserId());
            salesIds.add(dto.getHeadId());
            salesIds.add(dto.getDirectorId());
            salesIds.add(dto.getManagerId());
        });
        Map<Integer, UcUsers> salesMap = userClient.batchGetUserMap(BatchReq.create(salesIds)).getData();
        for (OrderBelongDto dto : data) {
            if (ObjectUtil.isNotEmpty(dto)) {
                if (dto.getUserId() != null) {
                    String nickName = ObjectUtil.isNotEmpty(salesMap.get(dto.getUserId()).getNickName()) ? salesMap.get(dto.getUserId()).getNickName() : null;
                    dto.setNickName(nickName);
                }
                if (dto.getSalesId() != null) {
                    String realName = ObjectUtil.isNotEmpty(salesMap.get(dto.getSalesId()).getRealName()) ? salesMap.get(dto.getSalesId()).getRealName() : null;
                    dto.setSalesName(realName);
                }
                if (dto.getHeadId() != null) {
                    String realName = ObjectUtil.isNotEmpty(salesMap.get(dto.getHeadId()).getRealName()) ? salesMap.get(dto.getHeadId()).getRealName() : null;
                    dto.setHeadName(realName);
                }
                if (dto.getManagerId() != null) {
                    String realName = ObjectUtil.isNotEmpty(salesMap.get(dto.getManagerId()).getRealName()) ? salesMap.get(dto.getManagerId()).getRealName() : null;
                    dto.setManagerName(realName);
                }
                if (dto.getDirectorId() != null) {
                    String realName = ObjectUtil.isNotEmpty(salesMap.get(dto.getDirectorId()).getRealName()) ? salesMap.get(dto.getDirectorId()).getRealName() : null;
                    dto.setDirectorName(realName);
                }
            }
        }
        return PageResult.success(data, pagination);

    }

    @ApiOperation("获取订单归属信息操作列表")
    @GetMapping("getOrderBelongOperateRecordList")
    BaseResult<List<OrderBelongOperateRecordDto>> getOrderBelongOperateRecordList(@RequestParam @ApiParam("订单id") Integer orderId) {
        List<RsOrderBelongOperateRecord> recordList = orderClient.getOrderBelongOperateRecordList(orderId).getData();
        List<OrderBelongOperateRecordDto> list = new ArrayList<>();
        if (recordList == null) {
            return BaseResult.success(list);
        }
        Set<Integer> userIds = new HashSet<>();
        recordList.forEach(
                record -> userIds.add(record.getOperateId())
        );
        Map<Integer, UcUsers> usersMap = userClient.batchGetUserMap(BatchReq.create(userIds)).getData();

        for (RsOrderBelongOperateRecord record : recordList) {
            if (ObjectUtil.isNotEmpty(record)) {
                OrderBelongOperateRecordDto operateRecordDto = OrderBelongOperateRecordDto.builder()
                        .content(record.getContent())
                        .gmtCreate(record.getGmtCreate())
                        .orderId(record.getOrderId())
                        .operateId(record.getOperateId())
                        .id(record.getId())
                        .build();
                if (ObjectUtil.isNotEmpty(record.getOperateId())) {
                    UcUsers users = usersMap.get(record.getOperateId());
                    operateRecordDto.setAvatarUrl(users.getAvatarUrl());
                    operateRecordDto.setTitle(String.format("{%s}%s", users.getRealName(), record.getTitle()));
                } else {
                    operateRecordDto.setTitle(String.format("{%s}%s", "系统", record.getTitle()));
                }
                list.add(operateRecordDto);
            }
        }
        return BaseResult.success(list);
    }

//    @GetMapping("{orderId}")
//    @ApiOperation("通过主订单ID获取订单基本信息")
//    public BaseResult<OrderInfo> getOrder(@PathVariable Integer orderId) {
//        OrderInfo order = orderService.getOrder(orderId).orElseThrow(RecordNotExistedException::new);
//        return BaseResult.success(order);
//    }

    @GetMapping("number/{orderNumber}")
    @ApiOperation("通过主订单号获取订单基本信息")
    public BaseResult<OrderInfo> getOrder(@PathVariable @ApiParam("订单编号") String orderNumber) {
        OrderInfo order = orderService.getOrder(orderNumber)
                .orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(order);
    }

//    @GetMapping("{orderId}/detail")
//    @ApiOperation("通过主订单ID获取订单详情")
//    public BaseResult<OrderDetail> getOrderDetailByNumber(@PathVariable Integer orderId) {
//        OrderDetail orderDetail = orderService.getOrderDetail(orderId)
//                .orElseThrow(RecordNotExistedException::new);
//        return BaseResult.success(orderDetail);
//    }

    @PostMapping("{orderNumber}/sync")
    @ApiOperation("同步主订单状态")
    public BaseResult<Void> syncOrder(@PathVariable @ApiParam("订单编号") String orderNumber) {
        Integer orderId = commonService.getOrderIdByNumber(orderNumber);
        return orderService.syncOrder(orderId);
    }

    @GetMapping("number/order-detail")
    @ApiOperation("通过主订单号获取订单详情")
    public BaseResult<OrderDetail> getOrderDetail(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam @ApiParam("订单编号") String orderNumber) {
        OrderDetail orderDetail = orderService.getOrderDetail(companyType, orderNumber).orElseThrow(RecordNotExistedException::new);
        return BaseResult.success(orderDetail);
    }

    @ApiOperation("查询主订单列表")
    @GetMapping("list")
    public PageResult<List<OrderDtoResp>> getSubOrderByFilter(
            @ApiIgnore @RequestHeader(COMPANY_TYPE) Integer companyType,
            @RequestParam(required = false) @ApiParam("订单状态") Integer orderStatus,
            @RequestParam(required = false) @ApiParam("销售ids") List<Integer> salesIds,
            @RequestParam(required = false) @ApiParam("创建开始时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate startCreateTime,
            @RequestParam(required = false) @ApiParam("创建结束时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate endCreateTime,
            @RequestParam(required = false) @ApiParam("支付开始时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate startPayTime,
            @RequestParam(required = false) @ApiParam("支付结束时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate endPayTime,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("用户code") String userCode,
            @RequestParam(required = false) @ApiParam("产品等级") Integer productLevel,
            @RequestParam(required = false) @ApiParam("sku列表") List<Integer> skuList,
            @RequestParam(required = false) @ApiParam("近60天是否添加高级助教") Boolean isMarkWxUser,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        Instant startCreateInstant = startCreateTime == null ? null : localDateToInstant(startCreateTime);
        Instant endCreateInstant = endCreateTime == null ? null : localDateToInstant(endCreateTime.plusDays(1));
        Instant startPayInstant = startPayTime == null ? null : localDateToInstant(startPayTime);
        Instant endPayInstant = endPayTime == null ? null : localDateToInstant(endPayTime.plusDays(1));
        Integer userId = StringUtils.isNotBlank(userCode) ? DesensitizeUtil.maskToId(userCode) : null;
        return orderService.getOrderByFilter(companyType, orderStatus, salesIds,
                startCreateInstant, endCreateInstant, startPayInstant, endPayInstant, searchContent, userId, productLevel, skuList, isMarkWxUser, current, size);

    }

    @ApiOperation("查询销售可见主订单列表")
    @GetMapping("list/sales/visible")
    public PageResult<List<DeptVisibleOrderResp>> getSalesVisibleOrderByFilter(
            @ApiIgnore @RequestHeader(COMPANY_TYPE) Integer companyType,
            @ApiIgnore @RequestHeader(USER_ID) Integer salesId,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("是否有相关退款") Boolean isRefund,
            @RequestParam(required = false) @ApiParam("支付开始时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate startPayTime,
            @RequestParam(required = false) @ApiParam("支付结束时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate endPayTime,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return orderService.getOrderBySalesVisible(companyType, salesId, searchContent, isRefund, startPayTime, endPayTime, current, size);
    }

    @GetMapping("hg/auditManage")
    @ApiOperation("订单合规审核管理新")
    public PageResult<List<OrderDtoResp>> getOrderHgAuditManage(
            @RequestParam(required = false) List<Integer> salesIds,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startPayTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endPayTime,
            @RequestParam(required = false) Integer feedbackStatus,
            @RequestParam(required = false) Integer productLevel,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("机审状态") Integer autoAuditStatus,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return orderService.getOrderByFilter(companyType, null, salesIds, startPayTime, endPayTime, feedbackStatus, productLevel,
                auditStatus != null ? Arrays.asList(auditStatus) : null, autoAuditStatus, searchContent, current, size);
    }

    @GetMapping("hg/auto-audit-info")
    @ApiOperation("获取自动审核信息")
    public BaseResult<List<OrderHgAutoAuditResp>> getOrderHgAutoAuditInfo(
            @RequestParam @ApiParam("订单编号") String orderNumber) {
        Integer orderId = commonService.getOrderIdByNumber(orderNumber);
        return orderService.getOrderHgAutoAuditInfo(orderId);
    }

    @GetMapping("hg/auditManage/my")
    @ApiOperation("订单合规审核")
    public PageResult<List<OrderDtoResp>> getMyOrderHgAuditManage(
            @RequestParam(required = false) List<Integer> salesIds,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startPayTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endPayTime,
            @RequestParam(required = false) Integer feedbackStatus,
            @RequestParam(required = false) Integer productLevel,
            @RequestParam(required = false) Integer auditStatus,
            @RequestParam(required = false) @ApiParam("机审状态") Integer autoAuditStatus,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(USER_ID) @ApiIgnore Integer auditorId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        List<Integer> hgAuditStatus = Arrays.asList(OrderAuditStatusConstant.HGAUDITING.getValue(), OrderAuditStatusConstant.CLOSED.getValue(), OrderAuditStatusConstant.PASSED.getValue());
        return orderService.getOrderByFilter(companyType, auditorId, salesIds, startPayTime, endPayTime, feedbackStatus, productLevel,
                auditStatus != null ? Arrays.asList(auditStatus) : hgAuditStatus, autoAuditStatus, searchContent, current, size);
    }

    @PostMapping("avoidSign")
    @ApiOperation("设置免签")
    public BaseResult<Void> updateAvoidSign(
            @RequestParam @ApiParam("订单编号") String orderNumber,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId
    ) {
        Integer orderId = commonService.getOrderIdByNumber(orderNumber);
        return orderClient.noNeedSign(orderId, userId);
    }

    @GetMapping("merchants")
    @ApiOperation("获取商户列表")
    public BaseResult<List<Option>> getMerchantList(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(required = false) Integer payType) {
        List<Option> merchantList = orderService.getMerchantList(companyType, payType);
        return BaseResult.success(merchantList);
    }

    @ApiOperation("关闭订单")
    @PostMapping("close")
    public BaseResult<Void> closeOrder(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("订单编号") String orderNumber) {
        return orderService.closeOrder(orderNumber, userId);
    }

//    @ApiOperation("生成签字pdf")
//    @PostMapping("signPdf")
//    public BaseResult<String> signPdf(
//            @RequestParam @ApiParam("订单id") Integer orderId) {
//        Integer companyType = AuthContextHolder.getCompanyType();
//        return orderService.generateSignPdf(orderId, companyType);
//    }

    @ApiOperation("查询用户订单")
    @GetMapping("user")
    public BaseResult<List<OrderSkuInfoResp>> getUserOrders(
            @ApiIgnore @RequestHeader(value = COMPANY_TYPE) Integer companyType,
            @RequestParam @ApiParam("用户编号") String userNumber,
            @RequestParam(required = false) @ApiParam("订单状态 1:已支付 2:待支付 3:已关闭 4:已开通 5:已退款") Integer orderStatus) {
        Integer userId = commonService.getUserIdByNumber(companyType, userNumber);
        return orderService.getMyOrderList(companyType, userId, orderStatus);
    }

    @ApiOperation("查询用户订单分页")
    @GetMapping("user/page")
    public PageResult<List<OrderSkuInfoResp>> getUserOrderList(
            @ApiIgnore @RequestHeader(value = COMPANY_TYPE) Integer companyType,
            @RequestParam @ApiParam("用户code") String userCode,
            @RequestParam(required = false) @ApiParam("订单状态") Integer orderStatus,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size) {
        return orderService.getUserOrderList(companyType, userCode, orderStatus, current, size);
    }

    @GetMapping("generateFeedbackPdf")
    @ApiOperation("生成回访问卷pdf")
    ResponseEntity<Void> feedbackPdf(
            @ApiIgnore HttpServletResponse response,
            @RequestParam @ApiParam("订单编号") String orderNumber,
            @RequestParam @ApiParam("token") String token
    ) throws IOException {
        Integer userId = (Integer) authService.getTokenPayload(token).get(USER_ID);
        Integer companyType = (Integer) authService.getTokenPayload(token).get(COMPANY_TYPE);
        response.setCharacterEncoding("UTF-8");
        if (ObjectUtil.isEmpty(userId)) {
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().write("权限不足");
            response.getWriter().flush();
            return ResponseEntity.ok().build();
        }
        response.setHeader("authorization", "Bearer " + token);
        Integer orderId = commonService.getOrderIdByCompanyNumber(companyType, orderNumber);
        orderService.generateFeedbackPdf(response, orderId);
        return ResponseEntity.ok().build();
    }

    @GetMapping("feedback/generate-pdf")
    @ApiOperation("生成回访问卷pdf")
    ResponseEntity<Void> generateFeedbackPdf(
            @ApiIgnore HttpServletResponse response,
            @RequestParam @ApiParam("订单编号") String orderNumber,
            @RequestParam @ApiParam("token") String token
    ) throws IOException {
        Integer userId = (Integer) authService.getTokenPayload(token).get(USER_ID);
        Integer companyType = (Integer) authService.getTokenPayload(token).get(COMPANY_TYPE);
        response.setCharacterEncoding("UTF-8");
        if (ObjectUtil.isEmpty(userId)) {
            response.setStatus(HttpServletResponse.SC_OK);
            response.getWriter().write("权限不足");
            response.getWriter().flush();
            return ResponseEntity.ok().build();
        }
        response.setHeader("authorization", "Bearer " + token);
        Integer orderId = commonService.getOrderIdByCompanyNumber(companyType, orderNumber);
        orderService.generateFeedbackPdf2(response, orderId);
        return ResponseEntity.ok().build();
    }

//    @ApiOperation("获取签字url")
//    @PostMapping("signUrl")
//    public BaseResult<String> getSignUrl(
//            @ApiIgnore @RequestHeader(COMPANY_TYPE) Integer companyType,
//            @RequestParam @ApiParam("订单id") Integer orderId) {
//        return orderService.getSignUrl(orderId, companyType);
//    }

    @PostMapping("freeze")
    @ApiOperation("冻结订单")
    public BaseResult<Void> freezeOrder(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("订单号") String orderNumber) {
        return orderClient.freezeOrder(orderNumber, userId);
    }

    @PostMapping("flow/batch/assign/record/export")
    @ApiOperation("导出批量分配记录")
    public BaseResult<RsFileExportRecord> exportBatchAssignRecord(@RequestParam @ApiParam("文件名") String fileName,
                                                                  @RequestParam @ApiParam("批次ID") Long batchId) {
        RsFileExportRecord record = exportExcelService.genExportRecord(fileName);
        ExportFileInfo<Object> info = ExportFileInfo.builder()
                .recordId(record.getId())
                .fileEnum(ExportFileEnum.EXPORT_BATCH_ASSIGN_RECORD)
                .t(batchId)
                .build();
        BaseResult<String> sendRes = statisticsClient.exportExcel(info);
        if (sendRes.isFail()) {
            exportExcelService.markExportFile(info.getRecordId());
        }
        return BaseResult.success(record);
    }

    @ApiOperation("合规审核批量分配-筛选合规待分配订单")
    @PostMapping("batch/assign/find")
    public BaseResult<BatchAssignResp> getOrderCount(
            @RequestParam(required = false) @ApiParam("支付开始时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATETIME) LocalDateTime startPayTime,
            @RequestParam(required = false) @ApiParam("支付结束时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATETIME) LocalDateTime endPayTime,
            @RequestParam(required = false) Integer productLevel,
            @RequestParam(required = false) Integer autoAuditStatus,
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType
    ) {
        return orderClient.getOrderCount(companyType, autoAuditStatus, localDateTimeToInstant(startPayTime), localDateTimeToInstant(endPayTime), productLevel);
    }

    @ApiOperation("合规审核批量分配")
    @PostMapping("flow/batch/assign")
    public BaseResult<Void> batchAssign(@RequestBody BatchAssignReq batchAssignReq) {
        batchAssignReq.setOperatorId(AuthContextHolder.getUserId());
        orderClient.batchAssign(batchAssignReq);
        return BaseResult.successful();
    }

    @ApiOperation("合规审核-筛选待分配订单")
    @PostMapping("department-order-count")
    public BaseResult<List<BatchAverageAssignResp>> filterAverageAssignOrder(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestBody BatchAverageAssignFilterReq req
    ) {
        return orderClient.filterAverageAssignOrder(companyType, req);
    }

    @ApiOperation("合规审核批量分配（平均分配）")
    @PostMapping("batch/average-assign")
    public BaseResult<Void> batchAverageAssign(
            @ApiIgnore @RequestHeader(USER_ID) Integer userId,
            @ApiIgnore @RequestHeader(COMPANY_TYPE) Integer companyType,
            @RequestBody BatchAverageAssignReq req) {
        req.setOperatorId(userId);
        orderClient.batchAverageAssign(companyType, req);
        return BaseResult.successful();
    }

    @GetMapping("batch/assign/record/list")
    @ApiOperation("批量分配记录列表")
    public PageResult<List<AssignBatchRecordResp>> getRsOrderAssignBatchRecord(
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return orderService.getRsOrderAssignBatchRecord(current, size);
    }

    @GetMapping("extract/detail")
    @ApiOperation("获取销售订单抽取结果")
    public BaseResult<List<AssignDetailResp>> getExtractDetail(
            @RequestParam @ApiParam("批次ID") Long batchId
    ) {
        List<AssignDetailResp> extractDetail = orderService.getExtractDetail(batchId);
        return BaseResult.success(extractDetail);
    }

    @GetMapping("assign/detail")
    @ApiOperation("获取合规订单分配结果")
    public BaseResult<List<AssignDetailResp>> getAssignDetail(
            @RequestParam @ApiParam("批次ID") Long batchId
    ) {
        List<AssignDetailResp> assignDetail = orderService.getAssignDetail(batchId);
        return BaseResult.success(assignDetail);
    }

    @PostMapping("assign/hg")
    @ApiOperation("分配合规")
    public BaseResult<Void> assignHg(
            @RequestParam @ApiParam("订单编号") String orderNumber,
            @RequestParam @ApiParam("合规审核人ID") Integer HgAuditorId,
            @RequestHeader(USER_ID) @ApiIgnore Integer hgAssignId
    ) {
        return orderClient.assignHg(orderNumber, HgAuditorId, hgAssignId);
    }

    @GetMapping("auditing/hg")
    @ApiOperation("再次分配-获取合规待审核订单")
    public PageResult<List<OrderDtoResp>> auditingHg(
            @RequestParam(required = false) @ApiParam("审核人") Integer auditorId,
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return orderService.getOrderByFilter(companyType, auditorId, null, null, null, null, null, Arrays.asList(OrderAuditStatusConstant.HGAUDITING.getValue()), null, null, current, size);
    }

    @PostMapping("reallocate/hg")
    @ApiOperation("再次分配")
    public BaseResult<Void> reallocateHg(
            @RequestHeader(USER_ID) @ApiIgnore Integer operatorId,
            @RequestBody @Valid BatchAssignReq req
    ) {
        req.setOperatorId(operatorId);
        return orderClient.reallocateHg(req);
    }

    @PostMapping("auditing/pass")
    @ApiOperation("审核-通过")
    public BaseResult<Void> auditingPass(
            @RequestParam @ApiParam("订单编号") String orderNumber,
            @RequestHeader(USER_ID) @ApiIgnore Integer auditorId
    ) {
        return orderClient.auditing(auditorId, orderNumber, OrderAuditStatusConstant.PASSED.getValue(), null);
    }

    @PostMapping("auditing/close")
    @ApiOperation("审核-关闭")
    public BaseResult<Void> auditingClose(
            @RequestParam @ApiParam("订单编号") String orderNumber,
            @RequestParam @ApiParam("内容") String content,
            @RequestHeader(USER_ID) @ApiIgnore Integer auditorId
    ) {
        return orderClient.auditing(auditorId, orderNumber, OrderAuditStatusConstant.CLOSED.getValue(), content);
    }

    @GetMapping("chat/record")
    @ApiOperation("审核-用户相关聊天记录")
    public BaseResult<List<ChatRecordResp>> getChatRecord(
            @RequestParam @ApiParam("用户编号") String number,
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType
    ) {
        Integer userId = commonService.getUserIdByNumber(companyType, number, null);
        List<ChatRecordResp> chatRecordRespList = orderService.getChatRecord(companyType, userId);
        return BaseResult.success(chatRecordRespList);
    }

    @PostMapping("feedback/artificial/create")
    @ApiOperation("标记需要人工回访")
    public BaseResult<String> createArtificialFeedback(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam Integer orderId) {
        return orderClient.createArtificialFeedback(userId, orderId);
    }

    @PostMapping("feedback/artificial/strategy/create")
    @ApiOperation("智投人工回访结果")
    public BaseResult<String> createArtificialStrategyFeedback(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam Integer orderId,
            @RequestParam @ApiParam("是否通过") Boolean isPass,
            @RequestParam @ApiParam("备注") String remark,
            @RequestParam(required = false) @ApiParam("视频链接") String videoUrl) {
        return orderClient.createArtificialStrategyFeedback(userId, orderId, isPass, remark, videoUrl);
    }

    @PostMapping("feedback/artificial/update")
    @ApiOperation("更新标记人工回访状态")
    public BaseResult<String> updateArtificialFeedback(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("人工回访id") Long id,
            @RequestParam @ApiParam("是否通过") Boolean isPass,
            @RequestParam @ApiParam("备注") String remark,
            @RequestParam(required = false) @ApiParam("视频链接") String videoUrl) {
        return orderClient.updateArtificialFeedback(userId, id, isPass, remark, videoUrl);
    }

    @GetMapping("mark-record-list")
    @ApiOperation("订单标记记录列表")
    public PageResult<List<OrderMarkRecordResp>> getMarkRecordList(
            @RequestParam(required = false) @ApiParam("标记开始时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate startMarkDate,
            @RequestParam(required = false) @ApiParam("标记结束时间") @DateTimeFormat(pattern = DEFAULT_PATTERN_DATE) LocalDate endMarkDate,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchText,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("分页数量") Integer size
    ) {
        if (ObjectUtil.isNotEmpty(searchText)) {
            searchText = commonService.getOrderIdByNumber(searchText).toString();
        }
        return orderService.getMarkRecordList(startMarkDate, endMarkDate, searchText, current, size);
    }
}
