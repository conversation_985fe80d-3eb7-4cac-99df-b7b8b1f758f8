package cn.shrise.radium.adminapi.controller.douyin;

import cn.shrise.radium.adminapi.service.douyin.DyConfigService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.entity.BaseConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api
@RestController
@RequestMapping("dy/config")
@RequiredArgsConstructor
public class DyConfigController {

    private final DyConfigService configService;

    @GetMapping("client")
    @ApiOperation("获取抖音应用信息")
    public BaseResult<List<BaseConfig>> getClientList(
            @RequestParam @ApiParam("应用类型") Integer companyType) {
        final List<BaseConfig> clientList = configService.getClientList(companyType);
        return BaseResult.success(clientList);
    }
}
