package cn.shrise.radium.adminapi.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.req.ResetStaffPasswordReq;
import cn.shrise.radium.adminapi.service.StaffService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcRole;
import cn.shrise.radium.userservice.req.CreateOrUpdateRoleReq;
import cn.shrise.radium.userservice.req.ModifyPasswordReq;
import cn.shrise.radium.userservice.req.SwitchPasswordReq;
import cn.shrise.radium.userservice.resp.MainStaffResp;
import cn.shrise.radium.userservice.resp.StaffInfoResp;
import cn.shrise.radium.workwxservice.resp.ContactUserRelationResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;

import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@Api
@Slf4j
@RestController
@RequestMapping("staff")
@RequiredArgsConstructor
public class StaffController {

    private final StaffService staffService;
    private final UserClient userClient;

    @PostMapping("update/password")
    @ApiOperation("更新密码")
    public BaseResult<Boolean> updatePassword(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("老密码") String oldPassword,
            @RequestParam @ApiParam("新密码") String newPassword) {
        return staffService.updatePassword(companyType, userId, oldPassword, newPassword);
    }

    @PostMapping("modify/password")
    @ApiOperation("首次登录/忘记密码")
    public BaseResult<Boolean> modifyPassword(
            @RequestBody @Valid ModifyPasswordReq req) {
        return staffService.modifyPassword(req);
    }

    @PostMapping("switch/password")
    @ApiOperation("90天更换密码")
    public BaseResult<Boolean> switchPassword(
            @RequestBody @Valid SwitchPasswordReq req) {
        return staffService.switchPassword(req);
    }

    @PostMapping("search")
    @ApiOperation("搜索主账号")
    public BaseResult<List<MainStaffResp>> searchStaff(
            @RequestParam @ApiParam("用户名") String searchContent) {
        return userClient.searchStaff(AuthContextHolder.getCompanyType(), searchContent, true);
    }

    @GetMapping("role/list")
    @ApiOperation("员工角色列表")
    public PageResult<List<UcRole>> getStaffRoleList(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return userClient.getStaffRoleList(companyType, current, size);
    }

    @PostMapping("role")
    @ApiOperation("新建/编辑角色")
    public BaseResult<String> createOrUpdateRole(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestBody CreateOrUpdateRoleReq req
    ) {
        if (ObjectUtil.isEmpty(req.getId())) {
            req.setCompanyType(companyType);
        }
        return userClient.createOrUpdateRole(req);
    }

    @GetMapping("info/{staffId}")
    @ApiOperation("员工信息详情")
    public BaseResult<StaffInfoResp> getStaffInfo(
            @PathVariable @ApiParam("员工id") Integer staffId) {
        StaffInfoResp staffInfo = staffService.getStaffInfo(staffId);
        return BaseResult.success(staffInfo);
    }

    @ApiOperation("获取销售好友")
    @GetMapping("{salesId}/relation/page")
    public PageResult<List<ContactUserRelationResp>> getFriendRelationPage(
            @PathVariable @ApiParam("用户ID") Integer salesId,
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(defaultValue = "1") @ApiParam("当前分页") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("每页数量") Integer size) {
        return staffService.getFriendRelationPage(salesId, companyType, current, size);
    }

    @GetMapping("check")
    @ApiOperation(value = "检验登录账号")
    public BaseResult<Boolean> checkLoginStaff(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("员工id") Integer staffId,
            @RequestParam @ApiParam("应用id") String appId,
            @RequestParam @ApiParam("设备号") String deviceNumber) {
        return staffService.checkLoginStaff(staffId, userId, appId, deviceNumber);
    }

}
