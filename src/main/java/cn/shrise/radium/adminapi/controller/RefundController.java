package cn.shrise.radium.adminapi.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.resp.order.RefundCommentResp;
import cn.shrise.radium.adminapi.resp.order.RefundManualResp;
import cn.shrise.radium.adminapi.resp.order.RefundProcessResp;
import cn.shrise.radium.adminapi.service.CommonService;
import cn.shrise.radium.adminapi.service.ExportExcelService;
import cn.shrise.radium.adminapi.service.OrderService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.orderservice.constant.RefundSignStatusEnum;
import cn.shrise.radium.orderservice.dto.OrderExtInfoDto;
import cn.shrise.radium.orderservice.entity.RsFileExportRecord;
import cn.shrise.radium.orderservice.entity.RsRefundFlowRecord;
import cn.shrise.radium.orderservice.req.CreateCourseRefundCommentReq;
import cn.shrise.radium.orderservice.req.ExportRefundManualReq;
import cn.shrise.radium.orderservice.req.RetryRefundPlanReq;
import cn.shrise.radium.orderservice.resp.*;
import cn.shrise.radium.statisticsservice.StatisticsClient;
import cn.shrise.radium.statisticsservice.bean.ExportFileInfo;
import cn.shrise.radium.statisticsservice.constant.ExportFileEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

@Api
@Slf4j
@RestController
@RequestMapping("refunds")
@RequiredArgsConstructor
public class RefundController {
    private final OrderService orderService;
    private final CommonService commonService;
    private final ExportExcelService exportExcelService;
    private final StatisticsClient statisticsClient;

    @ApiOperation("销售——我的相关退款——退款订单列表")
    @GetMapping("sales/relate")
    public PageResult<List<SalesRelateRefundResp>> getSalesRelateRefundList(
            @RequestParam(required = false) @ApiParam("退款状态") Integer refundStatus,
            @RequestParam(required = false) @ApiParam("退款订单号") String refundNumber,
            @RequestParam(required = false) @ApiParam("主订单号") String orderNumber,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        Integer companyType = AuthContextHolder.getCompanyType();
        Integer userId = AuthContextHolder.getUserId();
        return orderService.getSalesRelateRefundList(userId, null,
                companyType, refundStatus, refundNumber, orderNumber,
                null, null,
                null, null,
                null, null,
                null, null, null,
                current, size);
    }

    @ApiOperation("商务——发起退款——退款订单列表")
    @GetMapping("business")
    public PageResult<List<SalesRelateRefundResp>> getBusinessRefundList(
            @RequestParam(required = false) @ApiParam("退款状态") Integer refundStatus,
            @RequestParam(required = false) @ApiParam("退款订单号") String refundNumber,
            @RequestParam(required = false) @ApiParam("主订单号") String orderNumber,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        Integer companyType = AuthContextHolder.getCompanyType();
        Integer creatorId = AuthContextHolder.getUserId();
        return orderService.getSalesRelateRefundList(null, Collections.singletonList(creatorId),
                companyType, refundStatus, refundNumber, orderNumber,
                null, null,
                null, null,
                null, null,
                null, null, null,
                current, size);
    }

    @ApiOperation("商务——退款订单管理——退款订单列表")
    @GetMapping("business/manager")
    public PageResult<List<SalesRelateRefundResp>> getBusinessRefundManagerList(
            @RequestParam(required = false) @ApiParam("退款状态") Integer refundStatus,
            @RequestParam(required = false) @ApiParam("退款订单号") String refundNumber,
            @RequestParam(required = false) @ApiParam("主订单号") String orderNumber,
            @RequestParam(required = false) @ApiParam("开始创建时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createStartTime,
            @RequestParam(required = false) @ApiParam("结束创建时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createEndTime,
            @RequestParam(required = false) @ApiParam("开始退款时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate refundStartTime,
            @RequestParam(required = false) @ApiParam("结束退款时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate refundEndTime,
            @RequestParam(required = false) @ApiParam("产品属性") Integer productLevel,
            @RequestParam(required = false) @ApiParam("确认书状态") Integer readStatus,
            @RequestParam(required = false) @ApiParam("退款合同签字状态") RefundSignStatusEnum refundSignStatus,
            @RequestParam(required = false) @ApiParam("提交人Id") List<Integer> creatorIds,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        Integer companyType = AuthContextHolder.getCompanyType();
        return orderService.getSalesRelateRefundList(null, creatorIds,
                companyType, refundStatus, refundNumber, orderNumber,
                createStartTime, createEndTime,
                refundStartTime, refundEndTime,
                productLevel, null,
                null, readStatus, refundSignStatus,
                current, size);
    }

    @ApiOperation("商务——退款订单审核——退款订单列表")
    @GetMapping("business/audit")
    public PageResult<List<SalesRelateRefundResp>> getBusinessRefundAuditList(
            @RequestParam(required = false) @ApiParam("退款状态") Integer refundStatus,
            @RequestParam(required = false) @ApiParam("退款订单号") String refundNumber,
            @RequestParam(required = false) @ApiParam("主订单号") String orderNumber,
            @RequestParam(required = false) @ApiParam("开始创建时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createStartTime,
            @RequestParam(required = false) @ApiParam("结束创建时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createEndTime,
            @RequestParam(required = false) @ApiParam("开始退款时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate refundStartTime,
            @RequestParam(required = false) @ApiParam("结束退款时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate refundEndTime,
            @RequestParam(required = false) @ApiParam("产品属性") Integer productLevel,
            @RequestParam(required = false) @ApiParam("确认书状态") Integer readStatus,
            @RequestParam(required = false) @ApiParam("退款合同签字状态") RefundSignStatusEnum refundSignStatus,
            @RequestParam(required = false) @ApiParam("提交人Id") List<Integer> creatorIds,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        Integer companyType = AuthContextHolder.getCompanyType();
        Integer auditorId = null;
        return orderService.getSalesRelateRefundList(null, creatorIds,
                companyType, refundStatus, refundNumber, orderNumber,
                createStartTime, createEndTime,
                refundStartTime, refundEndTime,
                productLevel, "refundOrderAudit",
                auditorId, readStatus, refundSignStatus,
                current, size);
    }

    @ApiOperation("商务——退款确认——退款订单列表")
    @GetMapping("business/confirm")
    public PageResult<List<SalesRelateRefundResp>> getBusinessRefundConfirmList(
            @RequestParam(required = false) @ApiParam("退款状态") Integer refundStatus,
            @RequestParam(required = false) @ApiParam("退款订单号") String refundNumber,
            @RequestParam(required = false) @ApiParam("主订单号") String orderNumber,
            @RequestParam(required = false) @ApiParam("开始创建时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createStartTime,
            @RequestParam(required = false) @ApiParam("结束创建时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createEndTime,
            @RequestParam(required = false) @ApiParam("开始退款时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate refundStartTime,
            @RequestParam(required = false) @ApiParam("结束退款时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate refundEndTime,
            @RequestParam(required = false) @ApiParam("产品属性") Integer productLevel,
            @RequestParam(required = false) @ApiParam("确认书状态") Integer readStatus,
            @RequestParam(required = false) @ApiParam("退款合同签字状态") RefundSignStatusEnum refundSignStatus,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        Integer companyType = AuthContextHolder.getCompanyType();
        return orderService.getSalesRelateRefundList(null, null,
                companyType, refundStatus, refundNumber, orderNumber,
                createStartTime, createEndTime,
                refundStartTime, refundEndTime,
                productLevel, "refundOrderConfirm",
                null, readStatus, refundSignStatus,
                current, size);
    }

    @ApiOperation("财务——订单退款——退款订单列表")
    @GetMapping("finance")
    public PageResult<List<SalesRelateRefundResp>> getfinanceRefundList(
            @RequestParam(required = false) @ApiParam("退款状态") Integer refundStatus,
            @RequestParam(required = false) @ApiParam("退款订单号") String refundNumber,
            @RequestParam(required = false) @ApiParam("主订单号") String orderNumber,
            @RequestParam(required = false) @ApiParam("开始创建时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createStartTime,
            @RequestParam(required = false) @ApiParam("结束创建时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createEndTime,
            @RequestParam(required = false) @ApiParam("开始退款时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate refundStartTime,
            @RequestParam(required = false) @ApiParam("结束退款时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate refundEndTime,
            @RequestParam(required = false) @ApiParam("产品属性") Integer productLevel,
            @RequestParam(required = false) @ApiParam("确认书状态") Integer readStatus,
            @RequestParam(required = false) @ApiParam("退款合同签字状态") RefundSignStatusEnum refundSignStatus,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        Integer companyType = AuthContextHolder.getCompanyType();
        return orderService.getSalesRelateRefundList(null, null,
                companyType, refundStatus, refundNumber, orderNumber,
                createStartTime, createEndTime,
                refundStartTime, refundEndTime,
                productLevel, "financeRefundOrder",
                null, readStatus, refundSignStatus,
                current, size);
    }

    @ApiOperation("关闭退款订单")
    @PatchMapping("order/close")
    public BaseResult<RsRefundFlowRecord> closeRefundOrder(
            @RequestParam @ApiParam("退款单Id") Integer refundId) {
        Integer creatorId = AuthContextHolder.getUserId();
        RsRefundFlowRecord rsRefundFlowRecord = orderService.closeRefundOrder(creatorId, refundId);
        return BaseResult.success(rsRefundFlowRecord);
    }

    @ApiOperation("根据退款单ID获取退款流程进度")
    @PostMapping("process")
    public BaseResult<List<RefundProcessResp>> getRefundProcessRespListByRefundId(
            @RequestParam @ApiParam("退款单编号") String refundNumber
    ) {
        Integer refundId = commonService.getRefundIdByNumber(refundNumber);
        List<RefundProcessResp> refundProcessRespListByRefundId = orderService.getRefundProcessRespListByRefundId(refundId);
        return BaseResult.success(refundProcessRespListByRefundId);
    }

    @ApiOperation("获取订单详情")
    @GetMapping("order/id")
    public BaseResult<OrderDetailResp> getOrderDetailRespByRefundId(
            @RequestParam(required = false) @ApiParam("退款单编号") String refundNumber,
            @RequestParam(required = false) @ApiParam("订单编号") String orderNumber
    ) {
        Integer orderId = null;
        Integer refundId = null;
        if (ObjectUtils.isNotEmpty(refundNumber)) {
            refundId = commonService.getRefundIdByNumber(refundNumber);
        }
        if (ObjectUtil.isNotEmpty(orderNumber)) {
            orderId = commonService.getOrderIdByNumber(orderNumber);
        }
        OrderDetailResp orderDetailResp = orderService.getOrderDetailRespByRefundId(refundId, orderId);
        return BaseResult.success(orderDetailResp);
    }

    @ApiOperation("通过退款单ID获取评论信息")
    @GetMapping("comment")
    public BaseResult<List<RefundCommentResp>> getRsCourseRefundCommentByRefundId(
            @RequestParam @ApiParam("退款单编号") String refundNumber
    ) {
        Integer refundId = commonService.getRefundIdByNumber(refundNumber);
        List<RefundCommentResp> refundCommentResp = orderService.getRefundCommentRespByRefundId(refundId);
        return BaseResult.success(refundCommentResp);
    }

    @ApiOperation("发评论")
    @PostMapping("comment")
    public BaseResult<String> createCourseRefundComment(
            @RequestBody @Valid CreateCourseRefundCommentReq req
    ) {
        req.setUserID(AuthContextHolder.getUserId());
        Integer refundId = commonService.getRefundIdByNumber(req.getRefundNumber());
        req.setRefundID(refundId);
        return orderService.createCourseRefundComment(req);
    }

    @ApiOperation("通过退款单编号获取退款信息")
    @GetMapping
    public BaseResult<RefundOrderResp> getRefundOrderRespByRefundId(
            @RequestParam @ApiParam("退款单编号") String refundNumber
    ) {
        Integer refundId = commonService.getRefundIdByNumber(refundNumber);
        RefundOrderResp refundOrderResp = orderService.getRefundOrderRespByRefundId(refundId);
        return BaseResult.success(refundOrderResp);
    }

    @ApiOperation("获取历史退款记录")
    @GetMapping("record")
    public BaseResult<List<RefundOrderResp>> getRefundOrderRecord(
            @RequestParam @ApiParam("主订单编号") String orderNumber,
            @RequestParam(required = false) @ApiParam("退款状态 10:待提交 20:待分配 30:待审核 40:待确认 50:待退款 60:已退款 65:退款失败 70:已关闭 55:退款中")
            Integer refundStatus
    ) {
        Integer orderId = commonService.getOrderIdByNumber(orderNumber);
        return orderService.getRefundOrderRecord(orderId, refundStatus);
    }

    @ApiOperation("分配退款单")
    @PostMapping("distribute")
    public BaseResult<String> distributeRefundOrder(
            @RequestParam @ApiParam("退款单编号") String refundNumber,
            @RequestParam @ApiParam("分配对象ID") Integer auditorId
    ) {
        Integer refundId = commonService.getRefundIdByNumber(refundNumber);
        return orderService.distributeRefundOrder(refundId, auditorId, AuthContextHolder.getUserId());
    }

    @ApiOperation("再分配退款单")
    @PostMapping("distribute/again")
    public BaseResult<String> distributeRefundOrderAgain(
            @RequestParam @ApiParam("分配新对象ID") Integer newAuditorId,
            @RequestParam @ApiParam("分配旧对象ID") Integer oldAuditorId
    ) {
        return orderService.distributeRefundOrderAgain(newAuditorId, oldAuditorId, AuthContextHolder.getUserId());
    }

    @ApiOperation("审核通过退款单")
    @PostMapping("audit")
    public BaseResult<String> auditorRefundOrder(
            @RequestParam @ApiParam("退款单编号") String refundNumber,
            @RequestParam @ApiParam("是否关闭服务") Boolean isCloseService
    ) {
        Integer refundId = commonService.getRefundIdByNumber(refundNumber);
        return orderService.auditRefundOrder(refundId, AuthContextHolder.getUserId(), isCloseService);
    }

    @ApiOperation("确认退款单")
    @PostMapping("confirm")
    public BaseResult<String> confirmRefundOrder(
            @RequestParam @ApiParam("退款单编号") String refundNumber
    ) {
        Integer refundId = commonService.getRefundIdByNumber(refundNumber);
        return orderService.confirmRefundOrder(refundId, AuthContextHolder.getUserId());
    }

    @ApiOperation("开始退款确认书签字")
    @PostMapping("start/confirm/sign")
    public BaseResult<String> startConfirmSignRefundOrder(
            @RequestParam @ApiParam("退款单编号") String refundNumber
    ) {
        Integer refundId = commonService.getRefundIdByNumber(refundNumber);
        return orderService.startConfirmSignRefundOrder(refundId);
    }

    @ApiOperation("本次退款计划")
    @GetMapping("plan")
    public BaseResult<List<RefundPlanResp>> getRefundPlan(
            @RequestParam(required = false) @ApiParam("订单编号") String orderNumber,
            @RequestParam(required = false) @ApiParam("退款单编号") String refundNumber,
            @RequestParam(required = false) @ApiParam("退款状态 10：待退款，20：已完成，30：退款失败，40：已关闭") Integer refundStatus
    ) {
        Integer orderId = null;
        Integer refundId = null;
        if (ObjectUtils.isNotEmpty(refundNumber)) {
            refundId = commonService.getRefundIdByNumber(refundNumber);
        }
        if (ObjectUtil.isNotEmpty(orderNumber)) {
            orderId = commonService.getOrderIdByNumber(orderNumber);
        }
        return orderService.getRefundPlan(orderId, refundId, refundStatus);
    }

    @ApiOperation("退款计划重试")
    @PostMapping("plan/retry")
    public BaseResult<Void> retryRefundPlan(@RequestBody @Valid RetryRefundPlanReq req) {
        return orderService.retryRefundPlan(req);
    }

    @ApiOperation("重新签字")
    @PostMapping("sign/again")
    public BaseResult<String> signAgain(
            @RequestParam @ApiParam("退款签字审核ID") Integer refundOrderSignId,
            @RequestParam @ApiParam("签字ID") Integer signId,
            @RequestParam @ApiParam("退款单编号") String refundNumber
    ) {
        Integer refundId = commonService.getRefundIdByNumber(refundNumber);
        return orderService.signAgain(refundOrderSignId, signId, refundId);
    }

    @ApiOperation("确认退款签字合同")
    @PostMapping("confirm/contract")
    public BaseResult<String> auditSign(
            @RequestParam @ApiParam("退款单编号") String refundNumber
    ) {
        Integer refundId = commonService.getRefundIdByNumber(refundNumber);
        return orderService.auditSign(refundId);
    }

    @ApiOperation("发起退款")
    @GetMapping(value = "wx_pay")
    public BaseResult<String> wxPayRefund(@RequestParam @ApiParam("退款单编号") String refundNumber) {
        Integer operatorId = AuthContextHolder.getUserId();
        Integer refundId = commonService.getRefundIdByNumber(refundNumber);
        return orderService.wxPayRefundService(refundId, operatorId);
    }

    @ApiOperation("开始退款签字合同")
    @PostMapping("start/sign")
    public BaseResult<String> startSign(
            @RequestParam @ApiParam("退款单编号") String refundNumber
    ) {
        Integer refundId = commonService.getRefundIdByNumber(refundNumber);
        return orderService.startSign(refundId);
    }

    @ApiOperation("商户订单详情")
    @GetMapping("detail")
    public BaseResult<List<OrderExtInfoDto>> getOrderExtInfoDto(
            @RequestParam @ApiParam("订单编号") String orderNumber,
            @RequestParam @ApiParam("退款金额") Integer refundAmount
    ) {
        Integer orderId = commonService.getOrderIdByNumber(orderNumber);
        BaseResult<List<OrderExtInfoDto>> orderExtInfoDto = orderService.getOrderExtInfoDto(orderId, refundAmount);
        // 按照支付时间排序
        orderExtInfoDto.getData().sort(Comparator.comparing(OrderExtInfoDto::getGmtPay));
        return orderExtInfoDto;
    }
//    @GetMapping("generateOrderRefundPdf")
//    @ApiOperation("查看退款确认书pdf")
//    public ResponseEntity<Void> refundReadPdf(
//            @ApiIgnore HttpServletResponse response,
//            @RequestParam @ApiParam("退款单id") Integer refundId
//    ) {
//        orderService.generateOrderRefundReadPdf(response, refundId);
//        return ResponseEntity.ok().build();
//    }

    @ApiOperation("需人工处理的退款列表")
    @GetMapping("manual-list")
    public PageResult<List<RefundManualResp>> getRefundManualList(
            @RequestParam(required = false) @ApiParam("退款创建开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime createStartTime,
            @RequestParam(required = false) @ApiParam("退款创建结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime createEndTime,
            @RequestParam(required = false) @ApiParam("支付主体ID") Long payCompanyId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return orderService.getRefundManualList(createStartTime, createEndTime, payCompanyId, current, size);
    }

    @ApiOperation("需人工处理的退款列表导出")
    @PostMapping("manual-list/export")
    public BaseResult<RsFileExportRecord> batchExportRefundManual(
            @RequestParam @ApiParam("文件名") String fileName,
            @RequestBody ExportRefundManualReq req
    ) {
        RsFileExportRecord record = exportExcelService.genExportRecord(fileName);
        ExportFileInfo<Object> info = ExportFileInfo.builder()
                .recordId(record.getId())
                .fileEnum(ExportFileEnum.EXPORT_REFUND_MANUAL)
                .t(req)
                .build();
        BaseResult<String> sendRes = statisticsClient.exportExcel(info);
        if (sendRes.isFail()) {
            exportExcelService.markExportFile(info.getRecordId());
        }
        return BaseResult.success(record);
    }
}
