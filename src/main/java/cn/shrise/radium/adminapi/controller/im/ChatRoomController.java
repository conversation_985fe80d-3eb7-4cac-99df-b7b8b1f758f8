package cn.shrise.radium.adminapi.controller.im;

import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.resp.im.*;
import cn.shrise.radium.adminapi.service.im.ImChatRoomService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.constant.ImErrorCode;
import cn.shrise.radium.imservice.entity.ImChatStatistic;
import cn.shrise.radium.imservice.properties.ImPageMappingProperties;
import cn.shrise.radium.imservice.req.*;
import cn.shrise.radium.imservice.resp.ChatRoomNoticeResp;
import cn.shrise.radium.imservice.resp.ImChatRoomResp;
import cn.shrise.radium.tradeservice.entity.TdStockCaseChannel;
import cn.shrise.radium.userservice.resp.PageStatistics;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.checkerframework.checker.units.qual.A;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.time.Instant;
import java.util.List;

import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@Api
@RestController
@RequiredArgsConstructor
@RequestMapping("chat_room")
public class ChatRoomController {

    private final ImClient imClient;
    private final ImChatRoomService imChatRoomService;

    @ApiOperation("通过房间ID获取聊天室装饰")
    @GetMapping("{id}/decorate")
    public BaseResult<ImChatRoomDecorateResp> getChatRoomDecorate(@PathVariable Long id) {
        ImChatRoomDecorateResp chatRoom = imChatRoomService.getChatRoomDecorate(id);
        return BaseResult.success(chatRoom);
    }

    @PostMapping("update/decorate")
    @ApiOperation("更新聊天室-聊天室装饰")
    public BaseResult<Boolean> updateChatRoomDecorate(@RequestBody @Valid UpdateImChatRoomDecorateReq req) {
        return imClient.updateChatRoomDecorate(req);
    }

    @ApiOperation("通过房间ID获取房间信息")
    @GetMapping("{id}")
    public BaseResult<ChatRoomResp> getChatRoom(@PathVariable Long id) {
        ChatRoomResp chatRoom = imChatRoomService.getChatRoom(id);
        return BaseResult.success(chatRoom);
    }

    @PostMapping("update")
    @ApiOperation("编辑运营设置")
    public BaseResult<Void> updateChatRoom(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestBody @Valid UpdateImChatRoomOperationReq req
    ) {
        req.setOperatorId(userId);
        imChatRoomService.updateChatRoom(req);
        return BaseResult.successful();
    }

    @GetMapping("operateList")
    @ApiOperation("获取聊天室运营列表")
    public PageResult<List<ImChatRoomResp>> getChatRoomOperateList(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("聊天室类型") Integer type,
            @RequestParam(required = false) @ApiParam("启用/禁用") Boolean enabled,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size) {
        return imClient.getChatRoomOperateList(companyType, userId, type, enabled, current, size);
    }

    @GetMapping("list")
    @ApiOperation("获取聊天室管理列表")
    public PageResult<List<ImChatRoomResp>> getChatRoomPage(
            @RequestParam(required = false) @ApiParam("启用/禁用") Boolean enabled,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size
    ) {
        return imClient.getChatRoomPage(AuthContextHolder.getCompanyType(), enabled, current, size);
    }

    @PostMapping("update/info")
    @ApiOperation("编辑聊天室信息")
    public BaseResult<Void> updateChatRoom(@RequestBody @Valid UpdateImChatRoomInfoReq req) {
        imClient.updateChatRoom(req);
        return BaseResult.successful();
    }

    @PostMapping("create")
    @ApiOperation("新建聊天室")
    public BaseResult<ImErrorCode> createChatRoom(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestBody @Valid CreateChatRoomReq req
    ) {
        req.setCompanyType(companyType);
        return imClient.createChatRoom(req);
    }

    @PostMapping("shortLink")
    @ApiOperation("生成聊天室短链接")
    public BaseResult<String> createChatRoomShortLink(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestBody @Valid CreateChatRoomReq req) {
        req.setCompanyType(companyType);
        return imClient.createChatRoomShortLink(req);
    }

    @GetMapping("manager")
    @ApiOperation("聊天室处理人")
    public BaseResult<List<Integer>> getChatRoomManager(
            @RequestParam @ApiParam("聊天室ID") Long chatId
    ) {
        return imClient.getChatRoomManager(chatId);
    }

    @PostMapping("updateManage")
    @ApiOperation("配置聊天室处理人")
    public BaseResult<String> updateManage(
            @RequestParam Long id,
            @RequestParam @ApiParam("可见范围管理人json") String managerIds
    ) {
        return imClient.updateChatRoomManage(id, managerIds);
    }

    @GetMapping("manager/info")
    @ApiOperation("聊天室处理人老师信息")
    public BaseResult<List<ChatManagerResp>> getChatRoomManagerInfo(
            @RequestParam @ApiParam("聊天室Id") Long chatId,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enable) {
        List<ChatManagerResp> respList = imChatRoomService.getChatManagerAnalyst(chatId, enable);
        return BaseResult.success(respList);
    }

    @PostMapping("update/manager/analyst")
    @ApiOperation("修改处理人老师信息")
    public BaseResult<Void> updateManagerAnalyst(
            @RequestParam @ApiParam("记录Id") Long chatManagerId,
            @RequestParam(required = false) @ApiParam("老师id") Integer analystId) {
        return imChatRoomService.updateManagerAnalyst(chatManagerId, analystId);
    }

    @PostMapping("updateTemplate")
    @ApiOperation("聊天室模块配置")
    public BaseResult<String> updateTemplate(
            @RequestParam Long id,
            @RequestParam @ApiParam("是否有互动") Boolean isChat,
            @RequestParam @ApiParam("是否有问答(急诊室)") Boolean isQa,
            @RequestParam @ApiParam("是否有解盘") Boolean isStream) {
        return imClient.updateTemplate(id, isChat, isQa, isStream);
    }

    @GetMapping("notice")
    @ApiOperation("聊天室公告列表")
    public PageResult<List<ChatRoomNoticeResp>> getChatRoomNoticeResp(
            @RequestParam @ApiParam("聊天室ID") Long chatId,
            @RequestParam(required = false) @ApiParam("当前页") Integer current,
            @RequestParam(required = false) @ApiParam("每页条数") Integer size
    ) {
        return imClient.getChatRoomNoticeResp(chatId, current, size);
    }

    @PostMapping("notice/create")
    @ApiOperation("聊天室新建公告")
    public BaseResult<String> createNotice(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestBody @Valid CreateChatNoticeReq req
    ) {
        req.setCreatorId(userId);
        req.setCompanyType(companyType);
        return imClient.createNotice(req);
    }

    @PostMapping("notice/update")
    @ApiOperation("聊天室编辑公告")
    public BaseResult<String> updateNotice(
            @RequestBody @Valid UpdateImChatNoticeReq req
    ) {
        return imClient.updateNotice(req);
    }

    @GetMapping("statistics")
    @ApiOperation("聊天室数据统计")
    public BaseResult<ImChatStatistic> getChatStatistics(
            @RequestParam @ApiParam("房间id") Long chatId,
            @RequestParam @ApiParam("所属日期") Long flagDate) {
        Instant instant = Instant.ofEpochMilli(flagDate);
        return imClient.getChatStatistics(chatId, instant);
    }

    @GetMapping("page/mapping/{chatId}")
    @ApiOperation("获取聊天室对应页面信息")
    public BaseResult<ImPageMappingProperties.ChatPage> getChatPageInfo(@PathVariable Long chatId) {
        return imClient.getChatPageInfo(chatId);
    }

    @GetMapping("data/{chatId}")
    @ApiOperation("获取聊天室数据")
    public BaseResult<PageStatistics> getChatData(
            @PathVariable Long chatId,
            @RequestParam(required = false) @ApiParam("开始时间") Long startTime,
            @RequestParam(required = false) @ApiParam("结束时间") Long endTime) {
        return imChatRoomService.getPageStatistics(chatId, startTime, endTime);
    }

    @GetMapping("data/{chatId}/day")
    @ApiOperation("获取聊天室数据")
    public BaseResult<PageStatistics> getChatData(
            @PathVariable Long chatId,
            @RequestParam(required = false) @ApiParam("时间") Long flagTime) {
        return imChatRoomService.getPageStatistics(chatId, flagTime);
    }

    @GetMapping("sub/record/page")
    @ApiOperation("聊天室通知订阅记录")
    public PageResult<List<ImChatNoticeSubRecordResp>> getNoticeSubRecordPage(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam @ApiParam("聊天室id") Long chatId,
            @RequestParam @ApiParam("设备类型(10:公众号,20:APP)") Integer deviceType,
            @RequestParam(required = false) @ApiParam("开始时间") Long startTime,
            @RequestParam(required = false) @ApiParam("结束时间") Long endTime,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "20") @ApiParam("每页条数") Integer size) {
        return imChatRoomService.getNoticeSubRecordPage(chatId, deviceType, startTime, endTime, userId, companyType, current, size);
    }

    @GetMapping("stock/case")
    @ApiOperation("聊天室案例频道列表")
    public PageResult<List<TdStockCaseChannel>> getChatStockCaseChannelList(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "20") @ApiParam("每页条数") Integer size) {
        return imChatRoomService.getChatStockCaseChannelList(companyType, current, size);
    }

    @GetMapping("black/list")
    @ApiOperation("聊天室用户黑名单")
    public PageResult<List<ChatBlackInfoResp>> getChatBlackList(
            @RequestParam @ApiParam("聊天室id") Long chatId,
            @RequestParam(required = false) @ApiParam("用户code、微信id") String searchContent,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size) {
        return imChatRoomService.getChatBlackList(chatId, searchContent, current, size);
    }

    @GetMapping("black/search")
    @ApiOperation("查询用户聊天室黑名单信息")
    public BaseResult<List<ChatUserBlackInfoResp>> searchChatBlackInfo(
            @RequestParam @ApiParam("聊天室id") Long chatId,
            @RequestParam(required = false) @ApiParam("用户code、用户昵称、微信昵称") String searchContent) {
        List<ChatUserBlackInfoResp> list = imChatRoomService.searchChatBlackInfo(chatId, searchContent);
        return BaseResult.success(list);
    }

    @PostMapping("black/add")
    @ApiOperation("添加聊天室用户黑名单")
    public BaseResult<Void> addBlack(@RequestHeader(USER_ID) @ApiIgnore Integer userId,
                                     @RequestBody @ApiParam("用户黑名单") ChatBlackAddReq req) {
        req.setOperator(userId);
        return imClient.addChatBlack(req);
    }

    @PostMapping("black/remove")
    @ApiOperation("移除聊天室用户黑名单")
    public BaseResult<Void> removeBlack(@RequestHeader(USER_ID) @ApiIgnore Integer userId,
                                        @RequestParam @ApiParam("黑名单id") Long id) {
        return imClient.removeChatBlack(userId, id);
    }

    @GetMapping("black/record-list")
    @ApiOperation("聊天室用户黑名单操作记录")
    public BaseResult<List<ChatBlackModifyRecordResp>> getBlackOperateRecord(
            @RequestParam @ApiParam("聊天室id") Long chatId,
            @RequestParam @ApiParam("用户code") String userCode) {
        return imChatRoomService.getBlackOperateRecord(chatId, userCode);
    }
}
