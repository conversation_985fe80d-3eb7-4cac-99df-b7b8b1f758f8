package cn.shrise.radium.adminapi.controller.marketing;

import cn.shrise.radium.adminapi.resp.marketing.StreamRoomPromotionRecordResp;
import cn.shrise.radium.adminapi.resp.streamroom.StreamRoomRecordResp;
import cn.shrise.radium.adminapi.service.ExportExcelService;
import cn.shrise.radium.adminapi.service.marketing.StreamRoomService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.marketingservice.MarketingClient;
import cn.shrise.radium.marketingservice.req.streamroom.*;
import cn.shrise.radium.marketingservice.resp.streamroom.*;
import cn.shrise.radium.orderservice.entity.RsFileExportRecord;
import cn.shrise.radium.statisticsservice.StatisticsClient;
import cn.shrise.radium.statisticsservice.bean.ExportFileInfo;
import cn.shrise.radium.statisticsservice.constant.ExportFileEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

import javax.validation.Valid;

@Api
@Slf4j
@RestController
@RequestMapping("stream-room")
@RequiredArgsConstructor
public class StreamRoomController {

    private final MarketingClient marketingClient;
    private final StreamRoomService streamRoomService;
    private final ExportExcelService exportExcelService;
    private final StatisticsClient statisticsClient;

    @PostMapping("create")
    @ApiOperation("创建直播间")
    public BaseResult<Void> createStreamRoom(
            @RequestHeader(USER_ID) @ApiIgnore Integer operatorId,
            @RequestParam @ApiParam("直播间名称") String name,
            @RequestParam(required = false) @ApiParam("直播间介绍") String brief,
            @RequestParam(required = false) @ApiParam("设备ID") List<Long> deviceIds) {
        return marketingClient.createStreamRoom(operatorId, name, brief, deviceIds);
    }

    @PostMapping("update")
    @ApiOperation("修改直播间")
    public BaseResult<Void> updateStreamRoom(
            @RequestHeader(USER_ID) @ApiIgnore Integer operatorId,
            @RequestParam @ApiParam("直播间id") Long roomId,
            @RequestParam @ApiParam("直播间名称") String name,
            @RequestParam(required = false) @ApiParam("直播间介绍") String brief,
            @RequestParam(required = false) @ApiParam("设备ID") List<Long> deviceIds) {
        return marketingClient.updateStreamRoom(operatorId, roomId, name, brief, deviceIds);
    }

    @PostMapping("enable")
    @ApiOperation("启用/禁用直播间")
    public BaseResult<Void> enableStreamRoom(
            @RequestHeader(USER_ID) @ApiIgnore Integer operatorId,
            @RequestParam @ApiParam("直播间id") Long roomId,
            @RequestParam @ApiParam("启用/禁用") Boolean enabled) {
        return marketingClient.enableStreamRoom(operatorId, roomId, enabled);
    }

    @GetMapping("list")
    @ApiOperation("直播间列表")
    public PageResult<List<StreamRoomResp>> getStreamRoomList(
            @RequestParam(required = false) @ApiParam("状态") Boolean enabled,
            @RequestParam(required = false) @ApiParam("设备ID") Long deviceId,
            @RequestParam(required = false, defaultValue = "false") @ApiParam("是否拉取空闲直播间") Boolean isIdle,
            @RequestParam(required = false) @ApiParam("计划ID") Long planId,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) @ApiParam("当前分页") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) @ApiParam("每页数量") Integer size) {
        return marketingClient.getStreamRoomList(enabled, deviceId, isIdle, planId, startTime, endTime, current, size);
    }

    @PostMapping("device/create")
    @ApiOperation("创建直播间设备")
    public BaseResult<Void> createStreamRoomDevice(
            @RequestParam @ApiParam("设备名称") String name) {
        return marketingClient.createStreamRoomDevice(name);
    }

    @PostMapping("device/update")
    @ApiOperation("修改直播间设备")
    public BaseResult<Void> updateStreamRoomDevice(
            @RequestParam @ApiParam("设备ID") Long deviceId,
            @RequestParam @ApiParam("设备名称") String name) {
        return marketingClient.updateStreamRoomDevice(deviceId, name);
    }

    @PostMapping("device/enable")
    @ApiOperation("启用/禁用直播间设备")
    public BaseResult<Void> enableStreamRoomDevice(
            @RequestParam @ApiParam("设备ID") Long deviceId,
            @RequestParam @ApiParam("启用/禁用") Boolean enabled) {
        return marketingClient.enableStreamRoomDevice(deviceId, enabled);
    }

    @GetMapping("device/list")
    @ApiOperation("直播间设备列表")
    public PageResult<List<StreamRoomDeviceResp>> getStreamRoomDeviceList(
            @RequestParam(required = false) @ApiParam("启用/禁用") Boolean enabled,
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) @ApiParam("当前分页") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) @ApiParam("每页数量") Integer size) {
        return marketingClient.getStreamRoomDeviceList(enabled, current, size);
    }

    @GetMapping("info")
    @ApiOperation("直播间信息")
    public BaseResult<StreamRoomResp> getStreamRoomInfo(
            @RequestParam @ApiParam("直播间id") Long roomId) {
        return marketingClient.getStreamRoomInfo(roomId);
    }

    @GetMapping("record/list")
    @ApiOperation("直播间操作列表")
    public PageResult<List<StreamRoomRecordResp>> getStreamRoomRecordList(
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) @ApiParam("当前分页") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) @ApiParam("每页数量") Integer size) {
        return streamRoomService.getStreamRoomRecordList(current, size);
    }

    @GetMapping("config/list")
    @ApiOperation("直播室信息配置列表")
    public PageResult<List<StreamRoomConfigPropertyResp>> getStreamRoomConfigList(
            @RequestParam(required = false) @ApiParam("属性编号") List<String> propertyNumbers,
            @RequestParam(required = false) @ApiParam("状态") Boolean enabled,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return streamRoomService.getStreamRoomConfigList(propertyNumbers, enabled, current, size);
    }

    @PostMapping("config/create")
    @ApiOperation("创建直播室信息配置")
    public BaseResult<Void> createStreamRoomConfig(
            @RequestBody @Valid CreateStreamRoomConfigPropertyReq req,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId
    ) {
        req.setOperatorId(userId);
        return streamRoomService.createStreamRoomConfig(req);
    }

    @PostMapping("config/update")
    @ApiOperation("修改直播室信息配置")
    public BaseResult<Void> updateStreamRoomConfig(
            @RequestBody @Valid UpdateStreamRoomConfigPropertyReq req,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId
    ) {
        req.setOperatorId(userId);
        return streamRoomService.updateStreamRoomConfig(req);
    }

    @GetMapping("config/record/list")
    @ApiOperation("直播室信息配置操作记录")
    public PageResult<List<StreamRoomConfigRecordResp>> getStreamRoomConfigRecord(
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return streamRoomService.getStreamRoomConfigRecord(current, size);
    }

    @PostMapping("plan/list")
    @ApiOperation("直播排班计划")
    public BaseResult<List<StreamRoomPlanPromotionResp>> getStreamRoomPlanList(
            @RequestBody @Valid StreamRoomPlanReq req
    ) {
        return streamRoomService.getStreamRoomPlanList(req);
    }

    @GetMapping("plan/detail")
    @ApiOperation("直播排班计划详情")
    public BaseResult<StreamRoomPlanPromotionResp> getStreamRoomPlanDetail(
            @RequestParam @ApiParam("直播计划ID") Long planId
    ) {
        return streamRoomService.getStreamRoomPlanDetail(planId);
    }

    @PostMapping("plan/create")
    @ApiOperation("创建直播排班计划")
    public BaseResult<Long> createStreamRoomPlan(
            @RequestBody @Valid CreateStreamRoomPlanReq req,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId
    ) {
        req.setOperatorId(userId);
        return streamRoomService.createStreamRoomPlan(req);
    }

    @PostMapping("plan/update")
    @ApiOperation("修改直播排班计划")
    public BaseResult<Void> updateStreamRoomPlan(
            @RequestBody @Valid UpdateStreamRoomPlanReq req,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId
    ) {
        req.setOperatorId(userId);
        return streamRoomService.updateStreamRoomPlan(req);
    }

    @PostMapping("plan/update-remark")
    @ApiOperation("修改直播计划备注")
    public BaseResult<Void> updateStreamRoomPlanRemark(
            @RequestBody @Valid UpdateStreamRoomPlanRemarkReq req,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId
    ) {
        req.setOperatorId(userId);
        return streamRoomService.updateStreamRoomPlanRemark(req);
    }

    @PostMapping("promotion/update-remark")
    @ApiOperation("修改直播投放计划备注")
    public BaseResult<Void> updatePromotionRemark(
            @RequestBody @Valid UpdatePromotionRemark req,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId
    ) {
        req.setOperatorId(userId);
        return streamRoomService.updatePromotionRemark(req);
    }

    @PostMapping("plan/cancel")
    @ApiOperation("取消直播排班计划")
    public BaseResult<Void> cancelStreamRoomPlan(
            @RequestBody @Valid CancelStreamRoomPlanReq req,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId
    ) {
        req.setOperatorId(userId);
        return streamRoomService.cancelStreamRoomPlan(req);
    }

    @GetMapping("plan/record/list")
    @ApiOperation("直播排班计划操作记录")
    public PageResult<List<StreamRoomPlanRecordResp>> getStreamRoomPlanRecord(
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return streamRoomService.getStreamRoomPlanRecord(current, size);
    }

    @PostMapping("promotion/create")
    @ApiOperation("创建投放计划")
    public BaseResult<Void> createStreamRoomPromotion(
            @RequestBody @Valid CreateStreamRoomPromotionReq req,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId
    ) {
        req.setOperatorId(userId);
        return streamRoomService.createStreamRoomPromotion(req);
    }

    @PostMapping("promotion/update")
    @ApiOperation("修改投放计划")
    public BaseResult<Void> updateStreamRoomPromotion(
            @RequestBody @Valid UpdateStreamRoomPromotionReq req,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId
    ) {
        req.setOperatorId(userId);
        return streamRoomService.updateStreamRoomPromotion(req);
    }

    @PostMapping("promotion/enabled")
    @ApiOperation("删除投放计划")
    public BaseResult<Void> enabledStreamRoomPromotion(
            @RequestBody @Valid EnableStreamRoomPromotionReq req,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId
    ) {
        req.setOperatorId(userId);
        return streamRoomService.enabledStreamRoomPromotion(req);
    }

    @GetMapping("plan/statistics")
    @ApiOperation("直播数据统计")
    public BaseResult<StreamRoomPlanStatisticsResp> getStreamRoomPlanStatistics(
            @RequestParam @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime
    ) {
        return streamRoomService.getStreamRoomPlanStatistics(startTime);
    }

    @GetMapping("plan/anchor-list")
    @ApiOperation("主播列表")
    public PageResult<List<StreamRoomPlanAnchorResp>> getStreamRoomPlanAnchorList(
            @RequestParam @ApiParam("日期") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate date,
            @RequestParam(required = false) @ApiParam("是否上播") Boolean isPlay,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return marketingClient.getStreamRoomPlanAnchorList(date, isPlay, current, size);
    }

    @PostMapping("plan/page-list")
    @ApiOperation("直播排班计划")
    public PageResult<List<StreamRoomPlanResp>> getStreamRoomPlanPageList(
            @RequestBody @Valid StreamRoomPlanReq req,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size
    ) {
        return marketingClient.getStreamRoomPlanPageList(req, current, size);
    }

    @PostMapping("plan/export")
    @ApiOperation("导出直播计划excel")
    public BaseResult<RsFileExportRecord> exportStreamRoomPlan(
            @RequestParam @ApiParam("文件名") String fileName,
            @RequestBody @Valid StreamRoomPlanReq req
    ) {
        RsFileExportRecord record = exportExcelService.genExportRecord(fileName);
        ExportFileInfo<Object> info = ExportFileInfo.builder()
                .recordId(record.getId())
                .fileEnum(ExportFileEnum.EXPORT_STREAM_ROOM_PLAN)
                .t(req)
                .build();
        BaseResult<String> sendRes = statisticsClient.exportExcel(info);
        if (sendRes.isFail()) {
            exportExcelService.markExportFile(info.getRecordId());
        }
        return BaseResult.success(record);
    }

    @PostMapping("promotion/list")
    @ApiOperation("直播投放计划列表")
    public PageResult<List<StreamRoomPromotionPlanResp>> getStreamRoomPromotionList(
            @RequestBody @Valid StreamRoomPromotionReq req,
            @RequestParam(required = false) @ApiParam("页码") Integer current,
            @RequestParam(required = false) @ApiParam("分页数量") Integer size
    ) {
        return marketingClient.getStreamRoomPromotionPageList(req, current, size);
    }

    @PostMapping("promotion/export")
    @ApiOperation("导出直播投放计划excel")
    public BaseResult<RsFileExportRecord> exportStreamRoomPromotion(
            @RequestParam @ApiParam("文件名") String fileName,
            @RequestBody @Valid StreamRoomPromotionReq req
    ) {
        RsFileExportRecord record = exportExcelService.genExportRecord(fileName);
        ExportFileInfo<Object> info = ExportFileInfo.builder()
                .recordId(record.getId())
                .fileEnum(ExportFileEnum.EXPORT_STREAM_ROOM_PROMOTION)
                .t(req)
                .build();
        BaseResult<String> sendRes = statisticsClient.exportExcel(info);
        if (sendRes.isFail()) {
            exportExcelService.markExportFile(info.getRecordId());
        }
        return BaseResult.success(record);
    }

    @PostMapping("promotion/set-manager")
    @ApiOperation("直播投放计划设置投手")
    public BaseResult<Void> addStreamRoomPromotionManager(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("投放计划id") Long promotionId,
            @RequestParam(required = false) @ApiParam("投手id") Integer managerId
    ) {
        return marketingClient.addStreamRoomPromotionManager(userId, promotionId, managerId);
    }

    @GetMapping("promotion/record/list")
    @ApiOperation("直播投放计划操作记录列表")
    public PageResult<List<StreamRoomPromotionRecordResp>> getStreamRoomPromotionRecordList(
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return streamRoomService.getStreamRoomPromotionRecordList(current, size);
    }
}
