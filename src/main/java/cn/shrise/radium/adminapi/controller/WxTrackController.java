package cn.shrise.radium.adminapi.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.resp.WxContactTrackInfo;
import cn.shrise.radium.adminapi.resp.WxTrackInfo;
import cn.shrise.radium.adminapi.resp.wx.WxTrackEventResp;
import cn.shrise.radium.adminapi.service.WxTrackService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.wxservice.entity.UcWxTrackEvent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDate;
import java.util.List;

import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;

@Api
@RestController
@RequestMapping("wx")
@RequiredArgsConstructor
public class WxTrackController {

    private final WxTrackService wxTrackService;

//    @GetMapping("track")
//    @ApiOperation("获取外部联系人追踪信息")
//    public BaseResult<List<WxContactTrackInfo>> getWxContactTrackInfo(
//            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
//            @RequestParam @ApiParam("产品类型") String category,
//            @RequestParam(required = false) @ApiParam("销售Id") List<Integer> salesIdList,
//            @RequestParam(required = false) @ApiParam("企业微信账号id列表") List<Long> workWxUserIdList,
//            @RequestParam(required = false) @ApiParam("企业微信账号") String wxAccount,
//            @RequestParam(required = false, defaultValue = "100") @ApiParam("数量") Integer size) {
//        return wxTrackService.getWxContactTrackInfo(companyType, category, salesIdList, workWxUserIdList, wxAccount, size);
//    }

    @GetMapping("track/{wxId}")
    @ApiOperation("获取访问明细")
    public PageResult<List<WxTrackEventResp>> getWxAccessDetailList(
            @ApiParam("wxId") @PathVariable Integer wxId,
            @RequestParam(required = false) @ApiParam("类型(为userId时传2)") Integer type,
            @RequestParam(required = false) @ApiParam("是否区分销售") Boolean isSale,
            @RequestParam(required = false) @ApiParam("销售id") Integer salesId,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("产品类型") String category,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        Integer companyType = AuthContextHolder.getCompanyType();
        if (ObjectUtil.isNotEmpty(isSale) && isSale && ObjectUtil.isEmpty(salesId)) {
            salesId = AuthContextHolder.getUserId();
        }
        return wxTrackService.getWxAccessDetailList(wxId, isSale, salesId, companyType, type, startTime, endTime, category, current, size);
    }

    @GetMapping("track/hiddenUser")
    @ApiOperation("获取意向联系人追踪信息")
    public PageResult<List<WxTrackInfo>> getWxContactTrackInfo(
            @RequestParam @ApiParam("产品类型") String category,
            @RequestParam @ApiParam("销售id列表") List<Integer> salesIdList,
            @RequestParam(required = false) @ApiParam("是否过滤已成交") Boolean filterUser,
            @RequestParam(required = false) @ApiParam("访问开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("访问结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return wxTrackService.getWxHiddenTrackInfo(category, salesIdList, filterUser, startTime, endTime, current, size);
    }

}
