package cn.shrise.radium.adminapi.controller.user;

import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.shrise.radium.adminapi.entity.CustomerEvaluation;
import cn.shrise.radium.adminapi.entity.User;
import cn.shrise.radium.adminapi.entity.WxExtInfo;
import cn.shrise.radium.adminapi.resp.SearchCustomerAggregateItem;
import cn.shrise.radium.adminapi.resp.SearchCustomerItem;
import cn.shrise.radium.adminapi.resp.order.OrderSkuInfoResp;
import cn.shrise.radium.adminapi.resp.user.*;
import cn.shrise.radium.adminapi.service.OrderService;
import cn.shrise.radium.adminapi.service.UserService;
import cn.shrise.radium.adminapi.service.user.CustomerService;
import cn.shrise.radium.adminapi.util.IpUtils;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.orderservice.constant.OrderStatusEnum;
import cn.shrise.radium.secureservice.SecureServiceClient;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.constant.IdentityTypeEnum;
import cn.shrise.radium.userservice.constant.UserTypeConstant;
import cn.shrise.radium.userservice.entity.UcEvaluationInfo;
import cn.shrise.radium.userservice.entity.UcIdCardRecord;
import cn.shrise.radium.userservice.entity.UcVerifyInfo;
import cn.shrise.radium.userservice.entity.UcWxExt;
import cn.shrise.radium.userservice.error.UsErrorCode;
import cn.shrise.radium.userservice.req.SetCustomerWhitelistReq;
import cn.shrise.radium.userservice.resp.CustomerChangeRecordResp;
import cn.shrise.radium.userservice.resp.CustomerInterestTagResp;
import cn.shrise.radium.userservice.resp.SeverResp;
import cn.shrise.radium.userservice.resp.WxChangeRecordResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

/**
 * <AUTHOR>
 */
@Api
@Slf4j
@RestController
@RequestMapping("customer")
@RequiredArgsConstructor
public class CustomerController {

    private final CustomerService customerService;
    private final UserService userService;
    private final UserClient userClient;
    private final SecureServiceClient secureServiceClient;
    private final OrderService orderService;

    @GetMapping("list")
    @ApiOperation("获取客户管理列表")
    public PageResult<List<NewCustomerInfoResp>> getCustomerInfoList(
            @RequestParam @ApiParam("用户code,微信id,手机号") String searchText,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size,
            @RequestParam(required = false) @ApiParam("菜单") String menuName,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @ApiIgnore HttpServletRequest request) {
        if (ObjectUtil.isNotEmpty(menuName) && PhoneUtil.isMobile(searchText)) {
            secureServiceClient.mobileMatchRecord(searchText, UserTypeConstant.STAFF, userId, menuName, null, IpUtils.getClientIp(request));
        }
        PageResult<List<NewCustomerInfoResp>> pageResult = customerService.getCustomerInfoList(searchText, current, size);
        if (pageResult.isSuccess() && ObjectUtil.isEmpty(pageResult.getData())) {
            return customerService.getCustomerFakeInfoList(searchText, current, size);
        } else {
            return pageResult;
        }
    }

    @GetMapping("record")
    @ApiOperation("获取认证记录")
    public BaseResult<List<UcIdCardRecord>> getRecord(
            @RequestParam @ApiParam("手机号") String mobile) {
        return customerService.getRecord(mobile);
    }

    @PostMapping("unbindWx")
    @ApiOperation("用户解绑微信")
    public BaseResult<UsErrorCode> unbindWx(
            @ApiIgnore @RequestHeader(USER_ID) Integer operatorId,
            @RequestParam @ApiParam("微信id") Integer wxId) {
        return customerService.unbindWx(wxId, operatorId);
    }

    @GetMapping("interest/tag/user-list")
    @ApiOperation("通过userId获取用户兴趣标签")
    public BaseResult<List<CustomerInterestTagResp>> findCustomerInterestTag(@RequestParam String userCode) {
        return customerService.findCustomerInterestTag(userCode);
    }

    @GetMapping("search/aggregate/relation")
    @ApiOperation("OpenSearch搜索归属用户信息")
    public PageResult<List<SearchCustomerAggregateItem>> searchCustomerAggregateRelation(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(required = false) String appName,
            @RequestParam String keyword,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size
    ) {
        return customerService.searchCustomerAggregate(companyType, appName, keyword, current, size, false);
    }

    @GetMapping("search/aggregate")
    @ApiOperation("OpenSearch全局搜索用户信息")
    public PageResult<List<SearchCustomerAggregateItem>> searchCustomerAggregate(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(required = false) String appName,
            @RequestParam String keyword,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size
    ) {
        return customerService.searchCustomerAggregate(companyType, appName, keyword, current, size, true);
    }

    @GetMapping("whitelist/operate_record/list")
    @ApiOperation("获取白名单操作记录")
    public PageResult<List<CustomerWhitelistOperateRecordResp>> getCustomerWhitelistOperateRecordList(
            @RequestParam @ApiParam("用户code") String userCode,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("数量") Integer size) {
        return customerService.getCustomerWhitelistOperateRecordList(userCode, current, size);
    }

    @PostMapping("set/whitelist")
    @ApiOperation("设置白名单")
    public BaseResult<Void> setIsWhiteList(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestBody @Valid SetCustomerWhitelistReq req) {
        req.setOperatorId(userId);
        return customerService.setIsWhiteList(req);
    }

    @GetMapping("ext/info")
    @ApiOperation("客户扩展信息")
    public BaseResult<CustomerExtResp> getCustomerExtInfo(
            @RequestParam @ApiParam("用户code") String userCode) {
        CustomerExtResp customerExt = customerService.getCustomerExt(userCode);
        return BaseResult.success(customerExt);
    }

    @GetMapping("search/server")
    @ApiOperation("搜索后台用户")
    public PageResult<List<SeverResp>> searchServer(
            @RequestParam @ApiParam("工号或者姓名") String searchContent,
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("数量") Integer size) {
        return customerService.searchServer(searchContent, companyType, current, size);
    }

    @GetMapping("global-search")
    @ApiOperation("全局搜索用户信息")
    public PageResult<List<SearchCustomerItem>> globalSearch(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("搜索类型") Integer searchType,
            @RequestParam @ApiParam("搜索内容") String content,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size,
            @RequestParam(required = false) @ApiParam("菜单") String menuName,
            @ApiIgnore HttpServletRequest request) {
        if (ObjectUtil.isNotEmpty(menuName) && PhoneUtil.isMobile(content)) {
            secureServiceClient.mobileMatchRecord(content, UserTypeConstant.STAFF, userId, menuName, null, IpUtils.getClientIp(request));
        }
        PageResult<List<SearchCustomerItem>> pageResult = customerService.globalSearch(companyType, searchType, content, current, size);
        if (pageResult.isSuccess() && ObjectUtil.isEmpty(pageResult.getData())) {
            return customerService.globalSearchFake(searchType, content, current, size);
        } else {
            return pageResult;
        }
    }

    @GetMapping("global-search-new")
    @ApiOperation("全局搜索用户信息")
    public PageResult<List<SearchCustomerItem>> globalSearchNew(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("搜索类型") Integer searchType,
            @RequestParam @ApiParam("搜索内容") String content,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size,
            @RequestParam(required = false) @ApiParam("菜单") String menuName,
            @ApiIgnore HttpServletRequest request) {
        return customerService.globalSearchFake(searchType, content, current, size);
    }

    @GetMapping("mobile-change-record")
    @ApiOperation("获取用户手机号变更记录")
    public PageResult<List<CustomerChangeRecordResp>> getCustomerChangeRecordList(
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("分页数量") Integer size) {
        return customerService.getCustomerChangeRecordList(searchContent, current, size);
    }

    @GetMapping("wx-change-record")
    @ApiOperation("获取微信换绑记录")
    public PageResult<List<WxChangeRecordResp>> getWxChangeRecordList(
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("分页数量") Integer size) {
        return customerService.getWxChangeRecordList(searchContent, current, size);
    }


    @GetMapping("global-search-relation")
    @ApiOperation("全局搜索归属用户信息")
    public PageResult<List<SearchCustomerItem>> globalSearchRelation(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("搜索类型") Integer searchType,
            @RequestParam @ApiParam("搜索内容") String content,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size) {
        return customerService.globalSearchRelation(companyType, userId, searchType, content, current, size);
    }

    @ApiOperation("获取用户信息")
    @GetMapping("detail")
    public BaseResult<User> getUser(@RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
                                    @RequestParam @ApiParam("用户code") String userCode,
                                    @RequestParam(required = false, defaultValue = "false") @ApiParam("微信信息") Boolean needWxInfo,
                                    @RequestParam(required = false, defaultValue = "false") @ApiParam("评测信息") Boolean needEvaluationInfo) {
        int userId = DesensitizeUtil.maskToId(userCode);
        UserDetail ucUser = userService.searchUser(userId, companyType, UserTypeConstant.CUSTOMER);
        if (ObjectUtil.isEmpty(ucUser) || !ObjectUtil.equal(ucUser.getUserType(), UserTypeConstant.CUSTOMER)) {
            return BaseResult.success(new User());
        }
        User user = new User();
        BeanUtils.copyProperties(ucUser, user);
        if (needWxInfo) {
            UcWxExt wxInfo = userClient.getWxExtInfo(userId).getData();
            if (wxInfo != null) {
                WxExtInfo wxExtInfo = new WxExtInfo();
                BeanUtils.copyProperties(wxInfo, wxExtInfo);
                wxExtInfo.setUserCode(DesensitizeUtil.idToMask(wxInfo.getUserId()));
                user.setWxInfo(wxExtInfo);
            }
        }

        if (needEvaluationInfo) {
            //评测信息
            UcEvaluationInfo evalInfo = userClient.getEvaluationInfo(userId).orElse(null);
            //认证信息
            UcVerifyInfo verifyInfo = userClient.getVerifyInfo(userId).orElse(null);
            if (ObjectUtil.isAllEmpty(evalInfo, verifyInfo)) {
                return BaseResult.success(user);
            }
            CustomerEvaluation customerEvaluation = new CustomerEvaluation();
            if (evalInfo != null) {
                BeanUtils.copyProperties(evalInfo, customerEvaluation);
                customerEvaluation.setUserCode(DesensitizeUtil.idToMask(evalInfo.getUserId()));
                if (evalInfo.getSurveyScore() != null) {
                    customerEvaluation.setRiskLevel(evalInfo.getLevel());
                }
            }
            if (verifyInfo != null) {
                if (Objects.equals(verifyInfo.getIdentityType(), IdentityTypeEnum.ID_CARD.getCode())) {
                    String idCardNumber = AESUtil.decrypt(verifyInfo.getIdentityNumber());
                    int idCardAge = IdcardUtil.getAgeByIdCard(idCardNumber);
                    customerEvaluation.setAge(idCardAge);
                }
            }
            user.setEvaluationInfo(customerEvaluation);
        }
        return BaseResult.success(user);
    }

    @ApiOperation("获取简单用户信息")
    @GetMapping("simple-detail")
    public BaseResult<SimpleUserDetailResp> getSimpleUserDetail(@RequestParam @ApiParam("userCode") String userCode) {
        int userId = DesensitizeUtil.maskToId(userCode);
        SimpleUserDetailResp simpleUserDetail = customerService.getSimpleUserDetail(userId);
        List<OrderSkuInfoResp> orderSkuInfoResps = orderService.getMyOrderList(45, userId, OrderStatusEnum.PASSED.getValue()).getData();
        simpleUserDetail.setFinishedOrderCount(orderSkuInfoResps.size());
        return BaseResult.success(simpleUserDetail);
    }


}
