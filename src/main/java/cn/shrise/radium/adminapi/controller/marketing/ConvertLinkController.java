package cn.shrise.radium.adminapi.controller.marketing;

import cn.shrise.radium.adminapi.constant.ErrorConstant;
import cn.shrise.radium.adminapi.req.ConvertLinkUploadRecordReq;
import cn.shrise.radium.adminapi.resp.ConvertLinkContactWayRelationResp;
import cn.shrise.radium.adminapi.resp.NpConvertLinkRecordResp;
import cn.shrise.radium.adminapi.resp.NpConvertLinkUploadRecordResp;
import cn.shrise.radium.adminapi.service.marketing.ConvertLinkService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.marketingservice.entity.NpConvertLinkConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api
@Slf4j
@RestController
@RequestMapping("marketing/convert_link")
@RequiredArgsConstructor
public class ConvertLinkController {

    private final ConvertLinkService convertLinkService;

    @GetMapping("record_list")
    @ApiOperation("查询链路修改记录")
    public PageResult<List<NpConvertLinkRecordResp>> getConvertLinkRecordList(
            @RequestParam @ApiParam("链路id") Long linkId,
            @RequestParam(defaultValue = "true") @ApiParam("排序") Boolean asc,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        PageResult<List<NpConvertLinkRecordResp>> result = convertLinkService.getConvertLinkRecordList(linkId, asc,
                current, size);
        return PageResult.success(result.getData(), result.getPagination());
    }


    @PostMapping("/config")
    @ApiOperation("新增转化链路")
    public BaseResult<NpConvertLinkConfig> addConvertLinkConfig(
            @RequestParam @ApiParam("链路类型") Integer linkType,
            @RequestParam @ApiParam("链路名称") String linkName,
            @RequestParam @ApiParam("链路名称") String number) {

        return convertLinkService.saveOne(linkType, linkName, number);
    }

    @GetMapping("/config")
    @ApiOperation("获取转化链路列表")
    public PageResult<List<NpConvertLinkConfig>> getConvertLinkConfigList(
            @RequestParam(required = false) @ApiParam("链路类型") Integer linkType,
            @RequestParam(required = false) @ApiParam("链路状态") Boolean isEnabled,
            @RequestParam(required = false) @ApiParam("搜索") String searchContent,
            @RequestParam(required = false) @ApiParam("当前分页") Integer current,
            @RequestParam(required = false) @ApiParam("每页数量") Integer size) {
        return convertLinkService.getConvertLinkConfigList(linkType, isEnabled, searchContent, current, size);
    }

    @GetMapping("/contact_ways/{linkId}")
    @ApiOperation("获取链路关联渠道列表")
    public PageResult<List<ConvertLinkContactWayRelationResp>> getConvertLinkRelateContactWays(
            @PathVariable @ApiParam("链路id") Long linkId,
            @RequestParam(required = false) @ApiParam("当前分页") Integer current,
            @RequestParam(required = false) @ApiParam("每页数量") Integer size) {
        return convertLinkService.getRelationContactWays(linkId, current, size);
    }

    @PutMapping("/config/{linkId}")
    @ApiOperation("启用/禁用链路")
    public BaseResult<ErrorConstant> updateConvertLinkStatus(
            @PathVariable @ApiParam("链路ID") Long linkId,
            @RequestParam @ApiParam("是否启用") Boolean isEnabled) {

        return BaseResult.of(convertLinkService.updateConvertLinkStatus(linkId, isEnabled));
    }

    @PutMapping("/config/update/{linkId}")
    @ApiOperation("修改链路名称")
    public BaseResult<ErrorConstant> updateConvertLinkName(
            @PathVariable @ApiParam("链路ID") Long linkId,
            @RequestParam @ApiParam("链路名称") String linkName) {

        return BaseResult.of(convertLinkService.updateConvertLinkName(linkId, linkName));
    }


    @PostMapping("/upload_record")
    @ApiOperation("获取推广转化上报详情列表")
    public PageResult<List<NpConvertLinkUploadRecordResp>> getConvertLinkUploadRecord(
            @RequestBody @Validated ConvertLinkUploadRecordReq uploadRecordReq) {
        return convertLinkService.getConvertLinkUploadRecordList(uploadRecordReq);
    }

}
