package cn.shrise.radium.adminapi.controller.marketing;

import cn.shrise.radium.marketingservice.req.media.CreateMediaAccountReq;
import cn.shrise.radium.marketingservice.req.media.EnableMediaAccountReq;
import cn.shrise.radium.marketingservice.req.media.MediaAccountReq;
import cn.shrise.radium.marketingservice.req.media.UpdateMediaAccountReq;
import cn.shrise.radium.marketingservice.resp.media.MediaAccountRecordResp;
import cn.shrise.radium.marketingservice.resp.media.MediaAccountResp;
import cn.shrise.radium.adminapi.service.marketing.MediaAccountService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;

import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@RestController
@RequestMapping("media-account")
@RequiredArgsConstructor
public class MediaAccountController {

    private final MediaAccountService mediaAccountService;

    @PostMapping("list")
    @ApiOperation("媒体账号列表")
    public PageResult<List<MediaAccountResp>> getMediaAccountList(
            @RequestBody @Valid MediaAccountReq req,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return mediaAccountService.getMediaAccountList(req, current, size);
    }

    @PostMapping("create")
    @ApiOperation("创建媒体账号")
    public BaseResult<Void> createMediaAccount(
            @RequestBody @Valid CreateMediaAccountReq req,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId
    ) {
        req.setOperatorId(userId);
        return mediaAccountService.createMediaAccount(req);
    }

    @PostMapping("update")
    @ApiOperation("修改媒体账号")
    public BaseResult<Void> updateMediaAccount(
            @RequestBody @Valid UpdateMediaAccountReq req,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId
    ) {
        req.setOperatorId(userId);
        return mediaAccountService.updateMediaAccount(req);
    }

    @PostMapping("enable")
    @ApiOperation("启用/禁用媒体账号")
    public BaseResult<Void> enableMediaAccount(
            @RequestBody @Valid EnableMediaAccountReq req,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId
    ) {
        req.setOperatorId(userId);
        return mediaAccountService.enableMediaAccount(req);
    }

    @GetMapping("record/list")
    @ApiOperation("媒体账号操作记录")
    public PageResult<List<MediaAccountRecordResp>> getMediaAccountOperateRecord(
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return mediaAccountService.getMediaAccountOperateRecord(current, size);
    }
}
