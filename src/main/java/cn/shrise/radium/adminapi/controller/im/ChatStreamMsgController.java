package cn.shrise.radium.adminapi.controller.im;

import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.imservice.ImClient;
import cn.shrise.radium.imservice.req.CreateChatStreamMsgReq;
import cn.shrise.radium.imservice.resp.ChatStreamMessageResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;


/**
 * @auther 张涛淼
 */
@Api
@RestController
@RequiredArgsConstructor
@RequestMapping("chat_stream_msg")
public class ChatStreamMsgController {

    private final ImClient imClient;

    @PostMapping("/create")
    @ApiOperation("创建")
    public BaseResult<String> createStreamMsg(@RequestBody @Valid CreateChatStreamMsgReq req) {
        req.setCompanyType(AuthContextHolder.getCompanyType());
        req.setCreatorId(AuthContextHolder.getUserId());
        return imClient.createStreamMsg(req);
    }

    @GetMapping("/delete")
    @ApiOperation("删除")
    public BaseResult<String> deleteOne(@RequestParam @ApiParam("id") Long id) {
        return imClient.deleteOne(id);
    }

    @GetMapping("/list")
    @ApiOperation("解盘列表")
    public PageResult<List<ChatStreamMessageResp>> getStreamMessagePage(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam @ApiParam("聊天室id") Long chatId,
            @RequestParam(required = false) @ApiParam("消息状态") Boolean enabled,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return imClient.getStreamMessagePage(companyType, chatId, null, enabled, startTime, endTime, current, size);
    }
}
