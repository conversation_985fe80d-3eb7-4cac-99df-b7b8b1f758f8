package cn.shrise.radium.adminapi.controller.douyin;

import cn.shrise.radium.adminapi.entity.DouyinConvertTool;
import cn.shrise.radium.adminapi.req.CreateDyConvertToolReq;
import cn.shrise.radium.adminapi.req.UpdateDyAccountReq;
import cn.shrise.radium.adminapi.req.UpdateDyConvertToolReq;
import cn.shrise.radium.adminapi.resp.GetDyAccountResp;
import cn.shrise.radium.adminapi.service.douyin.DyAccountService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api
@RestController
@RequestMapping("dy/account")
@RequiredArgsConstructor
public class DyAccountController {

    private final DyAccountService accountService;

    @GetMapping
    @ApiOperation("获取授权账户列表")
    public PageResult<List<GetDyAccountResp>> getAccountList(
            @RequestParam @ApiParam("应用类型") Integer clientType,
            @RequestParam(required = false) @ApiParam("启用状态") Boolean enabled,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return accountService.getAccountList(clientType, enabled, current, size);
    }

    @PatchMapping("{id}")
    @ApiOperation("更新账户")
    public BaseResult<String> updateAccount(@PathVariable Long id, @RequestBody UpdateDyAccountReq req) {
        return accountService.updateAccount(id, req);
    }

    @PatchMapping("convert_tool/{id}")
    @ApiOperation("更新转化工具")
    public BaseResult<String> updateConvertTool(
            @PathVariable Long id,
            @RequestBody UpdateDyConvertToolReq req) {
        return accountService.updateConvertTool(id, req);
    }

    @PostMapping("{accountId}/convert_tool")
    @ApiOperation("创建转化工具")
    public BaseResult<DouyinConvertTool> createConvertTool(
            @PathVariable Long accountId,
            @RequestBody CreateDyConvertToolReq req) {
        return accountService.createConvertTool(accountId, req);
    }
}
