package cn.shrise.radium.adminapi.controller.content;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.resp.content.OriginMaterialOperateRecordResp;
import cn.shrise.radium.adminapi.resp.content.OriginMaterialResp;
import cn.shrise.radium.adminapi.service.content.OriginMaterialService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.entity.SsOriginMaterial;
import cn.shrise.radium.contentservice.req.CreateOriginMaterialReq;
import cn.shrise.radium.contentservice.req.OriginMaterialAuditReq;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@RestController
@RequestMapping("origin-material")
@RequiredArgsConstructor
public class OriginMaterialController {

    private final ContentClient contentClient;
    private final OriginMaterialService originMaterialsService;

    @PostMapping("create")
    @ApiOperation("上传好评素材")
    public BaseResult<Void> createOriginMaterial(
            @RequestHeader(value = COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(value = USER_ID) @ApiIgnore Integer userId,
            @RequestBody @Valid CreateOriginMaterialReq req) {
        req.setCompanyType(companyType);
        req.setCreatorId(userId);
        originMaterialsService.checkOriginMaterialChannel(userId, req.getChannelId());
        contentClient.createOriginMaterial(req);
        return BaseResult.successful();
    }

    @GetMapping("creator/list")
    @ApiOperation("获取创建人好评素材列表")
    public PageResult<List<OriginMaterialResp>> getOriginMaterialCreatorList(
            @RequestHeader(value = USER_ID) @ApiIgnore Integer userId,
            @RequestParam(required = false) @ApiParam("分类") Integer category,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) @ApiParam("审核状态(10:待审核 20:审核通过 30:审核未通过  40:删除)") List<Integer> auditStatusList,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("每页条数") Integer size) {
        return originMaterialsService.getOriginMaterialList(userId, startTime, endTime, null, category, true, auditStatusList, searchContent, current, size);
    }

    @GetMapping("list")
    @ApiOperation("好评素材列表")
    public PageResult<List<OriginMaterialResp>> getOriginMaterialList(
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) @ApiParam("频道id") Long channelId,
            @RequestParam(required = false) @ApiParam("分类") Integer category,
            @RequestParam(required = false) @ApiParam("审核状态(10:待审核 20:审核通过 30:审核未通过  40:删除)") List<Integer> auditStatusList,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("每页条数") Integer size) {
        return originMaterialsService.getOriginMaterialList(null, startTime, endTime, ObjectUtil.isNotNull(channelId) ? Collections.singletonList(channelId) : null, category, null, auditStatusList, searchContent, current, size);
    }

    @GetMapping("visible/list")
    @ApiOperation("可见好评素材列表")
    public PageResult<List<OriginMaterialResp>> getOriginMaterialList(
            @RequestHeader(value = USER_ID) @ApiIgnore Integer userId,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) @ApiParam("频道id") Long channelId,
            @RequestParam(required = false) @ApiParam("分类") Integer category,
            @RequestParam(required = false) @ApiParam("审核状态(10:待审核 20:审核通过 30:审核未通过  40:删除)") List<Integer> auditStatusList,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("每页条数") Integer size) {
        return originMaterialsService.getOriginMaterialList(userId, startTime, endTime, channelId, category, auditStatusList, searchContent, current, size);
    }

    @PostMapping("delete")
    @ApiOperation("删除好评素材")
    public BaseResult<Void> deleteOriginMaterial(
            @RequestHeader(value = USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("好评素材id") Long materialId
    ) {
        return contentClient.deleteOriginMaterial(userId, materialId);
    }

    @GetMapping("operate/record")
    @ApiOperation("好评素材操作记录")
    public BaseResult<List<OriginMaterialOperateRecordResp>> getOriginMaterialOperateRecord(
            @RequestParam @ApiParam("好评素材id") Long materialId
    ) {
        List<OriginMaterialOperateRecordResp> resp = originMaterialsService.getOriginMaterialOperateRecord(materialId);
        return BaseResult.success(resp);
    }

    @GetMapping("watermark/search")
    @ApiOperation("素材水印检索")
    public BaseResult<OriginMaterialResp> materialSearch(
            @RequestParam(required = false) @ApiParam("水印") String watermark) {
        OriginMaterialResp resp = originMaterialsService.getMaterialInfo(watermark);
        return BaseResult.success(resp);
    }

    @PostMapping("audit")
    @ApiOperation("好评素材审核")
    public BaseResult<Void> auditOriginMaterial(
            @RequestHeader(value = USER_ID) @ApiIgnore Integer auditorId,
            @RequestBody @Valid OriginMaterialAuditReq req) {
        req.setAuditorId(auditorId);
        return contentClient.auditOriginMaterial(req);
    }

    @GetMapping("info")
    @ApiOperation("获取好评素材")
    public BaseResult<OriginMaterialResp> getOriginMaterialById(@RequestParam @ApiParam("好评素材id") Long originMaterialId) {
        return BaseResult.success(originMaterialsService.getOriginMaterialById(originMaterialId));
    }


}
