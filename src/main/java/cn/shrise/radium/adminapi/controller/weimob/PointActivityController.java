package cn.shrise.radium.adminapi.controller.weimob;

import cn.shrise.radium.adminapi.resp.weimob.PointActivityResp;
import cn.shrise.radium.adminapi.service.weimob.PointActivityService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.weimobservice.WeiMobClient;
import cn.shrise.radium.weimobservice.entity.PointActivity;
import cn.shrise.radium.weimobservice.req.CreatePointActivityReq;
import cn.shrise.radium.weimobservice.req.EditPointActivityReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import javax.validation.constraints.Null;
import java.util.List;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;

/**
 * <AUTHOR>
 */
@Api
@RestController
@RequestMapping("point_activity")
@RequiredArgsConstructor
public class PointActivityController {

    private final WeiMobClient weiMobClient;

    private final PointActivityService pointActivityService;

    @PostMapping("create")
    @ApiOperation("创建积分活动")
    public BaseResult<Void> createPointActivity(
            @RequestHeader(value = COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestBody @Valid CreatePointActivityReq req) {
        req.setCompanyType(companyType);
        return weiMobClient.createPointActivity(req);
    }

    @PostMapping("edit")
    @ApiOperation("更新积分活动")
    public BaseResult<Void> editPointActivity(@RequestBody @Valid EditPointActivityReq req) {
        return weiMobClient.editPointActivity(req);
    }

    @PostMapping("edit/status")
    @ApiOperation("修改积分活动状态")
    public BaseResult<Void> setPointActivityStatus(
            @RequestParam @ApiParam("积分活动id") Long id,
            @RequestParam @ApiParam("状态") Integer status) {
        return weiMobClient.setPointActivityStatus(id, status);
    }

    @GetMapping("find/number")
    @ApiOperation("通过number获取积分活动")
    public BaseResult<PointActivity> findPointActivityByNumber(
            @RequestParam @ApiParam("编号") String number) {
        return weiMobClient.findPointActivityByNumber(number);
    }

    @GetMapping("list")
    @ApiOperation("获取积分活动列表")
    public PageResult<List<PointActivityResp>> getPointActivityList(
            @RequestHeader(value = COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("每页条数") Integer size,
            @RequestParam(required = false) @ApiParam("状态（0：下架 1：上架  null:全部)") Integer status) {
        return pointActivityService.getPointActivityList(companyType, current, size, status);
    }
}
