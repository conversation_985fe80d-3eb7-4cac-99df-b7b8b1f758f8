package cn.shrise.radium.adminapi.controller.roboadviser;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.req.GetMyStrategySubscriptionListReq;
import cn.shrise.radium.adminapi.resp.roboadviser.StocketResp;
import cn.shrise.radium.adminapi.resp.roboadviser.StrategyPassagewayResp;
import cn.shrise.radium.adminapi.service.CommonService;
import cn.shrise.radium.adminapi.service.roboadviser.StrategyOrderService;
import cn.shrise.radium.adminapi.service.roboadviser.StrategyService;
import cn.shrise.radium.adminapi.service.roboadviser.StrategySubscriptionService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.orderservice.resp.SkuStrategy;
import cn.shrise.radium.roboadviserservice.RoboAdviserServiceClient;
import cn.shrise.radium.roboadviserservice.req.CancelStrategyReq;
import cn.shrise.radium.roboadviserservice.req.EditStrategyReq;
import cn.shrise.radium.roboadviserservice.resp.*;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcCustomerFollowUpRelation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

/**
 * <AUTHOR>
 */
@Api
@RestController
@RequestMapping("strategy")
@RequiredArgsConstructor
public class StrategyController {

    private final RoboAdviserServiceClient roboAdviserServiceClient;
    private final StrategySubscriptionService strategySubscriptionService;
    private final StrategyOrderService strategyOrderService;
    private final CommonService commonService;
    private final StrategyService strategyService;
    private final UserClient userClient;

    @GetMapping("list")
    @ApiOperation("策略列表")
    public PageResult<List<StrategyInfoResp>> getStrategyList(
            @RequestParam(required = false) @ApiParam("择时仓位(0-空仓，1-正常交易)") Integer chooseTimeStatus,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("策略状态:10-可售卖,20-已售罄,30-下架中") Integer status,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size
    ) {
        return strategySubscriptionService.getStrategyList(chooseTimeStatus, searchContent, status, current, size);
    }

    @PostMapping("sync")
    @ApiOperation("同步策略")
    public BaseResult<Void> syncStrategy() {
        return roboAdviserServiceClient.syncStrategy();
    }

    @PostMapping("choose-time/update")
    @ApiOperation("更新择时")
    public BaseResult<Void> updateStrategyChooseTime(@RequestHeader(USER_ID) @ApiIgnore Integer userId,
                                                     @RequestParam @ApiParam("策略code") String strategy,
                                                     @RequestParam @ApiParam("择时仓位(0-空仓，1-正常交易)") Integer timingPosition) {
        return roboAdviserServiceClient.updateStrategyChooseTime(userId, strategy, timingPosition);
    }

    @GetMapping("choose-time/record")
    @ApiOperation("择时记录")
    public PageResult<List<StrategyChooseTimeRecordResp>> getStrategyChooseTimeRecord(@RequestParam @ApiParam("策略id") String strategy,
                                                                                      @RequestParam(required = false) @ApiParam("创建时间开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createStart,
                                                                                      @RequestParam(required = false) @ApiParam("创建时间结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createEnd,
                                                                                      @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
                                                                                      @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size) {
        return strategySubscriptionService.getStrategyChooseTimeRecord(strategy, createStart, createEnd, current, size);
    }

    @GetMapping("all")
    @ApiOperation("全部策略信息")
    public BaseResult<List<StrategyInfoResp>> getAllStrategy(@RequestParam(required = false) @ApiParam("是否启用") Boolean enabled) {
        return roboAdviserServiceClient.getAllStrategy(enabled);
    }

    @GetMapping("subscription/record/list")
    @ApiOperation("策略开通记录列表")
    public PageResult<List<StrategySubscriptionRecordResp>> getStrategySubscriptionRecordList(
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("操作类型") Integer operateType,
            @RequestParam(required = false) @ApiParam("策略id") Long strategyId,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size) {
        return strategySubscriptionService.getStrategySubscriptionRecordList(searchContent, operateType, strategyId, current, size);
    }

    @GetMapping("subscription/list")
    @ApiOperation("策略开通列表")
    public PageResult<List<StrategySubscriptionResp>> getStrategySubscriptionList(
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("策略code") String code,
            @RequestParam(required = false) @ApiParam("排序类型(1: 开通时间, 2: 累计净投入资金 3: 累计入金 4: 累计出金 5: qmt最后运行时间 6: 最大搭载容量)") Integer orderType,
            @RequestParam(required = false) @ApiParam("是否正序") Boolean isAsc,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size) {
        return strategySubscriptionService.getSubscriptionStrategyList(searchContent, code, orderType, isAsc, new ArrayList<>(), current, size);
    }

    @PostMapping("subscription/list/me")
    @ApiOperation("我的策略开通列表")
    public PageResult<List<StrategySubscriptionResp>> getMyStrategySubscriptionList(
            @RequestHeader(USER_ID) @ApiIgnore Integer salesId,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("策略code") String code,
            @RequestParam(required = false) @ApiParam("是否部门管理") Boolean isManager,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size,
            @RequestBody @Valid GetMyStrategySubscriptionListReq req)
    {
        List<Integer> salesLst = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(req.getDeptList())) {
            salesLst = userClient.getUserIdByDepartmentList(req.getDeptList()).getData();
        }
        if (ObjectUtil.isNotEmpty(req.getUserList())) {
            salesLst = req.getUserList();
        }
        if (ObjectUtil.equals(isManager, false)) {
            salesLst.add(salesId);
        }
        List<Integer> userIds = userClient.getRelationByBelongIds(BatchReq.of(salesLst)).getData().stream()
                .map(UcCustomerFollowUpRelation::getUserId).collect(Collectors.toList());
        if (ObjectUtil.isEmpty(userIds)) {
            return PageResult.empty();
        }
        return strategySubscriptionService.getMySubscriptionStrategyList(searchContent, code, 1, false, userIds, salesLst, current, size);
    }

    @PostMapping("subscription/customer/cancel")
    @ApiOperation("客户取消指定策略")
    public BaseResult<Void> cancelStrategy(@RequestBody @Valid CancelStrategyReq req,
                                           @RequestHeader(USER_ID) @ApiIgnore Integer userId) {
        req.setOperatorId(userId);
        return roboAdviserServiceClient.cancelStrategy(req);
    }

    @PostMapping("set/status")
    @ApiOperation("设置策略状态")
    public BaseResult<Void> setStrategyStatus(
            @RequestParam @ApiParam("策略id") Long strategyId,
            @RequestParam @ApiParam("策略状态:10-可售卖,20-已售罄,30-下架中") Integer status) {
        return roboAdviserServiceClient.setStrategyStatus(strategyId, status);
    }

    @PostMapping("edit")
    @ApiOperation("编辑策略")
    public BaseResult<Void> editStrategy(@RequestBody @Valid EditStrategyReq req) {
        return roboAdviserServiceClient.editStrategy(req);
    }

    @GetMapping("order/baseList")
    @ApiOperation("获取策略交易指令基础信息列表")
    public PageResult<List<StrategyOrderBaseResp>> getStrategyOrderBaseList(
            @RequestParam(required = false) @ApiParam("策略code") String code,
            @RequestParam(required = false) @ApiParam("用户code") String userCode,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("创建时间开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createStart,
            @RequestParam(required = false) @ApiParam("创建时间结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createEnd,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size) {
        return strategyOrderService.getStrategyOrderBaseList(code, userCode, searchContent, createStart, createEnd, current, size);
    }

    @GetMapping("order/detail")
    @ApiOperation("策略指令详情")
    public PageResult<List<StrategyOrderDetailResp>> getStrategyOrderRecordList(
            @RequestParam @ApiParam("id") Long strategyOrderId,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size) {
        return roboAdviserServiceClient.getStrategyOrderRecordList(strategyOrderId, current, size);
    }

    @GetMapping("order/recordList")
    @ApiOperation("PC策略信号推送记录")
    public PageResult<List<StrategyOrderBaseResp>> getStrategyOrderPushRecordList(
            @RequestParam(required = false) @ApiParam("策略code") String code,
            @RequestParam(required = false) @ApiParam("用户code") String userCode,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("发送开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createStart,
            @RequestParam(required = false) @ApiParam("发送结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createEnd,
            @RequestParam(required = false) @ApiParam("推送状态") Integer pushStatus,
            @RequestParam(required = false) @ApiParam("用户状态") Integer userStatus,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size) {
        return strategyOrderService.getStrategyOrderList(code, userCode, searchContent, createStart, createEnd, pushStatus, userStatus, current, size);
    }

    @GetMapping("getSocketStatusInfo")
    @ApiOperation("获取PC推送账号连接socket信息")
    public BaseResult<StocketResp> getSocketStatusInfo(@RequestParam @ApiParam("用户code") String userCode) {
        return strategyOrderService.getSocketStatusInfo(userCode);
    }

    @GetMapping("list/socket_record")
    @ApiOperation("获取客户连接记录")
    public PageResult<List<CustomerSocketRecordResp>> listCustomerSocketRecord(
            @RequestParam @ApiParam("用户编号") String userNumber,
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) @ApiParam(value = "页码", defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) @ApiParam(value = "分页数量", defaultValue = DEFAULT_SIZE) Integer size) {
        Integer userId = commonService.getUserIdByNumber(companyType, userNumber, null);
        return strategyOrderService.listCustomerSocketRecord(userId, current, size);
    }

    @GetMapping("passageway/list")
    @ApiOperation("策略通道列表")
    public BaseResult<List<StrategyPassagewayResp>> listStrategyPassageway(
            @RequestParam @ApiParam("策略编号") String strategyCode) {
        List<StrategyPassagewayResp> respList = strategyService.listStrategyPassageway(strategyCode);
        return BaseResult.success(respList);
    }

    @GetMapping("invite/url")
    @ApiOperation("获取售前策略邀请链接")
    public BaseResult<String> getStrategyInviteUrl(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam(required = false) @ApiParam("策略编号") String strategyCode,
            @RequestParam(required = false) @ApiParam("skuId") Integer skuId,
            @RequestParam(required = false, defaultValue = "false") @ApiParam("是否列表页") Boolean isList) {
        return strategyService.getStrategyUrl(companyType, userId, strategyCode, skuId, isList);
    }

    @GetMapping("sku-list")
    @ApiOperation("策略sku列表")
    public BaseResult<List<SkuStrategy>> getStrategySkuList(
            @RequestParam @ApiParam("策略id") Long strategyId,
            @RequestParam(required = false) @ApiParam("策略状态:1-待上架,2-已上架,3-已下架") Integer status) {
        return strategyService.getStrategySkuList(strategyId, status);
    }

    @GetMapping("back-test/list")
    @ApiOperation("后台客户回测记录")
    public PageResult<List<StrategyBackTestListResp>> getStrategyBackTestList(
            @RequestParam @ApiParam("客户id") String customerId,
            @RequestParam(required = false) @ApiParam("开始创建时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createStart,
            @RequestParam(required = false) @ApiParam("结束创建时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createEnd,
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) @ApiParam(value = "页码", defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) @ApiParam(value = "分页数量", defaultValue = DEFAULT_SIZE) Integer size) {
        Integer userId = commonService.getUserIdByCustomerId(customerId);
        return strategyService.getStrategyBackTestList(userId, createStart, createEnd, current, size);
    }

    @GetMapping("simulation/list")
    @ApiOperation("后台客户模拟实盘记录")
    public PageResult<List<StrategySimulationListResp>> getStrategyTerminateList(
            @RequestParam @ApiParam("客户id") String customerId,
            @RequestParam(required = false) @ApiParam("开始创建时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createStart,
            @RequestParam(required = false) @ApiParam("结束创建时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate createEnd,
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) @ApiParam(value = "页码", defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) @ApiParam(value = "分页数量", defaultValue = DEFAULT_SIZE) Integer size) {
        Integer userId = commonService.getUserIdByCustomerId(customerId);
        return strategyService.getStrategyTerminateList(userId, createStart, createEnd, current, size);
    }

    @GetMapping("simulation/user-list")
    @ApiOperation("通过用户id查询模拟实盘记录")
    public BaseResult<List<StrategySimulationListResp>> getStrategyTerminateUserList(@RequestParam @ApiParam("客户code") String userCode) {
        int userId = DesensitizeUtil.maskToId(userCode);
        return roboAdviserServiceClient.getStrategyTerminateUserList(userId);
    }

    @GetMapping("signal-list")
    @ApiOperation("获取策略信号列表")
    public PageResult<List<StrategySignalResp>> getSignalList(@RequestParam @ApiParam("策略code") String strategyCode,
                                                              @RequestParam(required = false) @ApiParam("交易日期") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate tradeDate,
                                                              @RequestParam(required = false) boolean isAsc,
                                                              @RequestParam(required = false) @ApiParam("排序类型(1-股票，2-权重)") Integer orderType,
                                                              @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
                                                              @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size
    ) {
        return roboAdviserServiceClient.getSignalList(strategyCode, tradeDate, isAsc, orderType, current, size);
    }

    @ApiOperation("策略服务开通重试")
    @PostMapping("open/retry")
    public BaseResult<Void> openRetry(
            @RequestParam @ApiParam("记录id") Long id,
            @RequestHeader(USER_ID) @ApiIgnore Integer operatorId
    ) {
        return roboAdviserServiceClient.openRetry(id, operatorId);
    }
}
