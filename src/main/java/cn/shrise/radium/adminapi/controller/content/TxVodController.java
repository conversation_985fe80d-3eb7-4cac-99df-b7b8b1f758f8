package cn.shrise.radium.adminapi.controller.content;

import cn.shrise.radium.contentservice.resp.promotion.TxVodSignatureResp;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.contentservice.ContentClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @created : 2025/7/7, 星期一
 **/
@Api
@Slf4j
@RestController
@RequestMapping("tx-vod")
@RequiredArgsConstructor
public class TxVodController {

    private final ContentClient contentClient;

    @GetMapping("signature")
    @ApiOperation("获取腾讯云点播上传签名")
    public BaseResult<TxVodSignatureResp> getVodSignature() {
        return contentClient.getTxVodSignature();
    }

}
