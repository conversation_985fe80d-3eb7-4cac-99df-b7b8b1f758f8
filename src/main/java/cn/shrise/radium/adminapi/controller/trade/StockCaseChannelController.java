package cn.shrise.radium.adminapi.controller.trade;

import cn.shrise.radium.adminapi.resp.StockCaseChannelResp;
import cn.shrise.radium.adminapi.resp.trade.StockCaseChannelRecordResp;
import cn.shrise.radium.adminapi.service.trade.CaseChannelService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.constant.TradeErrorCode;
import cn.shrise.radium.tradeservice.req.EditStockCaseChannelReq;
import cn.shrise.radium.tradeservice.req.UpdateCaseChannelHideReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@Api
@RestController
@RequestMapping("stock-case-channel")
@RequiredArgsConstructor
public class StockCaseChannelController {


    private final CaseChannelService caseChannelService;
    private final TradeClient tradeClient;

    @GetMapping("list")
    @ApiOperation("获取案例频道列表")
    public PageResult<List<StockCaseChannelResp>> getStockCaseChannelList(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(required = false) @ApiParam("用户Id") Integer userId,
            @RequestParam(required = false) @ApiParam("频道类型") Integer channelType,
            @RequestParam(required = false) @ApiParam("搜索") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return caseChannelService.getStockCaseChannelList(userId, channelType, companyType, searchContent, current, size);
    }

    @PostMapping("create-or-update")
    @ApiOperation("创建/编辑案例频道")
    public BaseResult<TradeErrorCode> createOrUpdateCaseChannel(
            @RequestHeader(COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @Validated @RequestBody EditStockCaseChannelReq req) {
        req.setCompanyType(companyType);
        req.setUserId(userId);
        return tradeClient.createOrUpdateCaseChannel(req);
    }

    @GetMapping("record/list")
    @ApiOperation("获取案例频道操作记录列表")
    public PageResult<List<StockCaseChannelRecordResp>> getStockCaseChannelRecordList(
            @RequestParam @ApiParam("频道id") Long channelId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "10") @ApiParam("每页条数") Integer size) {
        return caseChannelService.getStockCaseChannelRecordList(channelId, current, size);
    }

    @PostMapping("set-follow-hide")
    @ApiOperation("设置股票池频道解盘股票跟踪前端是否隐藏")
    public BaseResult<Void> setFollowHide(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @Validated @RequestBody UpdateCaseChannelHideReq req) {
        req.setOperatorId(userId);
        return tradeClient.setFollowHide(req);
    }
}
