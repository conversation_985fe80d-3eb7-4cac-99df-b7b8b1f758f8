package cn.shrise.radium.adminapi.controller.user;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.shrise.radium.adminapi.entity.CustomerInfo;
import cn.shrise.radium.adminapi.resp.user.CustomerResp;
import cn.shrise.radium.adminapi.resp.user.SeatUserRelationResp;
import cn.shrise.radium.adminapi.service.user.CallService;
import cn.shrise.radium.adminapi.util.IpUtils;
import cn.shrise.radium.secureservice.SecureServiceClient;
import cn.shrise.radium.userservice.constant.UserTypeConstant;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.resp.call.*;
import cn.shrise.radium.adminapi.service.CommonService;
import cn.shrise.radium.adminapi.service.user.UserSeatInfoService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.userservice.UserClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@Api
@Slf4j
@RestController
@RequestMapping("call")
@RequiredArgsConstructor
public class CallController {

    private final CommonService commonService;
    private final UserClient userClient;
    private final UserSeatInfoService userSeatInfoService;
    private final CallService callService;
    private final SecureServiceClient secureServiceClient;

    @GetMapping("seat/just-call/list")
    @ApiOperation("获取集时云坐席列表")
    public PageResult<List<JustCallSeatResp>> getJustCallSeatList(
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size) {
        return userClient.getJustCallSeatList(current, size);
    }

    @PostMapping("seat/just-call/create")
    @ApiOperation("添加集时云坐席")
    public BaseResult<String> createSeat(
            @RequestBody @Valid CreateJustCallSeatReq req) {
        return userClient.createSeat(req);
    }

    @GetMapping("seat/tencent/list")
    @ApiOperation("获取腾讯云坐席列表")
    public PageResult<List<TencentSeatResp>> getTencentCallSeatList(
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size) {
        return userSeatInfoService.getTencentCallSeatList(current, size);
    }

    @PostMapping("seat/tencent/update")
    @ApiOperation("更新腾讯云坐席信息")
    public BaseResult<String> updateTxSeat(@RequestHeader(value = USER_ID) @ApiIgnore Integer userId) {
        return userClient.updateTxSeat(userId);
    }

    @PostMapping("seat/tencent/update-assigned-phone")
    @ApiOperation("更新腾讯云坐席分配号码")
    public BaseResult<Void> updateTxSeatAssignedPhone(@RequestParam @ApiParam("坐席id") Long seatId,
                                                      @RequestParam(required = false) @ApiParam("分配号码") String assignedPhone) {
        return userClient.updateTxSeatAssignedPhone(seatId, assignedPhone);
    }

    @GetMapping("seat/tencent/get-update-result")
    @ApiOperation("查询腾讯云坐席更新结果")
    public BaseResult<UpdateTxSeatResult> getUpdateResult() {
        return userClient.getUpdateTxResult();
    }

    @PostMapping("seat/bind")
    @ApiOperation("绑定或解绑坐席用户")
    public BaseResult<String> bindSeatUser(
            @RequestHeader(value = COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam @ApiParam("坐席id") Long seatId,
            @RequestParam @ApiParam("坐席类型") Integer seatType,
            @RequestParam(required = false) @ApiParam("销售number（该参数不传即为解绑操作）") String userNumber) {
        Integer serverId = null;
        if (ObjectUtil.isNotEmpty(userNumber)) {
            serverId = commonService.getStaffIdByNumber(companyType, userNumber);
        }
        return userClient.bindSeatUser(seatId, seatType, serverId);
    }

    @GetMapping("tencent/call-list")
    @ApiOperation("获取腾讯云通话记录")
    public PageResult<List<TxCallRecordResp>> getTxCallRecordList(
            @RequestHeader(value = COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(defaultValue = "ALL") @ApiParam("接口类型（ALL: 全部，PERSON: 个人）") String type,
            @RequestHeader(value = USER_ID, required = false) @ApiIgnore Integer userId,
            @RequestParam(required = false) @ApiParam("客户number") String customerNumber,
            @RequestParam(required = false) @ApiParam("通话类型（呼入/呼出）") Boolean isCallIn,
            @RequestParam(required = false) @ApiParam("通话状态") Integer callStatus,
            @RequestParam(required = false) @ApiParam("通话时间筛选") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate callTime,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchText,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size,
            @RequestParam(required = false) @ApiParam("菜单") String menuName,
            @ApiIgnore HttpServletRequest request) {
        if (ObjectUtil.isNotEmpty(menuName) && PhoneUtil.isMobile(searchText)) {
            secureServiceClient.mobileMatchRecord(searchText, UserTypeConstant.STAFF, userId, menuName, null, IpUtils.getClientIp(request));
        }
        return callService.getTxCallRecordList(companyType, type, userId, customerNumber, isCallIn, callStatus, callTime, searchText, current, size);
    }

    @GetMapping("tencent/call-config")
    @ApiOperation("获取打电话配置")
    public BaseResult<TxCallConfigResp> getCallConfig(
            @RequestParam @ApiParam("坐席邮箱") String seatMail) {
        return userClient.getCallConfig(seatMail);
    }

    @ApiOperation("用户-坐席信息")
    @GetMapping("user-seat")
    public BaseResult<SeatUserRelationResp> findUserSeatInfo(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId
    ) {
        return BaseResult.success(userSeatInfoService.findUserSeatInfo(userId));
    }

    @ApiOperation("获取集时云通话记录")
    @GetMapping("just/call-list")
    public PageResult<List<JustCallRecordResp>> findJustCallRecordList(
            @RequestHeader(value = COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(defaultValue = "ALL") @ApiParam("接口类型（ALL: 全部，PERSON: 个人）") String type,
            @RequestHeader(value = USER_ID, required = false) @ApiIgnore Integer userId,
            @RequestParam(required = false) @ApiParam("客户number") String customerNumber,
            @RequestParam(required = false) @ApiParam("callId") String callId,
            @RequestParam(required = false) @ApiParam("通话类型") Boolean isCallIn,
            @RequestParam(required = false) @ApiParam("通话状态") Integer callStatus,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startDay,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endDay,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size,
            @RequestParam(required = false) @ApiParam("菜单") String menuName,
            @ApiIgnore HttpServletRequest request) {
        if (ObjectUtil.isNotEmpty(menuName) && PhoneUtil.isMobile(searchContent)) {
            secureServiceClient.mobileMatchRecord(searchContent, UserTypeConstant.STAFF, userId, menuName, null, IpUtils.getClientIp(request));
        }
        return callService.getJustCallRecordList(companyType, type, userId, customerNumber, callId, isCallIn, callStatus, startDay, endDay, searchContent, current, size);
    }

    @ApiOperation("按通话id获取腾讯云通话记录")
    @GetMapping("tencent/record-info")
    public BaseResult<TxCallRecordResp> getTxCallRecordByCallId(
            @RequestParam @ApiParam("callId") Long callId
    ) {
        return userClient.getTxCallRecordByCallId(callId);
    }

    @ApiOperation("按通话id对应客户电话号码获取腾讯云通话记录列表")
    @GetMapping("tencent/record-list/by-call-id")
    public PageResult<List<TxCallRecordResp>> getTxCallRecordByCustomerMobileOfCallId(
            @RequestHeader(value = COMPANY_TYPE) @ApiIgnore Integer companyType,
            @RequestParam(required = true) @ApiParam("callId") Long callId,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size
    ) {
        return userClient.getTxCallRecordByCustomerMobileOfCallId(companyType, callId, current, size);
    }

    @GetMapping("tencent/user-info")
    @ApiOperation("sessionId获取用户信息")
    public BaseResult<CustomerInfo> getUserInfo(@RequestHeader(COMPANY_TYPE) Integer companyType, @RequestParam String sessionId) {
        UcUsers ucUsers = userClient.getTencentCccUserInfo(companyType, sessionId).orElse(null);
        return BaseResult.success(CustomerInfo.of(ucUsers));
    }
}
