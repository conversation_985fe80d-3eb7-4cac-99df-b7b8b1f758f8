package cn.shrise.radium.adminapi.controller.trade;

import cn.shrise.radium.adminapi.resp.trade.RecommendStockModifyResp;
import cn.shrise.radium.adminapi.resp.trade.RecommendStockResp;
import cn.shrise.radium.adminapi.service.trade.RecommendStockService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.req.EditRecommendStockReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.time.LocalDateTime;
import java.util.List;

import static cn.shrise.radium.common.constant.HeaderConstant.COMPANY_TYPE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

/**
 * <AUTHOR>
 * @version 1.0
 * desc: A股情报-股票池
 */
@Api
@RestController
@RequestMapping("trade/recommend/stock")
@RequiredArgsConstructor
public class RecommendStockController {

    private final TradeClient tradeClient;
    private final RecommendStockService recommendStockService;

    @GetMapping("audit")
    @ApiOperation("A股情报-股票池审核列表")
    public PageResult<List<RecommendStockResp>> getRecommendStockAuditList(
            @ApiIgnore @RequestHeader(COMPANY_TYPE) Integer companyType,
            @RequestParam @ApiParam("是否审核") Boolean isAudit,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size) {
        return recommendStockService.getRecommendStockAuditList(companyType, isAudit, current, size);
    }

    @GetMapping("modify/list")
    @ApiOperation("A股情报-股票池修改记录列表")
    public PageResult<List<RecommendStockModifyResp>> getRecommendStockModifyRecordPage(
            @RequestParam @ApiParam("股票id") Integer stockId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "10") @ApiParam("每页条数") Integer size) {
        return recommendStockService.getRecommendStockModifyRecordPage(stockId, current, size);
    }

    @PostMapping
    @ApiOperation("A股情报-股票池 新增股票")
    public BaseResult<String> addRecommendStock(
            @ApiIgnore @RequestHeader(COMPANY_TYPE) Integer companyType,
            @ApiIgnore @RequestHeader(USER_ID) Integer userId,
            @Validated @RequestBody EditRecommendStockReq req) {
        req.setCreatorId(userId);
        req.setCompanyType(companyType);
        return tradeClient.addRecommendStock(req);
    }

    @PostMapping("{stockId}")
    @ApiOperation("A股情报-股票池 修改股票")
    public BaseResult<String> updateRecommendStock(
            @ApiIgnore @RequestHeader(COMPANY_TYPE) Integer companyType,
            @ApiIgnore @RequestHeader(USER_ID) Integer userId,
            @PathVariable Integer stockId,
            @Validated @RequestBody EditRecommendStockReq req) {
        req.setCompanyType(companyType);
        return tradeClient.updateRecommendStock(userId, stockId, req);
    }

    @DeleteMapping("{stockId}")
    @ApiOperation("A股情报-股票池 删除股票")
    public BaseResult<String> deleteRecommendStock(
            @ApiIgnore @RequestHeader(USER_ID) Integer userId,
            @PathVariable Integer stockId) {
        return tradeClient.deleteRecommendStock(userId, stockId);
    }

    @PostMapping("audit/{stockId}")
    @ApiOperation("A股情报-股票池 审核股票")
    public BaseResult<String> auditRecommendStock(
            @ApiIgnore @RequestHeader(USER_ID) Integer userId,
            @PathVariable Integer stockId,
            @RequestParam @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("审核内容") String auditRemark) {
        return tradeClient.auditRecommendStock(userId, stockId, auditStatus, auditRemark);
    }

    @PutMapping("{stockId}")
    @ApiOperation("A股情报-股票池 置顶")
    public BaseResult<String> updateRecommendStockTopStatus(@PathVariable Integer stockId,
                                                            @RequestParam @ApiParam("榜单") Boolean isTop) {
        return tradeClient.updateRecommendStockTopStatus(stockId, isTop);
    }

}
