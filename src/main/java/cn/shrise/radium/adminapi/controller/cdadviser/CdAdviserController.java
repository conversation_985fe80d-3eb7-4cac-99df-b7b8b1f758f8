package cn.shrise.radium.adminapi.controller.cdadviser;

import cn.shrise.radium.adminapi.service.cdadviser.CdAdviserService;
import cn.shrise.radium.adminapi.service.cdadviser.CdAdviserSubscriptionService;
import cn.shrise.radium.cdadviserservice.req.*;
import cn.shrise.radium.cdadviserservice.resp.*;
import cn.shrise.radium.cdadviserservice.CdAdviserServiceClient;
import cn.shrise.radium.cdadviserservice.req.OpenCdAdviserSubscriptionReq;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@Api("智投-财达版")
@RestController
@RequiredArgsConstructor
@RequestMapping("cd-adviser")
public class CdAdviserController {

    private final CdAdviserServiceClient cdAdviserServiceClient;

    private final CdAdviserService cdAdviserService;
    private final CdAdviserSubscriptionService cdAdviserSubscriptionService;

    @GetMapping("strategy/list")
    @ApiOperation("策略管理列表")
    public PageResult<List<CdAdviserStrategyResp>> getCdAdviserStrategyList(
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return cdAdviserService.getCdAdviserStrategyList(current, size);
    }

    @PostMapping("strategy/create")
    @ApiOperation("创建策略")
    public BaseResult<Void> createCdAdviserStrategy(
            @RequestHeader(value = USER_ID) @ApiIgnore Integer operatorId,
            @RequestBody @Valid CdAdviserStrategyReq req
    ) {
        req.setOperatorId(operatorId);
        return cdAdviserService.createCdAdviserStrategy(req);
    }

    @PostMapping("strategy/update")
    @ApiOperation("编辑策略")
    public BaseResult<Void> updateCdAdviserStrategy(
            @RequestHeader(value = USER_ID) @ApiIgnore Integer operatorId,
            @RequestBody @Valid CdAdviserStrategyReq req
    ) {
        req.setOperatorId(operatorId);
        return cdAdviserService.updateCdAdviserStrategy(req);
    }

    @GetMapping("strategy/record-list")
    @ApiOperation("策略操作记录")
    public PageResult<List<CdAdviserStrategyRecordResp>> getCdAdviserStrategyOperateRecordList(
            @RequestParam @ApiParam("策略ID") String code,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return cdAdviserService.getCdAdviserStrategyOperateRecordList(code, current, size);
    }

    @GetMapping("strategy/selection-stock/list")
    @ApiOperation("交易信号")
    public PageResult<List<CdAdviserStrategySelectionStockResp>> getCdAdviserStrategySelectionStockList(
            @RequestParam @ApiParam("日期") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate date,
            @RequestParam @ApiParam("策略ID") String code,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return cdAdviserService.getCdAdviserStrategySelectionStockList(date, code, current, size);
    }

    @PostMapping("strategy/time-position/update")
    @ApiOperation("设置仓位")
    public BaseResult<Void> updateCdAdviserStrategyTimePosition(
            @RequestParam @ApiParam("策略ID") String code,
            @RequestParam @ApiParam("仓位") Double position,
            @RequestHeader(value = USER_ID) @ApiIgnore Integer operatorId
    ) {
        return cdAdviserService.updateCdAdviserStrategyTimePosition(code, position, operatorId);
    }

    @GetMapping("strategy/time-position/record-list")
    @ApiOperation("仓位设置记录")
    public PageResult<List<CdAdviserStrategyTimePositionRecordResp>> getCdAdviserStrategyTimePositionRecordList(
            @RequestParam @ApiParam("策略ID") String code,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size
    ) {
        return cdAdviserService.getCdAdviserStrategyTimePositionRecordList(code, current, size);
    }

    @GetMapping("request-detail")
    @ApiOperation("获取api接口详情")
    public BaseResult<CdAdviserRequestDetailResp> getCdAdviserRequestDetail(
            @RequestParam @ApiParam("请求ID") String requestId
    ) {
        return cdAdviserService.getCdAdviserRequestDetail(requestId);
    }

    @GetMapping("subscription/list")
    @ApiOperation("获取财达用户订阅列表")
    PageResult<List<CdAdviserSubscriptionInfoResp>> getCdAdviserCustomerList(
            @RequestParam(defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) Integer size) {
        return cdAdviserSubscriptionService.getCdAdviserCustomerList(current, size);
    }

    @PostMapping("subscription/open")
    @ApiOperation("开通策略")
    BaseResult<Void> openCdAdviserSubscription(
            @RequestHeader(USER_ID) @ApiIgnore Integer operatorId,
            @RequestBody @Valid OpenCdAdviserSubscriptionReq req) {
        return cdAdviserSubscriptionService.openCdAdviserSubscription(operatorId, req);
    }

    @PostMapping("subscription/cancel")
    @ApiOperation("关闭策略")
    BaseResult<Void> cancelCdAdviserSubscription(
            @RequestHeader(USER_ID) @ApiIgnore Integer operatorId,
            @RequestParam @ApiParam("id") Long id) {
        return cdAdviserSubscriptionService.cancelCdAdviserSubscription(operatorId, id);
    }

    @GetMapping("subscription/record-list")
    @ApiOperation("策略订阅记录列表")
    PageResult<List<CdAdviserSubscriptionRecordListResp>> getCdAdviserSubscriptionRecordList(
            @RequestParam(defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) Integer size) {
        return cdAdviserSubscriptionService.getCdAdviserSubscriptionRecordList(current, size);
    }

    @PostMapping("subscription/trade-time/update")
    @ApiOperation("修改交易时间")
    BaseResult<Void> updateCdAdviserTradeTime(
            @RequestBody @Valid CdUpdateTradeTimeReq req
    ) {
        return cdAdviserServiceClient.updateCdAdviserTradeTime(req);
    }

}
