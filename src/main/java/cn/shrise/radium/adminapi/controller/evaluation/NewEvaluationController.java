package cn.shrise.radium.adminapi.controller.evaluation;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.adminapi.context.AuthContextHolder;
import cn.shrise.radium.adminapi.service.CommonService;
import cn.shrise.radium.adminapi.service.evaluation.NewEvaluationService;
import cn.shrise.radium.adminapi.service.evaluation.VerifyService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.util.AESUtil;
import cn.shrise.radium.common.util.DesensitizeUtil;
import cn.shrise.radium.common.util.EvaluationUtil;
import cn.shrise.radium.userservice.UserClient;
import cn.shrise.radium.userservice.entity.UcEvaluationInfo;
import cn.shrise.radium.userservice.entity.UcProfileInfo;
import cn.shrise.radium.userservice.entity.UcVerifyInfo;
import cn.shrise.radium.userservice.req.evaluation.AddEvaluationVersionReq;
import cn.shrise.radium.userservice.req.evaluation.EvaluationVersionScoreConfigReq;
import cn.shrise.radium.userservice.req.evaluation.EvaluationVersionTemplateConfigReq;
import cn.shrise.radium.userservice.resp.evaluation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;

import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

@Api
@Slf4j
@RestController
@RequestMapping("new-evaluation")
@RequiredArgsConstructor
public class NewEvaluationController {

    private final UserClient userClient;
    private final CommonService commonService;
    private final NewEvaluationService evaluationService;
    private final VerifyService verifyService;
    private final EvaluationUtil evaluationUtil;

    @ApiOperation("测评问卷版本列表")
    @GetMapping("version/list")
    public PageResult<List<EvaluationVersionResp>> getEvaluationVersionList(
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return userClient.getEvaluationVersionList(current, size);
    }

    @ApiOperation("测评问卷版本详情")
    @GetMapping("version/detail")
    public BaseResult<EvaluationVersionDetailResp> getEvaluationVersionDetail(
            @RequestParam @ApiParam("版本id") Long id) {
        return userClient.getEvaluationVersionDetail(id);
    }

    @ApiOperation("查看测评问卷配置")
    @GetMapping("version/config-info")
    public BaseResult<List<EvaluationVersionConfigResp>> getEvaluationVersionConfigInfo(
            @RequestParam @ApiParam("版本id") Long id) {
        return userClient.getEvaluationVersionConfigInfo(id);
    }

    @ApiOperation("新建测评问卷")
    @PostMapping("version/add")
    public BaseResult<Void> addEvaluationVersion(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestBody @Valid AddEvaluationVersionReq req) {
        return userClient.addEvaluationVersion(userId, req);
    }

    @ApiOperation("测评分数区间配置")
    @PostMapping("version/score-config")
    public BaseResult<Void> editEvaluationVersionScoreConfig(
            @RequestBody @Valid EvaluationVersionScoreConfigReq req) {
        return userClient.editEvaluationVersionScoreConfig(req);
    }

    @ApiOperation("测评文件模板配置")
    @PostMapping("version/template-config")
    public BaseResult<Void> editEvaluationVersionTemplateConfig(
            @RequestBody @Valid EvaluationVersionTemplateConfigReq req) {
        return userClient.editEvaluationVersionTemplateConfig(req);
    }

    @ApiOperation("发布测评问卷版本")
    @PostMapping("version/publish")
    public BaseResult<Void> editEvaluationVersionPublish(
            @RequestHeader(USER_ID) @ApiIgnore Integer userId,
            @RequestParam @ApiParam("版本id") Long id) {
        return userClient.editEvaluationVersionPublish(userId, id);
    }

    @ApiOperation("测评问卷版本操作记录")
    @GetMapping("version/operate-record")
    public PageResult<List<EvaluationVersionOperateRecordResp>> getEvaluationVersionOperateRecord(
            @RequestParam @ApiParam("版本id") Long id,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return userClient.getEvaluationVersionOperateRecord(id, current, size);
    }

    @GetMapping("evaluation/info")
    @ApiOperation("获取用户的测评信息")
    public BaseResult<NewEvaluateInfoResp> getEvaluationInfo(
            @RequestParam @ApiParam("用户编号") String userNumber) {
        Integer userId = commonService.getUserIdByNumber(AuthContextHolder.getCompanyType(), userNumber);
        return evaluationService.getEvaluationInfo(userId);
    }

    @GetMapping("verify/info")
    @ApiOperation("获取用户的认证信息")
    public BaseResult<VerifyInfoResp> getVerifyInfo(
            @RequestParam @ApiParam("用户编号") String userNumber) {
        Integer userId = commonService.getUserIdByNumber(AuthContextHolder.getCompanyType(), userNumber);
        UcVerifyInfo verifyInfo = userClient.getVerifyInfo(userId).orElse(null);
        if (ObjectUtil.isEmpty(verifyInfo)) {
            return BaseResult.success(null);
        }
        VerifyInfoResp resp = VerifyInfoResp.builder()
                .id(verifyInfo.getId())
                .gmtCreate(verifyInfo.getGmtCreate())
                .identityType(verifyInfo.getIdentityType())
                .verifyType(verifyInfo.getVerifyType())
                .verifyStatus(verifyInfo.getVerifyStatus())
                .maskMobile(verifyInfo.getMaskMobile())
                .identityNumber(DesensitizedUtil.idCardNum(AESUtil.decrypt(verifyInfo.getIdentityNumber()), 4, 4))
                .name(verifyInfo.getName())
                .age(verifyInfo.getAge())
                .finishTime(verifyInfo.getFinishTime())
                .build();
        return BaseResult.success(resp);
    }

    @GetMapping("evaluation/redo")
    @ApiOperation("重新测评")
    public BaseResult<Void> redoEvaluation(
            @RequestHeader(USER_ID) @ApiIgnore Integer operatorId,
            @RequestParam @ApiParam("用户code") String userCode,
            @RequestParam @ApiParam("测评编号") String evaluationNumber) {
        Integer userId = DesensitizeUtil.maskToId(userCode);
        BaseResult<Void> evaluationResult = userClient.deprecateEvaluation(operatorId, evaluationNumber, true);
        if (evaluationResult.isSuccess()) {
            UcProfileInfo profileInfo = userClient.getProfileInfo(userId).orElse(null);
            if(ObjectUtil.isNotEmpty(profileInfo)) {
                userClient.deleteProfile(userId);
            }
        }
        return evaluationResult;
    }

    @GetMapping("verify/redo")
    @ApiOperation("重新认证")
    public BaseResult<Void> redoVerify(
            @RequestHeader(USER_ID) @ApiIgnore Integer operatorId,
            @RequestParam @ApiParam("用户code") String userCode,
            @RequestParam @ApiParam("认证id") Long verifyId) {
        Integer userId = DesensitizeUtil.maskToId(userCode);
        BaseResult<Void> verifyResult = userClient.deprecateVerify(verifyId, operatorId);
        if (verifyResult.isSuccess()) {
            UcEvaluationInfo evaluationInfo = userClient.getEvaluationInfo(userId).orElse(null);
            if (evaluationInfo != null && ObjectUtil.isNotEmpty(evaluationInfo.getSurveyScore())) {
                userClient.deprecateEvaluation(operatorId, evaluationInfo.getNumber(), true);
                UcProfileInfo profileInfo = userClient.getProfileInfo(userId).orElse(null);
                if (ObjectUtil.isNotEmpty(profileInfo)) {
                    userClient.deleteProfile(userId);
                }
            }
        }
        return verifyResult;
    }

    @ApiOperation("获取客户测评操作记录")
    @GetMapping("evaluation/record-list")
    public PageResult<List<EvaluationRecordResp>> getEvaluationRecordList(
            @RequestParam @ApiParam("用户code") String userCode,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        Integer userId = DesensitizeUtil.maskToId(userCode);
        return userClient.getEvaluationRecordList(userId, current, size);
    }

    @ApiOperation("获取客户认证操作记录")
    @GetMapping("verify/record-list")
    public PageResult<List<VerifyRecordResp>> getVerifyRecordList(
            @RequestParam @ApiParam("用户code") String userCode,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        Integer userId = DesensitizeUtil.maskToId(userCode);
        return userClient.getVerifyRecordList(userId, current, size);
    }

    @ApiOperation("获取客户测评记录")
    @GetMapping("evaluation/list")
    public PageResult<List<NewEvaluateInfoResp>> getEvaluationList(
            @RequestParam @ApiParam("用户code") String userCode,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return evaluationService.getEvaluationList(userCode, current, size);
    }

    @ApiOperation("获取客户认证记录")
    @GetMapping("verify/list")
    public PageResult<List<VerifyInfoResp>> getVerifyList(
            @RequestParam @ApiParam("用户code") String userCode,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size) {
        return verifyService.getVerifyList(userCode, current, size);
    }
}
