package cn.shrise.radium.adminapi.controller.content;

import cn.shrise.radium.adminapi.service.promotion.PromotionVideoService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.contentservice.ContentClient;
import cn.shrise.radium.contentservice.req.promotion.*;
import cn.shrise.radium.contentservice.resp.promotion.PromotionVideoInfoResp;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.shrise.radium.common.base.PageResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.annotations.ApiParam;
import cn.shrise.radium.common.util.DateUtils;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

/**
 * <AUTHOR> zhangjianwu
 * @created : 2025/7/4, 星期五
 **/
@Api
@Slf4j
@RestController
@RequestMapping("promotion-video")
@RequiredArgsConstructor
public class PromotionVideoController {

    private final PromotionVideoService promotionVideoService;
    private final ContentClient contentClient;

    @PostMapping("video/create")
    @ApiOperation("上传营销视频")
    public BaseResult<Void> create(@RequestBody @Valid PromotionVideoUploadReq req,
                                   @ApiIgnore @RequestHeader(USER_ID) Integer creatorId) {
        req.setCreatorId(creatorId);
        return contentClient.createPromotionVideo(req);
    }

    @PostMapping("video/edit")
    @ApiOperation("编辑营销视频")
    public BaseResult<Void> editVideo(@RequestBody @Valid PromotionVideoEditReq req) {
        return contentClient.editPromotionVideo(req);
    }

    @PostMapping("video/set-department")
    @ApiOperation("设置视频可见部门")
    public BaseResult<Void> setVideoDepartments(@RequestBody @Valid PromotionVideoSetDepartmentReq req,
                                                @ApiIgnore @RequestHeader(USER_ID) Integer userId) {
        req.setOperatorId(userId);
        return contentClient.setVideoDepartments(req);
    }

    @GetMapping("video/department-list")
    @ApiOperation("视频可见部门")
    public BaseResult<List<Integer>> getCaseChannelManagerRelations(
            @RequestParam(required = false) @ApiParam("视频id") Long videoId
    ) {
        return contentClient.getVideoVisibleDepartments(videoId);
    }

    @GetMapping("video/info")
    @ApiOperation("获取营销视频详情")
    public BaseResult<PromotionVideoInfoResp> getVideoInfo(@RequestParam Long videoId) {
        return BaseResult.success(promotionVideoService.getVideoInfo(videoId));
    }

    @GetMapping("video/list")
    @ApiOperation("获取营销视频列表")
    public PageResult<List<PromotionVideoInfoResp>> listVideos(
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("每页条数") Integer size,
            @RequestParam(required = false) @ApiParam("搜索类型(10:id 20:描述 )") Integer searchType,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startDate,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endDate,
            @RequestParam(required = false) @ApiParam("审核状态 10待审 20通过 30拒绝") List<Integer> auditStatusList,
            @RequestParam(required = false) @ApiParam("展示状态(10:待上架 20:上架中 30:下架中)") Integer isShow,
            @RequestParam(required = false) @ApiParam("标签ID列表") List<Long> tagList,
            @RequestParam(required = false) @ApiParam("上传者ID列表") List<Integer> creatorList,
            @RequestParam(required = false) @ApiParam("视频类型 10:策划 20:销售") Integer videoType,
            @RequestParam(required = false) @ApiParam("发布者id") Long publisherId,
            @RequestHeader(USER_ID) Integer userId) {
        return promotionVideoService.listVideos(current, size, searchType, searchContent, startDate, endDate, auditStatusList, isShow, tagList, creatorList, videoType, publisherId, userId);
    }

    @GetMapping("video/list-share")
    @ApiOperation("获取分享营销视频列表")
    public PageResult<List<PromotionVideoInfoResp>> listShareVideos(
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) @ApiParam("每页条数") Integer size,
            @RequestParam(required = false) @ApiParam("搜索类型(10:id 20:描述 )") Integer searchType,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(required = false) @ApiParam("标签ID列表") List<Long> tagList,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startDate,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endDate,
            @RequestHeader(USER_ID) Integer userId) {
        return promotionVideoService.listShareVideos(current, size, searchType, searchContent, tagList, startDate, endDate, userId);
    }

    @PostMapping("video/audit")
    @ApiOperation("审核营销视频")
    public BaseResult<Void> auditVideo(@RequestBody @Valid PromotionVideoAuditReq req,
                                       @RequestHeader(USER_ID) Integer userId) {
        req.setOperatorId(userId);
        return contentClient.auditPromotionVideo(req);
    }

    @PostMapping("video/set-show-status")
    @ApiOperation("视频上下架")
    public BaseResult<Void> setShowStatus(@RequestParam Long videoId,
                                          @RequestParam Boolean isShow) {
        return contentClient.setShowStatus(videoId, isShow);
    }

    @PostMapping("publisher/list")
    @ApiOperation("发布者列表")
    public PageResult<List<PromotionVideoPublisherResp>> publisherList(@RequestBody PromotionVideoPublisherReq req) {
        return promotionVideoService.publisherList(req);
    }

    @PostMapping("publisher/sales-list")
    @ApiOperation("销售发布者列表")
    public BaseResult<List<PromotionVideoPublisherResp>> publisherSalesList(@RequestHeader(USER_ID) @ApiIgnore Integer salesId,
                                                                            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus) {
        return promotionVideoService.publisherSalesList(salesId, auditStatus);
    }

    @PostMapping("publisher/create")
    @ApiOperation("创建发布者")
    public BaseResult<Void> createPublisher(@RequestHeader(USER_ID) @ApiIgnore Integer userId,
                                            @RequestBody PromotionVideoPublisherCreateReq req) {
        req.setCreatorId(userId);
        return contentClient.createPublisher(req);
    }

    @PostMapping("publisher/edit")
    @ApiOperation("编辑发布者")
    public BaseResult<Void> editPublisher(@RequestBody PromotionVideoPublisherEditReq req) {
        return contentClient.editPublisher(req);
    }

    @PostMapping("publisher/audit")
    @ApiOperation("发布者审核")
    public BaseResult<Void> auditPublisher(@RequestParam Long id,
                                           @RequestHeader(USER_ID) @ApiIgnore Integer userId,
                                           @RequestParam @ApiParam("审核状态") Integer auditStatus) {
        return contentClient.auditPublisher(id, userId, auditStatus);
    }

    @GetMapping("tag/list")
    @ApiOperation("营销视频标签列表")
    public PageResult<List<PromotionVideoTagResp>> videoTagList(
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size
    ) {
        return promotionVideoService.videoTagList(current, size);
    }

    @PostMapping("tag/create")
    @ApiOperation("创建营销视频标签")
    public BaseResult<Void> createVideoTag(@RequestHeader(USER_ID) @ApiIgnore Integer userId,
                                           @RequestBody PromotionVideoTagCreateReq req) {
        req.setCreatorId(userId);
        return contentClient.createVideoTag(req);
    }

    @PostMapping("tag/edit")
    @ApiOperation("编辑营销视频标签")
    public BaseResult<Void> editVideoTag(@RequestBody PromotionVideoTagEditReq req) {
        return contentClient.editVideoTag(req);
    }

    @PostMapping("tag/disable")
    @ApiOperation("作废营销视频标签")
    public BaseResult<Void> disableVideoTag(@RequestParam Long id) {
        return contentClient.disableVideoTag(id);
    }


}
