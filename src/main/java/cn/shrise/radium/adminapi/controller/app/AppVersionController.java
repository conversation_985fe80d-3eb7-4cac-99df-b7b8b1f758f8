package cn.shrise.radium.adminapi.controller.app;

import cn.shrise.radium.adminapi.service.app.AppVersionService;
import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.contentservice.entity.SsAppVersion;
import cn.shrise.radium.contentservice.req.AppVersionReq;
import cn.shrise.radium.contentservice.req.UpdateVersionReq;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api
@RestController
@RequestMapping("app/version")
@RequiredArgsConstructor
public class AppVersionController {

    private final AppVersionService appVersionService;

    @GetMapping("page")
    @ApiOperation("APP版本发布管理")
    public PageResult<List<SsAppVersion>> getAppVersionPage(
            @RequestParam @ApiParam("设备") Integer platform,
            @RequestParam(defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(defaultValue = "20") @ApiParam("每页条数") Integer size
    ) {
        return appVersionService.getAppVersionPage(platform, current, size);
    }

    @PostMapping("create")
    @ApiOperation("新增发布单")
    public BaseResult<Void> addAppVersion(
            @RequestBody AppVersionReq req
    ) {
        return appVersionService.addAppVersion(req);
    }

    @PostMapping("update")
    @ApiOperation("编辑发布单")
    public BaseResult<Void> updateAppVersion(
            @RequestBody UpdateVersionReq req
    ) {
        return appVersionService.updateAppVersion(req);
    }
}
