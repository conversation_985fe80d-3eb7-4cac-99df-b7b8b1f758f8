package cn.shrise.radium.caseprofittask.service;

import cn.hutool.core.util.ObjectUtil;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.entity.TdStrategyChannelExt;
import cn.shrise.radium.tradeservice.resp.StrategyStockCaseResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.shrise.radium.common.constant.CompanyTypeConstant.CT_GCTS;

@Service
@Slf4j
@RequiredArgsConstructor
public class CaseStockProfitRatioService {
    private final TradeClient tradeClient;

    public void start(ApplicationContext applicationContext) {
        log.info("开始处理案例股票入池收益胜率");
        //获取所有可用策略组信息
        List<TdStrategyChannelExt> strategyChannelExtList = tradeClient.getStrategyChannelListByEnabled(CT_GCTS.getValue(), true);
        if (ObjectUtil.isEmpty(strategyChannelExtList)) {
            SpringApplication.exit(applicationContext);
        }
        List<Long> channelIds = strategyChannelExtList.stream().map(TdStrategyChannelExt::getChannelId).collect(Collectors.toList());
        List<StrategyStockCaseResp> strategyProfitList = tradeClient.getStrategyStockCaseRespList(CT_GCTS.getValue(), channelIds);
        if (ObjectUtil.isEmpty(strategyProfitList)) {
            SpringApplication.exit(applicationContext);
        }
        Map<Long, Double> profitRatioMap = getProfitRatioMap(strategyProfitList);
        for (TdStrategyChannelExt tdStrategyChannelExt : strategyChannelExtList) {
            tdStrategyChannelExt.setProfitRatio(profitRatioMap.getOrDefault(tdStrategyChannelExt.getChannelId(), 0d));
            tdStrategyChannelExt.setProfitTime(Instant.now());
        }
        tradeClient.batchUpdateProfitRatio(BatchReq.of(strategyChannelExtList));
    }

    private Map<Long, Double> getProfitRatioMap(List<StrategyStockCaseResp> strategyProfitList) {
        Map<Long, Double> profitRatioMap = new HashMap<>();
        for (StrategyStockCaseResp strategyStockCaseResp : strategyProfitList) {
            if (!profitRatioMap.containsKey(strategyStockCaseResp.getChannelId())) {
                profitRatioMap.put(strategyStockCaseResp.getChannelId(), getProfitRatio(strategyProfitList));
            }
        }
        return profitRatioMap;
    }

    public Double getProfitRatio(List<StrategyStockCaseResp> strategyProfitList) {
        double count = 0;
        for (StrategyStockCaseResp strategyStockCaseResp : strategyProfitList) {
            if (ObjectUtil.isNotEmpty(strategyStockCaseResp.getChangeRatio()) && strategyStockCaseResp.getChangeRatio() > 0) {
                count++;
            }
        }
        return count / strategyProfitList.size();
    }
}
