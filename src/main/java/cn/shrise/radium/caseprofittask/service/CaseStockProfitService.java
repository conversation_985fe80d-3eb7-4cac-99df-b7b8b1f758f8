package cn.shrise.radium.caseprofittask.service;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.shrise.radium.common.exception.BusinessException;
import cn.shrise.radium.tradeservice.req.DividendInfo;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.quoteservice.QuoteClient;
import cn.shrise.radium.quoteservice.constant.KlineTypeEnum;
import cn.shrise.radium.quoteservice.entity.KlineBase;
import cn.shrise.radium.tradeservice.TradeClient;
import cn.shrise.radium.tradeservice.entity.TdCaseProfit;
import cn.shrise.radium.tradeservice.entity.TdStockCase;
import cn.shrise.radium.tradeservice.req.ProcessDayReq;
import cn.shrise.radium.tradeservice.req.UpdateProfitReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.Instant;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class CaseStockProfitService {

    private final TradeClient tradeClient;
    private final QuoteClient quoteClient;
    private final KLineService kLineService;
    private final CaseQuoteService caseQuoteService;


    public void processProfit16to180(ApplicationContext applicationContext) {
        log.info("processProfit16to180 start: {}", java.time.Instant.now());
        List<TdStockCase> tdStockCases = tradeClient.findCaseByProcessDays(null, 11, 180).getData();
        start(tdStockCases, applicationContext);
    }

    public void processProfit0to15(ApplicationContext applicationContext) {
        log.info("processProfit0to15 start: {}", java.time.Instant.now());
        List<TdStockCase> tdStockCases = tradeClient.findCaseByProcessDays(null, null, 10).orElse(new ArrayList<>());
        tdStockCases.addAll(getRrefreshCaseList());
        start(tdStockCases, applicationContext);
        // 前后都检查是否有需要刷新的
        start(getRrefreshCaseList(), applicationContext);
    }

    private List<TdStockCase> getRrefreshCaseList() {
        List<TdStockCase> refreshCaseList = tradeClient.getProfitRefreshCaseList().orElse(new ArrayList<>());
        log.info("getRrefreshCaseList refreshCaseList: {}", JSONUtil.toJsonStr(refreshCaseList));
        return refreshCaseList;
    }

    public void start(List<TdStockCase> tdStockCases, ApplicationContext applicationContext) {
        if (ObjectUtil.isEmpty(tdStockCases)) {
            //SpringApplication.exit(applicationContext);
            return;
        }
        long millis = Instant.now().getMillis();
        java.time.Instant now = java.time.Instant.ofEpochMilli(millis);
        java.time.Instant dayOfStart = DateUtils.getDayOfStart(now);
        log.info("开始处理案例股票入池收益，dayOfStart：{}", now);
        int totalCount = tdStockCases.size();
        int failCount = 0;
        List<String> errorMsgList = new ArrayList<>();
        for (TdStockCase stockCase : tdStockCases) {
            if (ObjectUtil.isAllEmpty(stockCase.getPrice()) || ObjectUtil.isAllEmpty(stockCase.getDealTime())) {
                continue;
            }
            TdCaseProfit profit = tradeClient.findCaseProfitByCaseId(stockCase.getId()).getData();
            if (ObjectUtil.isEmpty(profit)) {
                // 未找到收益记录
                continue;
            }
            Integer processDays = profit.getProcessDays();
            java.time.Instant dealTime = stockCase.getDealTime();
            if (processDays > 1 || dayOfStart.isAfter(dealTime)) {
                log.info("计算日K行情，caseId:{},profitId:{},processDays:{},当前行情时间:{},当前行情收益:{}", stockCase.getId(), profit.getId(), processDays, profit.getQuoteTime(), JSONUtil.toJsonStr(profit));
                // 处理时间为买入时间第二天及以后
                // 取日K
                List<KlineBase> klineBases;
                try {
                    klineBases = caseQuoteService.getKlineList(stockCase, KlineTypeEnum.DAY, profit.getQuoteTime());
                } catch (BusinessException ex) {
                    errorMsgList.add(ex.getMessage());
                    failCount++;
                    continue;
                }
                if (ObjectUtil.isEmpty(klineBases)) {
                    // 不存在最新日K
                    continue;
                }
                List<KlineBase> klineBaseList = klineBases.stream()
                        .sorted(Comparator.comparing(KlineBase::getQuoteTime, Comparator.naturalOrder()))
                        .filter(k -> k.getQuoteTime().compareTo(profit.getQuoteTime()) >= 0)
                        .collect(Collectors.toList());
                if (ObjectUtil.isEmpty(klineBaseList)) {
                    continue;
                }
                // 获取除权数据
                List<DividendInfo> dividendInfo;
                try {
                    dividendInfo = caseQuoteService.getDividendInfo(stockCase.getLabelCode(), stockCase.getDealTime());
                } catch (BusinessException businessException) {
                    errorMsgList.add(businessException.getMessage());
                    failCount++;
                    continue;
                }
                // 获取计算行情所需参数
                ProcessDayReq build = ProcessDayReq.builder()
                        .klineBaseList(klineBaseList)
                        .dividendInfo(dividendInfo)
                        .profit(profit)
                        .stockCase(stockCase)
                        .build();
                // 计算日K行情
                tradeClient.processDay(build);
            } else {
                log.info("计算分钟K行情，caseId:{},profitId:{},当前行情时间:{}", stockCase.getId(), profit.getId(), profit.getQuoteTime());
                // 处理时间为买入时间当天，取分钟K
                List<KlineBase> klineBases;
                try {
                    klineBases = caseQuoteService.getKlineList(stockCase, KlineTypeEnum.MIN, profit.getQuoteTime());
                } catch (BusinessException ex) {
                    errorMsgList.add(ex.getMessage());
                    failCount++;
                    continue;
                }
                // 筛选大于买入时间，且最高价的一条分钟K
                Optional<KlineBase> first = klineBases.stream()
                        .sorted(Comparator.comparing(KlineBase::getHigh, Comparator.reverseOrder()))
                        .filter(k -> k.getQuoteTime().compareTo(stockCase.getDealTime()) >= 0)
                        .findFirst();
                // 筛选大于买入时间，且最低价的一条分钟K
                Optional<KlineBase> lowBasesOptional = klineBases.stream()
                        .sorted(Comparator.comparing(KlineBase::getLow))
                        .filter(k -> k.getQuoteTime().compareTo(stockCase.getDealTime()) >= 0)
                        .findFirst();
                klineBases = klineBases.stream()
                        .sorted(Comparator.comparing(KlineBase::getQuoteTime, Comparator.reverseOrder()))
                        .filter(k -> k.getQuoteTime().compareTo(stockCase.getDealTime()) >= 0)
                        .collect(Collectors.toList());
                Double minPrice = profit.getMin9Price();
                java.time.Instant minTime = profit.getMin9Time();
                Double minRatio = profit.getMin9Ratio();
                if (lowBasesOptional.isPresent()) {
                    KlineBase lowBase = lowBasesOptional.get();
                    minPrice = lowBase.getLow();
                    minTime = lowBase.getEndTime();
                    minRatio = (lowBase.getLow() - stockCase.getPrice()) / stockCase.getPrice();
                }
                log.info("caseId:{},profitId:{},klineBases:{}", stockCase.getId(), profit.getId(), JSONUtil.toJsonStr(klineBases));
                if (first.isPresent()) {
                    KlineBase klineBase = first.get();
                    Double maxRatio = (klineBase.getHigh() - stockCase.getPrice()) / stockCase.getPrice();
                    Double changeRatio = (klineBases.get(0).getClose() - stockCase.getPrice()) / stockCase.getPrice();
                    UpdateProfitReq updateProfitReq = UpdateProfitReq.builder()
                            .maxPrice(klineBase.getHigh())
                            .maxTime(klineBase.getEndTime())
                            .maxRatio(maxRatio)
                            .caseDay(processDays)
                            .profit(profit)
                            .quoteTime(klineBases.get(0).getQuoteTime())
                            .changeRatio(changeRatio)
                            .closed(stockCase.getIsClosed())
                            .minPrice(minPrice)
                            .minTime(minTime)
                            .minRatio(minRatio)
                            .build();
                    tradeClient.updateProfit(updateProfitReq);
                } else if (!klineBases.isEmpty()) {
                    UpdateProfitReq updateProfitReq = UpdateProfitReq.builder()
                            .maxPrice(profit.getMax7Price())
                            .maxTime(profit.getMax7Time())
                            .maxRatio(profit.getMax7Ratio())
                            .caseDay(processDays)
                            .profit(profit)
                            .quoteTime(klineBases.get(0).getQuoteTime())
                            .changeRatio(profit.getChangeRatio())
                            .closed(stockCase.getIsClosed())
                            .minPrice(minPrice)
                            .minTime(minTime)
                            .minRatio(minRatio)
                            .build();
                    tradeClient.updateProfit(updateProfitReq);
                }
            }
        }
        log.info("结束处理案例股票入池收益 end: {}, totalCount:{}, successCount:{}, failCount:{}, errorMsgList:{}", java.time.Instant.now(), totalCount, totalCount - failCount, failCount, errorMsgList);
    }
}
