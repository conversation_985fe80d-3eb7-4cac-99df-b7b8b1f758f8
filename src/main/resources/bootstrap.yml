server:
  port: 8203
  max-http-header-size: 1024000
spring:
  redis:
    database: 15
  application:
    name: case-profit-task
  profiles:
    active: ${profiles-active:dev1}
  cloud:
    nacos:
      config:
        file-extension: yaml
        extension-configs:
          - data-id: basement-setting.yaml
            group: DEFAULT_GROUP
            refresh: true
      discovery:
        cluster-name: shanghai

---
spring:
  config:
    activate:
      on-profile: dev0
  cloud:
    nacos:
      config:
        namespace: efc94dcf-106f-468a-9b28-7a9c4e8ee5ab
      discovery:
        namespace: ${nacos-discovery-namespace:efc94dcf-106f-468a-9b28-7a9c4e8ee5ab}
      # 公网
      server-addr: mse-4e1f18f0-p.nacos-ans.mse.aliyuncs.com:8848


---
spring:
  config:
    activate:
      on-profile: dev1
  cloud:
    nacos:
      config:
        namespace: 948cb756-ca3b-430d-816f-aaf8b78fa4b5
      discovery:
        namespace: ${nacos-discovery-namespace:948cb756-ca3b-430d-816f-aaf8b78fa4b5}
      # 公网
      server-addr: mse-4e1f18f0-p.nacos-ans.mse.aliyuncs.com:8848


---
spring:
  config:
    activate:
      on-profile: dev2
  cloud:
    nacos:
      config:
        namespace: e887c086-3c1b-4d0f-b77d-a4c16ffc28f3
      discovery:
        namespace: ${nacos-discovery-namespace:e887c086-3c1b-4d0f-b77d-a4c16ffc28f3}
      # 公网
      server-addr: mse-4e1f18f0-p.nacos-ans.mse.aliyuncs.com:8848


---
spring:
  config:
    activate:
      on-profile: dev3
  cloud:
    nacos:
      config:
        namespace: d7a951a3-7d79-4a2a-89ae-fc8f9e4a84f4
      discovery:
        namespace: ${nacos-discovery-namespace:d7a951a3-7d79-4a2a-89ae-fc8f9e4a84f4}
      # 公网
      server-addr: mse-4e1f18f0-p.nacos-ans.mse.aliyuncs.com:8848


---
spring:
  config:
    activate:
      on-profile: dev4
  cloud:
    nacos:
      config:
        namespace: 410368f2-ae37-47cf-9bb1-bf31019361fd
      discovery:
        namespace: ${nacos-discovery-namespace:410368f2-ae37-47cf-9bb1-bf31019361fd}
      # 公网
      server-addr: mse-4e1f18f0-p.nacos-ans.mse.aliyuncs.com:8848


---
spring:
  config:
    activate:
      on-profile: beta0
  cloud:
    nacos:
      config:
        namespace: 4f93443d-b992-4d28-9e5a-0844d8bb219a
      discovery:
        namespace: 4f93443d-b992-4d28-9e5a-0844d8bb219a
      # 内网
      server-addr: mse-4e1f18f0-nacos-ans.mse.aliyuncs.com:8848


---
spring:
  config:
    activate:
      on-profile: beta1
  cloud:
    nacos:
      config:
        namespace: bda957b9-5eeb-4ede-9b31-3546522b5c63
      discovery:
        namespace: bda957b9-5eeb-4ede-9b31-3546522b5c63
      # 内网
      server-addr: mse-4e1f18f0-nacos-ans.mse.aliyuncs.com:8848


---
spring:
  config:
    activate:
      on-profile: beta2
  cloud:
    nacos:
      config:
        namespace: 0f809775-8b4d-43ef-aee5-3fbf7224be0b
      discovery:
        namespace: 0f809775-8b4d-43ef-aee5-3fbf7224be0b
      # 内网
      server-addr: mse-4e1f18f0-nacos-ans.mse.aliyuncs.com:8848


---
spring:
  config:
    activate:
      on-profile: beta3
  cloud:
    nacos:
      config:
        namespace: 9d3828bc-fb23-4016-9ccb-c87fadbe07b4
      discovery:
        namespace: 9d3828bc-fb23-4016-9ccb-c87fadbe07b4
      # 内网
      server-addr: mse-4e1f18f0-nacos-ans.mse.aliyuncs.com:8848


---
spring:
  config:
    activate:
      on-profile: beta4
  cloud:
    nacos:
      config:
        namespace: 4551022e-1826-4b2a-a864-6c84e4c209c0
      discovery:
        namespace: 4551022e-1826-4b2a-a864-6c84e4c209c0
      # 内网
      server-addr: mse-4e1f18f0-nacos-ans.mse.aliyuncs.com:8848


---
spring:
  config:
    activate:
      on-profile: prod
  cloud:
    nacos:
      config:
        namespace: 2a304dd5-1b4f-4c89-b982-21db197b9cd9
      discovery:
        namespace: 2a304dd5-1b4f-4c89-b982-21db197b9cd9
      # 内网
      server-addr: mse-3c55d140-nacos-ans.mse.aliyuncs.com:8848