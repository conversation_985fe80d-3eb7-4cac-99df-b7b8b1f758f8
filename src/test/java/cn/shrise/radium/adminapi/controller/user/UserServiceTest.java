package cn.shrise.radium.adminapi.controller.user;

import cn.shrise.radium.adminapi.entity.User;
import cn.shrise.radium.authservice.entity.AccessToken;
import cn.shrise.radium.authservice.entity.RefreshToken;
import cn.shrise.radium.authservice.resp.AdminLoginResp;
import cn.shrise.radium.authservice.resp.AdminVerifyResp;
import cn.shrise.radium.userservice.entity.UcUsers;
import cn.shrise.radium.userservice.resp.UserBaseInfoResp;
import org.junit.jupiter.api.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class UserServiceTest {

    @Test
    public void test1() {
        AdminVerifyResp.UserInfo userInfo = new AdminVerifyResp.UserInfo();
        userInfo.setId(1);
        userInfo.setUserName("H.");
        userInfo.setNickName("H.");
        UcUsers user = new UcUsers();
        BeanUtils.copyProperties(userInfo, user);
        System.out.println(userInfo);
        System.out.println(user);
    }

    @Test
    public void test2() {
        AdminLoginResp loginResp = AdminLoginResp.builder()
                .accessToken(new AccessToken())
                .refreshToken(new RefreshToken())
                .user(new AdminLoginResp.UserInfo(1, 1, 45, "张三", null, "张三", "true", "张三", "张三", "张三", "张三", "张三", true, null, 1, null, true, 1))
                .staff(new AdminLoginResp.StaffInfo(1))
                .build();

        AdminLoginResp.StaffInfo staffInfo = loginResp.getStaff();

        AccessToken accessToken = loginResp.getAccessToken();
        AdminLoginResp.UserInfo userInfo = loginResp.getUser();
        User user = new User();
        BeanUtils.copyProperties(userInfo, user);
        System.out.println(userInfo+"and"+staffInfo);
        System.out.println(user);
    }

    @Test
    public void test3() {
        AdminLoginResp loginResp = AdminLoginResp.builder()
                .accessToken(new AccessToken())
                .refreshToken(new RefreshToken())
                .user(new AdminLoginResp.UserInfo(1, 1, 45, "张三", null, "张三", "true", "张三", "张三", "张三", "张三", "张三", true, null, 1, null, true, 1))
                .staff(new AdminLoginResp.StaffInfo(1))
                .build();

        AdminLoginResp.StaffInfo staffInfo = loginResp.getStaff();

        AccessToken accessToken = loginResp.getAccessToken();
        AdminLoginResp.UserInfo userInfo = loginResp.getUser();
        UserBaseInfoResp user = new UserBaseInfoResp();
        BeanUtils.copyProperties(userInfo, user);
        System.out.println("复制前用户"+userInfo);
        System.out.println("复制前员工"+staffInfo);
        System.out.println("复制后用户"+user);
    }





}
