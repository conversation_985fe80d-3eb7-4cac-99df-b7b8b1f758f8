package cn.shrise.radium.tradeservice.resp;

import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StrategyChannelResp {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("编号")
    private String number;

    @ApiModelProperty("频道名称")
    private String channelName;

    @ApiModelProperty("展示名称")
    private String showName;

    @ApiModelProperty("状态")
    private Boolean enabled;

    @ApiModelProperty("股池频道id")
    private Long channelId;

    @ApiModelProperty("近半年胜率显示")
    private Boolean isShowProfit;

    @ApiModelProperty("标签")
    private JSONArray tags;

    @ApiModelProperty("简介")
    private String description;

    @ApiModelProperty("胜率")
    private Double profitRatio;

    @ApiModelProperty("胜率更新时间")
    private Instant profitTime;

    @ApiModelProperty("老师信息")
    private SsAnalystInfo analystInfo;

    @ApiModelProperty("策略组最近更新时间")
    private Instant lastUpdateTime;

}
