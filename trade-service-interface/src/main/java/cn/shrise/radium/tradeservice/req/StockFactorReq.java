package cn.shrise.radium.tradeservice.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @Author: tang<PERSON><PERSON><PERSON>
 * @Date: 2025/4/2 10:39
 * @Desc:
 **/
@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class StockFactorReq {

    @NotNull(message = "类型不能为空")
    @ApiModelProperty("类型")
    private Integer type;

    @NotNull(message = "编号不能为空")
    @ApiModelProperty("编号")
    private String number;

    @NotNull(message = "名称不能为空")
    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("备注")
    private String remark;

}
