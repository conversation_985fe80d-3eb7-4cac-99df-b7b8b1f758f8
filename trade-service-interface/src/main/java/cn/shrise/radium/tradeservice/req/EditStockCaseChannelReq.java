package cn.shrise.radium.tradeservice.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class EditStockCaseChannelReq {

    @ApiModelProperty(value = "频道Id")
    private Long channelId;

    @ApiModelProperty(value = "频道类型", required = true)
    private Integer channelType;

    @ApiModelProperty(value = "频道编号", required = true)
    private String number;

    @ApiModelProperty(value = "频道名称", required = true)
    private String name;

    @ApiModelProperty(value = "关联直播室Id")
    private Long roomId;

    @ApiModelProperty(value = "关联栏目Id")
    private Integer seriesId;

    @ApiModelProperty(value = "关联聊天室Id")
    private Long chatId;

    @ApiModelProperty(value = "公司类型", hidden = true)
    private Integer companyType;

    @ApiModelProperty(value = "员工id", hidden = true)
    private Integer userId;

    @ApiModelProperty(value = "是否APP推送")
    private Boolean appPush;

    @ApiModelProperty(value = "频道备注")
    private String remark;

    @ApiModelProperty(value = "是否决策小组审核")
    private Boolean needDecision;

    @ApiModelProperty(value = "股票池频道解盘股票跟踪前端是否隐藏")
    private Boolean isFollowHide;

}
