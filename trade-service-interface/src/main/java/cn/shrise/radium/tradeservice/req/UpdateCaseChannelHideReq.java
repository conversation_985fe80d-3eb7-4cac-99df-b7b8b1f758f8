package cn.shrise.radium.tradeservice.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class UpdateCaseChannelHideReq {

    @ApiModelProperty(value = "案例频道id")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "股票池频道解盘股票跟踪前端是否隐藏")
    @NotNull
    private Boolean isFollowHide;

    @ApiModelProperty(value = "员工id", hidden = true)
    private Integer operatorId;
}
