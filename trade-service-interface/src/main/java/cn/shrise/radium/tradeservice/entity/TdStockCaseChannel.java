package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Entity
@Table(name = "td_stock_case_channel", indexes = {
        @Index(name = "room_id_ibfk1_idx", columnList = "room_id")
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdStockCaseChannel {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "channel_type", nullable = false)
    private Integer channelType;

    @Column(name = "number", nullable = false, length = 128)
    private String number;

    @Column(name = "name", nullable = false, length = 64)
    private String name;

    @Column(name = "room_id")
    private Long roomId;

    @Column(name = "series_id")
    private Integer seriesId;

    @Column(name = "gmt_create", nullable = false, insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", nullable = false, insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "company_type", nullable = false)
    private Integer companyType;

    @Column(name = "is_enabled", nullable = false)
    private Boolean isEnabled;

    @Column(name = "chat_id")
    private Long chatId;

    @Column(name = "remark")
    private String remark;

    @Column(name = "need_decision")
    private Boolean needDecision;

    @Column(name = "is_follow_hide")
    private Boolean isFollowHide;
}