package cn.shrise.radium.tradeservice.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SymbolDetailReq {
    /**
     * 返回类型
     */
    private int reqtype;
    /**
     * 请求 ID
     */
    private int reqid;
    /**
     * 会话标识
     */
    private String session;
    /**
     * 数据列表
     */
    private List<Stock> data;

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Stock {
        /**
         * 市场代码
         */
        private int market;
        /**
         * 证券代码
         */
        private String code;
        /**
         * 标识标志
         */
        private int symflag;
    }
}
