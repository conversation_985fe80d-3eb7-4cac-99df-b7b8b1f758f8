package cn.shrise.radium.tradeservice.req;

import cn.shrise.radium.quoteservice.entity.KlineBase;
import cn.shrise.radium.tradeservice.dto.CasePortfolioDto;
import cn.shrise.radium.tradeservice.entity.TdCaseProfit;
import cn.shrise.radium.tradeservice.entity.TdStockCase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
@Builder
public class ProcessDayReq {

    @ApiModelProperty(value = "待处理的日K列表")
    private List<KlineBase> klineBaseList;
    @ApiModelProperty(value = "累计除权信息列表")
    private List<CasePortfolioFactorReq> factorList;
    @ApiModelProperty(value = "正在处理的收益信息")
    private TdCaseProfit profit;
    @ApiModelProperty(value = "正在处理的案例")
    private TdStockCase stockCase;
    @ApiModelProperty(value = "计算收益所需参数对象")
    private CasePortfolioDto portfolioParameter;

    List<DividendInfo> dividendInfo;

}
