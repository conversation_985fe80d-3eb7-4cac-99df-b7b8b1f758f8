package cn.shrise.radium.tradeservice;

import cn.shrise.radium.common.base.BaseResult;
import cn.shrise.radium.common.base.PageResult;
import cn.shrise.radium.common.constant.ServiceConstant;
import cn.shrise.radium.common.entity.ServiceIntroduction;
import cn.shrise.radium.common.req.BatchReq;
import cn.shrise.radium.common.util.DateUtils;
import cn.shrise.radium.tradeservice.constant.TradeErrorCode;
import cn.shrise.radium.tradeservice.dto.*;
import cn.shrise.radium.tradeservice.entity.*;
import cn.shrise.radium.tradeservice.req.*;
import cn.shrise.radium.tradeservice.resp.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static cn.shrise.radium.common.base.Pagination.DEFAULT_CURRENT;
import static cn.shrise.radium.common.base.Pagination.DEFAULT_SIZE;
import static cn.shrise.radium.common.constant.HeaderConstant.USER_ID;

/**
 * <AUTHOR>
 */
@FeignClient(name = ServiceConstant.TRADE_SERVICE)
public interface TradeClient {

    /**
     * 根据用户ID获取绑定的频道列表
     *
     * @param userId      用户i的
     * @param channelType 频道类型
     * @return 频道列表
     */
    @GetMapping("channel/bindList")
    BaseResult<List<TdPortfolioChannel>> getBindChannelList(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("频道类型") Integer channelType);

    /**
     * 根据用户ID获取未绑定的频道列表
     *
     * @param userId      用户id
     * @param channelType 频道类型
     * @return 频道列表
     */
    @GetMapping("channel/unbindList")
    BaseResult<List<TdPortfolioChannel>> getUnbindChannelList(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("频道类型") Integer channelType);

    /**
     * 用户绑定频道
     *
     * @param userId     用户id
     * @param channelId  频道id
     * @param operatorId 操作人id
     * @return 操作结果
     */
    @PostMapping("channel/bind")
    @ApiOperation("用户绑定频道")
    BaseResult<Boolean> bindChannel(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("頻道id") Integer channelId,
            @RequestParam @ApiParam("操作人id") Integer operatorId);

    /**
     * 用户解绑频道
     *
     * @param userId     用户id
     * @param channelId  频道id
     * @param operatorId 操作人id
     * @return 操作结果
     */
    @PostMapping("channel/unbind")
    @ApiOperation("用户解绑频道")
    BaseResult<Boolean> unbindChannel(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("频道id") Integer channelId,
            @RequestParam @ApiParam("操作人id") Integer operatorId);

    @PostMapping("channel/introduction")
    @ApiOperation("获取实战跟投服务简介列表")
    BaseResult<List<ServiceIntroduction>> getPortfolioServiceIntroductionList(
            @RequestParam Integer companyType,
            @RequestBody @Valid BatchReq<String> req);

    @PostMapping("notice/create_sub_info")
    @ApiOperation("创建通知订阅消息")
    BaseResult<TradeErrorCode> createOrUpdateSubInfo(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer customerId,
            @RequestParam @ApiParam("通知类型") List<Integer> noticeTypeList,
            @RequestParam @ApiParam("状态") Boolean enabled);

    @PostMapping("notice/create_sub_info_record")
    @ApiOperation("创建通知订阅消息和记录")
    BaseResult<TradeErrorCode> createSubInfo(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("用户id") Integer customerId,
            @RequestParam @ApiParam("操作人id") Integer operatorId,
            @RequestBody @ApiParam("通知列表") List<CreateSubReq> noticeList);

    @GetMapping("notice/getSubList")
    @ApiOperation("创建通知订阅消息和记录")
    BaseResult<List<TdNoticeSubInfo>> getSubList(
            @RequestParam @ApiParam("用户id") Integer customerId,
            @RequestParam(required = false) @ApiParam("是否订阅") Boolean isSub);


    @GetMapping("case_channel/list")
    @ApiOperation("获取案例频道列表")
    PageResult<List<TdStockCaseChannel>> getStockCaseChannelList(
            @RequestParam(required = false) @ApiParam("频道Id列表") List<Long> Ids,
            @RequestParam(required = false) @ApiParam("频道类型") Integer channelType,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("搜索") String searchContent,
            @RequestParam @ApiParam("页码") Integer current,
            @RequestParam @ApiParam("分页数量") Integer size);

    @GetMapping("case_channel/list/all")
    @ApiOperation("获取全部案例频道列表")
    BaseResult<List<TdStockCaseChannel>> getStockCaseChannelList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("频道Id列表") List<Long> channelIds,
            @RequestParam(required = false) @ApiParam("频道类型") Integer channelType,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enabled);

    @GetMapping("case_channel/all-filter")
    @ApiOperation("获取全部案例频道列表")
    BaseResult<List<TdStockCaseChannel>> getAllCaseChannelList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("频道类型") List<Integer> channelTypes,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean enabled);

    @PostMapping("case_channel/createOrUpdate")
    @ApiOperation("创建/编辑案例频道")
    BaseResult<TradeErrorCode> createOrUpdateCaseChannel(@Validated @RequestBody EditStockCaseChannelReq req);

    @PostMapping("case_channel/addAnalyst")
    @ApiOperation("为频道添加投顾老师")
    BaseResult<TradeErrorCode> addAnalyst(@Validated @RequestBody CaseChannelAnalystReq req);

    @PostMapping("case_channel/deleteAnalystRelation")
    @ApiOperation("删除投顾老师关系")
    BaseResult<TradeErrorCode> deleteAnalystRelation(
            @RequestParam @ApiParam("需要删除的关系id") List<Long> Ids);

    @GetMapping("case_channel/getChannelAnalystRelation")
    @ApiOperation("获取案例频道对映老师关系")
    BaseResult<List<TdCaseChannelAnalystRelation>> getCaseChannelAnalystRelations(
            @RequestParam @ApiParam("频道ID") Long channelId);

    @PostMapping("case_channel/addManager")
    @ApiOperation("为频道配置处理人")
    BaseResult<TradeErrorCode> addManager(@Validated @RequestBody CaseChannelManagerReq req);

    @PostMapping("case_channel/setManager")
    @ApiOperation("为频道配置处理人")
    BaseResult<TradeErrorCode> setManager(@Validated @RequestBody CaseChannelManagerReq req);

    @GetMapping("case_channel/getChannelManagerRelation")
    @ApiOperation("获取案例频道对应处理人关系")
    BaseResult<List<TdCaseChannelManagerRelation>> getCaseChannelManagerRelations(
            @RequestParam(required = false) @ApiParam("处理人Id") Integer managerId,
            @RequestParam(required = false) @ApiParam("频道ID") Long channelId);

    @GetMapping("case_channel/getChannelDepartmentRelation")
    @ApiOperation("获取案例频道对应部门关系")
    BaseResult<List<TdCaseChannelDepartmentRelation>> getChannelDepartmentRelation(
            @RequestParam(required = false) @ApiParam("部门Id") Integer departmentId,
            @RequestParam(required = false) @ApiParam("频道ID") Long channelId);

    @PostMapping("case_channel/deleteManagerRelation")
    @ApiOperation("删除频道处理人关系")
    BaseResult<TradeErrorCode> deleteManagerRelation(
            @RequestParam @ApiParam("需要删除的关系id") List<Long> Ids);

    @GetMapping("case_channel/filter")
    @ApiOperation("获取案例频道")
    BaseResult<TdStockCaseChannel> getStockCaseChannelByFilter(
            @RequestParam(required = false) @ApiParam("频道Id") Long id,
            @RequestParam(required = false) @ApiParam("频道编号") String number,
            @RequestParam(required = false) @ApiParam("聊天室Id") Long chatId);

    @GetMapping("case_channel/by-seriesId")
    @ApiOperation("获取案例频道")
    BaseResult<List<TdStockCaseChannel>> getStockCaseChannelBySeriesId(
            @RequestParam @ApiParam("系列id") Integer seriesId);

    @GetMapping("stock/case")
    @ApiOperation("案例股票池-案例列表")
    PageResult<List<StockCaseDto>> getStockCaseDetailList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("频道") Long channelId,
            @RequestParam(required = false) @ApiParam("频道Ids") Collection<Long> channelIdList,
            @RequestParam(required = false) @ApiParam("筛选") String searchText,
            @RequestParam(required = false) @ApiParam("时间类型") Integer timeType,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) @ApiParam("正序排列") Boolean isAsc,
            @RequestParam(required = false) @ApiParam("是否清仓") Boolean isClosed,
            @RequestParam(required = false) @ApiParam("是否置顶") Boolean isTop,
            @RequestParam(required = false) @ApiParam("是否置顶排序") Boolean isTopSort,
            @RequestParam(required = false) @ApiParam("案例来源") Integer sourceType,
            @RequestParam(required = false) @ApiParam("涨幅类型") Integer ratioType,
            @RequestParam(required = false) @ApiParam("审核状态") List<Integer> auditStatusList,
            @RequestParam(required = false) @ApiParam("顾问老师id") Integer analystId,
            @RequestParam(required = false) @ApiParam("是否调研票") Boolean isResearch,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size);

    @PostMapping("stock/case")
    @ApiOperation("新增案例")
    BaseResult<String> addStockCase(@Validated @RequestBody EditStockCaseReq req);

    @PostMapping("stock/case/deal")
    @ApiOperation("新增案例操作")
    BaseResult<TdStockCaseDeal> addStockCaseDeal(@Validated @RequestBody EditStockCaseDealReq req);

    @GetMapping("stock/case/deal_history")
    @ApiOperation("案例股票池-案例操作历史")
    PageResult<List<StockCaseDealDto>> getStockCaseDealList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("频道") Long channelId,
            @RequestParam(required = false) @ApiParam("案例Id") Long caseId,
            @RequestParam(required = false) @ApiParam("股票代码") String labelCode,
            @RequestParam(required = false) @ApiParam("是否已审核") Boolean isAudit,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("投资顾问") Integer analystId,
            @RequestParam(required = false) @ApiParam("排序类型") Integer orderType,
            @RequestParam(required = false) @ApiParam("是否升序") Boolean isAsc,
            @RequestParam(required = false) @ApiParam("是否调研票") Boolean isResearch,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size);

    @GetMapping("notice/sub/record/list")
    @ApiOperation("获取用户订阅记录列表")
    PageResult<List<TdNoticeSubRecord>> getSubRecordList(
            @RequestParam Integer companyType,
            @RequestParam(required = false) @ApiParam("开始时间") Instant startTime,
            @RequestParam(required = false) @ApiParam("结束时间") Instant endTime,
            @RequestParam(required = false) @ApiParam("是否订阅") Boolean isSub,
            @RequestParam(required = false) @ApiParam("通知类型") Integer noticeType,
            @RequestParam(required = false) @ApiParam("客户id") Integer customerId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);


    @GetMapping("recommend/stock")
    @ApiOperation("A股情报-股票池列表")
    PageResult<List<RecommendStockDto>> getRecommendStockList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("频道") Integer typeId,
            @RequestParam(required = false) @ApiParam("榜单") Boolean isTop,
            @RequestParam(required = false) @ApiParam("筛选") String searchText,
            @RequestParam(required = false) @ApiParam("排序类型 1 创建时间 2 编辑时间 3 买入时间 4 成交时间 5 最高涨幅") Integer orderType,
            @RequestParam(required = false) @ApiParam("时间类型 1 编辑时间  2 买入时间") Integer timeType,
            @RequestParam(required = false) @ApiParam("创建人ids") List<Integer> creatorIds,
            @RequestParam(required = false) @ApiParam("投顾老师id") Integer analystId,
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) @ApiParam("是否逆序") Boolean isDesc,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size);

    @GetMapping("recommend/stock/audit")
    @ApiOperation("A股情报-股票池审核列表")
    PageResult<List<RecommendStockDto>> getRecommendStockAuditList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("是否审核") Boolean isAudit,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size);

    @GetMapping("recommend/stock/modify/list")
    @ApiOperation("A股情报-股票池修改记录列表")
    PageResult<List<TdRecommendStockModifyRecord>> getRecommendStockModifyRecordPage(
            @RequestParam @ApiParam("股票id") Integer stockId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "10") @ApiParam("每页条数") Integer size);

    @PostMapping("recommend/stock")
    @ApiOperation("A股情报-股票池 新增股票")
    BaseResult<String> addRecommendStock(@Validated @RequestBody EditRecommendStockReq req);

    @PostMapping("recommend/stock/{stockId}")
    @ApiOperation("A股情报-股票池 修改股票")
    BaseResult<String> updateRecommendStock(
            @RequestParam @ApiParam("用户id") Integer userId,
            @PathVariable Integer stockId,
            @Validated @RequestBody EditRecommendStockReq req);

    @DeleteMapping("recommend/stock/{stockId}")
    @ApiOperation("A股情报-股票池 删除股票")
    BaseResult<String> deleteRecommendStock(
            @RequestParam @ApiParam("用户id") Integer userId,
            @PathVariable Integer stockId);

    @PostMapping("recommend/stock/audit/{stockId}")
    @ApiOperation("A股情报-股票池 审核股票")
    BaseResult<String> auditRecommendStock(
            @RequestParam @ApiParam("用户id") Integer userId,
            @PathVariable Integer stockId,
            @RequestParam @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("审核内容") String auditRemark);

    @PostMapping("recommend/stock/audit/decision")
    @ApiOperation("A股情报-股票池 决策小组审核")
    BaseResult<Void> auditRecommendStockByDecision(
            @RequestParam @ApiParam("案例id") Integer stockId,
            @RequestParam @ApiParam("审核人") Integer auditorId,
            @RequestParam @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("拒绝原因") String rejectReason);


    @PutMapping("recommend/stock/{stockId}")
    @ApiOperation("A股情报-股票池 置顶")
    BaseResult<String> updateRecommendStockTopStatus(@PathVariable Integer stockId,
                                                     @RequestParam @ApiParam("榜单") Boolean isTop);

    /**
     * 根据股票id获取股票详情
     *
     * @param stockId     股票id
     * @param companyType 公司类型
     * @return 股票详情
     */
    @GetMapping("recommend/stock/stockDetail")
    @ApiOperation("股票详情")
    BaseResult<StockDetailDto> getStockDetail(
            @RequestParam @ApiParam("股票id") Integer stockId,
            @RequestParam @ApiParam("公司类型") Integer companyType);

    @GetMapping("recommend/stock-type/list")
    @ApiOperation("A股情报-频道管理列表")
    PageResult<List<TdRecommendStockType>> getRecommendStockTypeList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("频道") Collection<Integer> ids,
            @RequestParam(required = false) @ApiParam("章栏目ID") Collection<Integer> seriesIds,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size);

    @PostMapping("recommend/stock-type/createOrUpdate")
    @ApiOperation("A股情报-创建/编辑股票池频道")
    BaseResult<TradeErrorCode> createOrUpdateRecommendStockType(@Validated @RequestBody EditRecommendStockTypeReq req);

    @PostMapping("recommend/stock-type/introduction")
    @ApiOperation("获取股票池服务简介列表")
    BaseResult<List<ServiceIntroduction>> getRecommendStockServiceIntroductionList(
            @RequestParam Integer companyType,
            @RequestBody @Valid BatchReq<String> req);

    /**
     * 获取上榜股票信息
     *
     * @param companyType 公司类型
     * @param seriesId    文章栏目id
     * @param poolNumber  股票池number
     * @param current     页码
     * @param size        分页数量
     * @return 股票信息
     */
    @GetMapping("recommend/stock/rank")
    @ApiOperation("上榜股票")
    PageResult<List<StockInfoDto>> getRankStock(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("文章栏目id") Integer seriesId,
            @RequestParam(required = false) @ApiParam("股票池number") String poolNumber,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    /**
     * 获取一档栏目股票
     *
     * @param companyType 公司类型
     * @param seriesId    文章栏目id
     * @param number      频道number
     * @param showDay     展示天数
     * @param openTime    开通时间
     * @param buyTime     入池时间
     * @return 栏目股票信息
     */
    @GetMapping("recommend/stock/column")
    @ApiOperation("一档栏目股票")
    BaseResult<List<StockInfoDto>> getColumnStock(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("文章栏目id") Integer seriesId,
            @RequestParam(required = false) @ApiParam("频道栏目number") String number,
            @RequestParam(required = false) @ApiParam("展示天数") Integer showDay,
            @RequestParam @ApiParam("开通时间") Instant openTime,
            @RequestParam(required = false) @ApiParam("入池时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate buyTime);

    @GetMapping("recommend/stock/notice")
    @ApiOperation("A股情报-公告管理")
    PageResult<List<TdRecommendStockNotice>> getRecommendStockNoticeList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("old,new") String level,
            @RequestParam(required = false) @ApiParam("频道") Integer typeId,
            @RequestParam(required = false) @ApiParam("案例频道number") String channelNumber,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean isEnabled,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size);

    default PageResult<List<TdRecommendStockNotice>> getRecommendStockNoticeList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("频道") Integer typeId,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean isEnabled,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size
    ) {
        return this.getRecommendStockNoticeList(companyType, "old", typeId, null, isEnabled, current, size);
    }

    @GetMapping("recommend/stock/notice/case")
    @ApiOperation("案例公告管理")
    PageResult<List<TdRecommendStockNotice>> getCaseChannelNoticeList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("案例频道number") List<String> channelNumbers,
            @RequestParam(required = false) @ApiParam("是否启用") Boolean isEnabled,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size);

    @PostMapping("recommend/stock/notice/create")
    @ApiOperation("A股情报-公告管理-创建")
    BaseResult<String> createRecommendStockNotice(@Validated @RequestBody EditRecommendStockNoticeReq req);

    @PostMapping("recommend/stock/notice/update")
    @ApiOperation("A股情报-公告管理-编辑")
    BaseResult<String> updateRecommendStockNotice(@Validated @RequestBody EditRecommendStockNoticeReq req);

    @DeleteMapping("recommend/stock/notice/{noticeId}")
    @ApiOperation("A股情报-公告管理-删除")
    BaseResult<Boolean> deleteRecommendStockNotice(@PathVariable @ApiParam("公告Id") Integer noticeId);

    /**
     * 获取案例股票最新动态
     *
     * @param companyType 公司类型
     * @param startTime   查询开始时间
     * @param serviceTime 服务开通时间
     * @param seriesId    文章栏目id
     * @param current     页码
     * @param size        分页数量
     * @return 最新动态
     */
    @GetMapping("stock/case/news")
    @ApiOperation("案例最新动态")
    PageResult<List<StockCaseDealDto>> getStockCaseNewsList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("文章栏目id") Integer seriesId,
            @RequestParam(required = false) @ApiParam("查询的开始时间") Instant startTime,
            @RequestParam @ApiParam("用户服务开通时间") Instant serviceTime,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size);

    @GetMapping("stock/case/chat/list")
    @ApiOperation("聊天室案例列表")
    PageResult<List<StockCaseDealDto>> getStockCaseList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("案例频道ID") Long channelId,
            @RequestParam(required = false) @ApiParam("筛选开仓") Boolean filterOpen,
            @RequestParam(required = false) @ApiParam("筛选清仓") Boolean filterClose,
            @RequestParam(required = false) @ApiParam("筛选置顶") Boolean filterTop,
            @RequestParam(required = false) @ApiParam("筛选开通") Boolean checkOpen,
            @RequestParam(required = false) @ApiParam("开通类型, 1: 案例, 2: 操作") Integer checkOpenType,
            @RequestParam(required = false) @ApiParam("老师id") Integer analystId,
            @RequestParam(required = false) @ApiParam("案例开始时间") Instant caseStartTime,
            @RequestParam(required = false) @ApiParam("案例结束时间") Instant caseEndTime,
            @RequestParam(required = false) @ApiParam("案例操作开始时间") Instant dealStartTime,
            @RequestParam(required = false) @ApiParam("案例操作结束时间") Instant dealEndTime,
            @RequestParam(required = false) @ApiParam("服务开通时间") Instant openTime,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size);

    @GetMapping("channel/all")
    @ApiOperation("获取所有频道")
    BaseResult<List<TdPortfolioChannel>> getPortfolioChannels(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("频道类型") Integer channelType);

    @PostMapping("channel/update")
    @ApiOperation("更新频道")
    BaseResult<Void> updatePortfolioChannel(
            @RequestBody @Valid UpdatePortfolioChannelReq req);

    @GetMapping("stock/case/ids")
    @ApiOperation("根据ids查询案例信息")
    BaseResult<List<StockCaseInfoResp>> getAllByIds(
            @RequestParam @ApiParam("案例操作ids") List<Long> dealIds);

    @GetMapping("channel/info")
    @ApiOperation("获取指定频道信息")
    BaseResult<TdPortfolioChannel> getPortfolioChannelInfo(@RequestParam @ApiParam("频道id") Integer id);

    @GetMapping("portfolio/follow/{userId}")
    @ApiOperation("关注列表")
    PageResult<List<PortfolioFollowResp>> followList(
            @PathVariable @ApiParam("用户id") Integer userId,
            @RequestParam(required = false) @ApiParam("组合类型") Integer portfolioType,
            @RequestParam(required = false) @ApiParam("频道类型") Integer channelType,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("stock/case/editCaseTop")
    @ApiOperation("编辑案例是否置顶")
    BaseResult<Void> editCaseTop(
            @RequestParam @ApiParam("案例id") Long caseId,
            @RequestParam @ApiParam("是否置顶") Boolean isTop);

    @GetMapping("stock/case/editCaseSource")
    @ApiOperation("编辑案例来源")
    BaseResult<Void> editCaseSource(
            @RequestParam @ApiParam("案例id") Long caseId,
            @RequestParam @ApiParam("来源类型") Integer sourceType);

    @GetMapping("case_channel/chat/list")
    @ApiOperation("获取聊天室案例频道列表")
    PageResult<List<TdStockCaseChannel>> getChatStockCaseChannelList(
            @RequestParam(required = false) @ApiParam("频道Id列表") List<Long> ids,
            @RequestParam(required = false) @ApiParam("频道类型") Integer channelType,
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("页码") Integer current,
            @RequestParam @ApiParam("分页数量") Integer size);

    @GetMapping("my/stock/by/userId")
    @ApiOperation("获取用户自选股")
    BaseResult<TdMyStock> getStockByUserId(
            @RequestParam @ApiParam("用户ID") Integer userId);

    @PostMapping("my/stock/sort")
    @ApiOperation("用户编辑自选股")
    BaseResult<String> sortStock(
            @RequestBody StockSortReq req);

    @PostMapping("my/stock/add")
    @ApiOperation("用户添加自选股")
    BaseResult<TradeErrorCode> addStock(
            @RequestBody StockAddReq req);

    @PostMapping("my/stock/reduce")
    @ApiOperation("用户删除自选股")
    BaseResult<TradeErrorCode> reduceStock(
            @RequestBody StockReduceReq req);

    @GetMapping("stock/portfolio/detail")
    @ApiOperation("获取实战跟投组合")
    BaseResult<TdStockPortfolio> getStockPortfolio(@RequestParam Integer id);

    @GetMapping("stock/portfolio")
    @ApiOperation("获取实战跟投组合列表")
    PageResult<List<TdStockPortfolio>> getStockPortfolioList(
            @RequestParam Integer companyType,
            @RequestParam(required = false) @ApiParam("频道类型") Integer channelType,
            @RequestParam(required = false) @ApiParam("是否审核") Boolean audit,
            @RequestParam(required = false) @ApiParam("审核状态 StockPortfolioAuditStatusConstant") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("页码") Boolean enabled,
            @RequestParam(defaultValue = DEFAULT_CURRENT) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE) @ApiParam("分页数量") Integer size
    );

    @PostMapping("stock/portfolio/audit")
    @ApiOperation("变更实战跟投组合审核状态")
    BaseResult<Void> auditStockPortfolio(@RequestBody @Valid AuditStockPortfolioReq req);

    @PostMapping("stock/portfolio/channel/batch")
    @ApiOperation("批量获取投资组合频道列表")
    BaseResult<List<TdPortfolioChannel>> getPortfolioChannelList(@RequestBody @Valid BatchReq<Integer> req);

    @PostMapping("stock/portfolio/channel/batch/map")
    @ApiOperation("批量获取投资组合频道Map")
    BaseResult<Map<Integer, TdPortfolioChannel>> getPortfolioChannelMap(@RequestBody @Valid BatchReq<Integer> req);

    @GetMapping("stock/portfolio/analyst/relation")
    @ApiOperation("获取投资组合下的分析师列表")
    BaseResult<List<TdPortfolioAnalystRelation>> getPortfolioAnalystRelationList(@RequestParam Integer portfolioId);

    @PostMapping("stock/portfolio/analyst/relation/batch")
    @ApiOperation("批量获取投资组合下的分析师列表")
    BaseResult<List<TdPortfolioAnalystRelation>> getPortfolioAnalystRelationList(@RequestBody @Valid BatchReq<Integer> req);

    @GetMapping("stock/portfolio/position")
    @ApiOperation("获取投资组合下的仓位列表")
    BaseResult<List<TdPortfolioPosition>> getPortfolioPositionList(@RequestParam Integer portfolioId);

    @PostMapping("stock/portfolio/position/batch")
    @ApiOperation("批量获取投资组合下的仓位列表")
    BaseResult<List<TdPortfolioPosition>> getPortfolioPositionList(@RequestBody @Valid BatchReq<Integer> req);

    @GetMapping("stock/portfolio/adjust/detail")
    @ApiOperation("获取投资组合下的调仓详情列表")
    BaseResult<List<TdPortfolioAdjustDetail>> getPortfolioAdjustDetailList(@RequestParam Integer portfolioId);

    @GetMapping("stock/portfolio/adjust/record")
    @ApiOperation("获取投资组合下的调仓记录列表")
    BaseResult<List<TdPortfolioAdjustRecord>> getPortfolioAdjustRecordList(@RequestParam Integer portfolioId);

    @PostMapping("stock/portfolio/adjust/record/batch")
    @ApiOperation("批量获取调仓记录列表")
    BaseResult<List<TdPortfolioAdjustRecord>> getPortfolioAdjustRecordList(@RequestBody @Valid BatchReq<Integer> req);

    @PostMapping("stock/case/deal/audit")
    @ApiOperation("审核案例操作")
    BaseResult<Void> auditStockCaseDeal(
            @RequestParam @ApiParam("案例id") Long dealId,
            @RequestParam @ApiParam("审核人") Integer auditorId,
            @RequestParam @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("拒绝原因") String rejectReason);

    @PostMapping("stock/case/audit")
    @ApiOperation("审核案例")
    BaseResult<Void> auditStockCase(
            @RequestParam @ApiParam("案例id") Long caseId,
            @RequestParam @ApiParam("审核人") Integer auditorId,
            @RequestParam @ApiParam("审核状态") Integer auditStatus,
            @RequestParam(required = false) @ApiParam("拒绝原因") String rejectReason);

    @PostMapping({"case_channel/service/introduction/number"})
    @ApiOperation("获取股池服务简介列表")
    BaseResult<List<ServiceIntroduction>> getPoolCaseServiceIntroductionList(
            @RequestParam Integer companyType,
            @RequestBody @Valid BatchReq<String> req);

    @GetMapping("stock/case/lastCase")
    @ApiOperation("获取最后一条案例")
    BaseResult<TdStockCase> getLastStockCaseDetail(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam(required = false) @ApiParam("频道") Long channelId,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus);

    @GetMapping("stock/case/signal")
    @ApiOperation("获取案例股票信号列表")
    BaseResult<List<TdStockCase>> getStockCaseSignalList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("股票代码") String labelCode,
            @RequestParam(required = false) @ApiParam("频道") List<Long> channelIdList,
            @RequestParam(required = false) @ApiParam("审核状态") Integer auditStatus);

    @GetMapping("stock/case/lastCaseDeal")
    @ApiOperation("获取最后一条案例操作")
    BaseResult<TdStockCaseDeal> getLastStockCaseDeal(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("频道") Long channelId,
            @RequestParam(required = false) @ApiParam("开通时间") Instant openTime,
            @RequestParam(required = false) @ApiParam("截止时间") Instant deadline);

    @PostMapping("portfolio/filter/sub")
    @ApiOperation("过滤组合订阅用户")
    BaseResult<List<Integer>> getFilterSubUserList(@RequestBody @Valid FilterNoticeSubUsersReq req);

    @GetMapping("portfolio/sub/list")
    @ApiOperation("查询组合订阅用户")
    BaseResult<List<TdPortfolioCustomerRelation>> getPortfolioCustomerRelationList(
            @RequestParam @ApiParam("组合id") Integer portfolioId,
            @RequestParam @ApiParam("通知类型") Integer noticeType,
            @RequestParam @ApiParam("起点id") Integer startId,
            @RequestParam @ApiParam("size") Integer size);

    @GetMapping("notice/sub/list")
    @ApiOperation("获取用户订阅列表")
    BaseResult<List<TdNoticeSubInfo>> getNoticeSubList(
            @RequestParam(required = false) @ApiParam("通知类型") Integer noticeType,
            @RequestParam @ApiParam("起点id") Integer startId,
            @RequestParam @ApiParam("size") Integer size);

    @GetMapping("stock/case/lastCase/processDays")
    @ApiOperation("根据入池天数获取案例")
    BaseResult<List<TdStockCase>> findCaseByProcessDays(
            @RequestParam(required = false) @ApiParam("isClosed") Boolean isClosed,
            @RequestParam(required = false) @ApiParam("入池开始天数") Integer startProcessDays,
            @RequestParam @ApiParam("processDays") Integer processDays);

    @GetMapping("stock/case/last_days")
    @ApiOperation("获取最近几天案例")
    BaseResult<List<TdStockCase>> findCaseByCreateTime(
            @RequestParam @ApiParam("最近几天") Integer days);

    @GetMapping("stock/case/getCaseProfit/caseId")
    @ApiOperation("通过案例id获取案例收益")
    BaseResult<TdCaseProfit> findCaseProfitByCaseId(
            @RequestParam @ApiParam("案例id") Long caseId);

    @GetMapping("stock/case/info")
    @ApiOperation("案例股票池-案例详情")
    BaseResult<StockCaseDto> getStockCaseDetailInfo(@RequestParam @ApiParam("案例id") Long caseId);

    @GetMapping("stock/case/mySubList")
    @ApiOperation("案例股票池-我的关注")
    PageResult<List<StockCaseDealDto>> getMySubStockCaseList(@RequestParam @ApiParam("公司类型") Integer companyType,
                                                             @RequestParam @ApiParam("用户id") Integer userId,
                                                             @RequestParam @ApiParam("开通时间") Instant openTime,
                                                             @RequestParam(required = false) Integer current,
                                                             @RequestParam(required = false) Integer size);

    @GetMapping("stock/case/customerSubList")
    @ApiOperation("企微后台-客户关注案例")
    PageResult<List<CustomerStockCaseDto>> getCustomerSubStockCaseList(@RequestParam @ApiParam("公司类型") Integer companyType,
                                                                       @RequestParam @ApiParam("用户id") Integer userId,
                                                                       @RequestParam(required = false) Integer current,
                                                                       @RequestParam(required = false) Integer size);

    @GetMapping("stock/case/sub/filterSubByUserId")
    @ApiOperation("案例股票池-获取用户已关注列表")
    BaseResult<List<TdCaseSubInfo>> filterSubByUserId(@RequestParam @ApiParam("用户id") Integer userId);

    @GetMapping("stock/case/sub/count")
    @ApiOperation("案例股票池-关注案例个数")
    BaseResult<SubStockCaseCountResp> getMySubStockCaseCount(@RequestParam @ApiParam("用户id") Integer userId,
                                                             @RequestParam(required = false) @ApiParam("是否本人关注") Boolean isSelf);

    @GetMapping("stock/case/sub/list")
    @ApiOperation("案例股票池-订阅人数")
    PageResult<List<TdCaseSubInfo>> getStockCaseSubInfoList(@RequestParam @ApiParam("案例id") Long caseId,
                                                            @RequestParam(required = false) @ApiParam("搜索内容") String content,
                                                            @RequestParam(required = false) Integer current,
                                                            @RequestParam(required = false) Integer size);

    @GetMapping("stock/case/sub/recordList")
    @ApiOperation("案例股票池-订阅记录")
    PageResult<List<TdCaseSubRecord>> getStockCaseSubRecordList(@RequestParam @ApiParam("案例id") Long caseId,
                                                                @RequestParam(required = false) @ApiParam("搜索内容") String content,
                                                                @RequestParam(required = false) Integer current,
                                                                @RequestParam(required = false) Integer size);

    @PostMapping("stock/case/sub/editSubInfo")
    @ApiOperation("案例股票池-关注/取关")
    BaseResult<String> editStockCaseSubInfo(@Validated @RequestBody EditStockCaseSubInfoReq req);

    @PostMapping("stock/case/sub/batchEdit")
    @ApiOperation("案例股票池-批量关注")
    BaseResult<String> batchEditCaseSub(@Validated @RequestBody BatchEditStockCaseSubInfoReq req);

    @PostMapping("stock/case/sub/filterUser")
    @ApiOperation("案例股票池-获取订阅用户")
    BaseResult<List<Integer>> filterStockCaseSubInfoUser(@RequestParam @ApiParam("案例id") Long caseId,
                                                         @RequestBody @Valid @ApiParam("用户id") BatchReq<Integer> req);

    @PostMapping("stock/case/sub/cancelUser")
    @ApiOperation("案例股票池-注销用户取关")
    BaseResult<String> cancelUserStockCaseSub(@RequestParam @ApiParam("用户id") Integer userId);

    @GetMapping("stock/case/filterStockCaseByChannel")
    @ApiOperation("根据频道分页获取案例")
    PageResult<List<TdStockCase>> filterStockCaseByChannel(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("频道") Long channelId,
            @RequestParam(required = false) @ApiParam("搜索内容") String content,
            @RequestParam(required = false) @ApiParam("案例开始时间") Long caseStartTime,
            @RequestParam(required = false) @ApiParam("案例结束时间") Long caseEndTime,
            @RequestParam(required = false) Integer current,
            @RequestParam(required = false) Integer size);

    @PostMapping("portfolio/processDay")
    @ApiOperation("计算日K行情")
    BaseResult<Void> processDay(
            @RequestBody ProcessDayReq req);

    @PostMapping("portfolio/updateProfit")
    @ApiOperation("更新入池案例行情处理结果")
    BaseResult<Void> updateProfit(
            @RequestBody @Valid UpdateProfitReq req);

    @GetMapping("case_channel/strategyList")
    @ApiOperation("策略组列表")
    PageResult<List<StrategyChannelResp>> getStrategyChannelList(
            @RequestParam Integer companyType,
            @RequestParam Integer userId,
            @RequestParam(required = false) @ApiParam("搜索内容") String searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("case_channel/strategy/info")
    @ApiOperation("策略组详情")
    BaseResult<StrategyChannelResp> getStrategyInfo(@RequestParam @ApiParam("策略组ID") Long id);

    @GetMapping("case_channel/strategy/channelId")
    @ApiOperation("根据频道id获取策略组详情")
    BaseResult<StrategyChannelResp> getStrategyByChannelId(@RequestParam @ApiParam("策略组频道ID") Long channelId);

    @PostMapping("case_channel/strategy/update")
    @ApiOperation("策略组信息编辑")
    BaseResult<Void> updateStrategyInfo(@Validated @RequestBody UpdateStrategyInfoReq req);

    @GetMapping("case_channel/strategy/userList")
    @ApiOperation("策略组用户列表")
    PageResult<List<StrategyUserInfoResp>> getStrategyUserInfoList(
            @RequestParam @ApiParam("策略组ID") Long id,
            @RequestParam(required = false) @ApiParam("搜索内容") Integer searchContent,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("stock/case/strategy/detail")
    @ApiOperation("策略组概览明细")
    BaseResult<List<StrategyStockCaseResp>> getStrategyStockCaseRespList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("策略组频道ID") Long channelId);

    @GetMapping("stock/case/strategy/detailWithoutQuote")
    @ApiOperation("策略组概览明细(去掉行情调用)")
    public BaseResult<List<StrategyStockCaseResp>> getStrategyStockCaseWithoutQuoteRespList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("策略组频道ID") Long channelId,
            @RequestParam @ApiParam("开通时间") Instant openTime
    );

    @GetMapping("stock/case/strategy/profit/detail")
    @ApiOperation("策略组概览胜率详情")
    PageResult<List<StrategyStockCaseResp>> getStrategyProfitDetailList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("策略组频道ID") Long channelId,
            @RequestParam(defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = "10") @ApiParam("分页数量") Integer size);

    @GetMapping("case_channel/strategyList/by/enabled")
    @ApiOperation("根据状态获取策略组列表")
    List<TdStrategyChannelExt> getStrategyChannelListByEnabled(
            @RequestParam Integer companyType,
            @RequestParam @ApiParam("状态") Boolean enabled);

    @GetMapping("stock/case/strategy/profit")
    @ApiOperation("根据频道获取收益信息")
    List<StrategyStockCaseResp> getStrategyStockCaseRespList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("策略组频道ID") Collection<Long> channelIds);

    @PostMapping("stock/case/strategy/profit")
    @ApiOperation("批量更新收益胜率")
    BaseResult<String> batchUpdateProfitRatio(@RequestBody @Valid BatchReq<TdStrategyChannelExt> req);

    @GetMapping("stock/case/strategy/stockList")
    @ApiOperation("策略组持仓列表")
    BaseResult<List<StrategyStockCaseResp>> getStrategyStock(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("策略组频道ID") Long channelId,
            @RequestParam @ApiParam("开通时间") Instant openTime);

    @GetMapping("stock/case/strategy/stockDealList")
    @ApiOperation("策略组持仓操作列表")
    BaseResult<List<TdStockCaseDeal>> getStrategyStockDealList(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("策略组频道ID") Long channelId,
            @RequestParam @ApiParam("开通时间") Instant openTime,
            @RequestParam(required = false) @ApiParam("结束时间") Instant endTime,
            @RequestParam(required = false, defaultValue = "5") @ApiParam("获取天数") Integer day);

    @GetMapping("stock/case/batch/stockCase")
    @ApiOperation("批量获取案例股票")
    BaseResult<List<TdStockCase>> batchStockCase(@RequestParam List<Long> caseIdList);

    @GetMapping("stock/case/user/case_list")
    @ApiOperation("获取用户订阅案例")
    BaseResult<List<UserStockCaseSubInfoResp>> getUserSubInfoList(
            @RequestParam @ApiParam("用户列表") List<Integer> userIdList);

    @PostMapping("stock/case/user/batchSub")
    @ApiOperation("订阅案例-用户批量关注")
    BaseResult<String> userBatchCaseSub(@RequestBody @Validated UserBatchStockCaseSubReq req);

    @GetMapping("stock/case/case_list/channel_type")
    @ApiOperation("根据频道类型获取案例列表")
    PageResult<List<CaseChannelResp>> findStockCaseByChannelType(
            @RequestParam @ApiParam("公司类型") Integer companyType,
            @RequestParam @ApiParam("频道类型") Integer channelType,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("页码") Integer current,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("数量") Integer size);

    @PostMapping("stock/case/dzStockCaseCreate")
    @ApiOperation("点证入选股票池")
    BaseResult<Void> dzStockCaseCreate(@Validated @RequestBody DzStockPoolReq req);

    @PostMapping("case_channel/getProcessableCaseChannelIds")
    @ApiOperation("获取用户可处理的案例频道id")
    BaseResult<Set<Long>> getProcessableCaseChannelIds(@RequestParam @ApiParam("用户id") Integer userId);

    @PostMapping({"case_channel/stock-channel-list"})
    @ApiOperation("批量获取股池频道列表")
    BaseResult<List<TdStockCaseChannel>> getStrategyListByType(
            @RequestParam Integer companyType,
            @RequestParam Integer channelType,
            @RequestBody @Valid BatchReq<String> req);

    @GetMapping("stock-case/get-notice-sub")
    @ApiOperation("获取用户订阅推送关系")
    BaseResult<StockChannelNoticeSubResp> getStockNoticeSub(
            @ApiIgnore @RequestHeader(USER_ID) Integer userId,
            @RequestParam @ApiParam("频道类型（50-量化频道）") Integer channelType,
            @RequestParam @ApiParam("设备类型（20-APP）") Integer deviceType);

    @PostMapping("stock-case/sub-notice")
    @ApiOperation("订阅/取消案例推送")
    BaseResult<String> createSubPush(
            @ApiIgnore @RequestHeader(USER_ID) Integer userId,
            @RequestParam @ApiParam("频道类型（50-量化频道）") Integer channelType,
            @RequestParam @ApiParam("设备类型（20-APP）") Integer deviceType,
            @RequestParam @ApiParam("是否订阅") Boolean isSub);

    @PostMapping("stock-case/is-sub-list")
    @ApiOperation("获取已订阅的list")
    BaseResult<List<Integer>> findIsSubUserList(
            @RequestParam @ApiParam("频道类型（50-量化频道）") Integer channelType,
            @RequestParam @ApiParam("设备类型（20-APP）") Integer deviceType,
            @RequestBody BatchReq<Integer> req);

    @PostMapping("stock-case/cancel-user-sub")
    @ApiOperation("取消用户订阅通知")
    BaseResult<String> cancelUserSub(
            @RequestParam @ApiParam("用户id") Integer userId,
            @RequestParam @ApiParam("设备类型（20-APP）") Integer deviceType);

    @GetMapping("case_channel/record/list")
    @ApiOperation("获取案例频道操作记录列表")
    PageResult<List<TdStockCaseChannelRecord>> getStockCaseChannelRecordList(
            @RequestParam @ApiParam("频道id") Long channelId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "10") @ApiParam("每页条数") Integer size);

    @GetMapping("stock/case/record/list")
    @ApiOperation("获取案例操作记录列表")
    PageResult<List<TdStockCaseRecord>> getStockCaseRecordList(
            @RequestParam @ApiParam("案例id") Long caseId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "10") @ApiParam("每页条数") Integer size);

    @GetMapping("recommend/stock-type/record/list")
    @ApiOperation("A股情报-获取股票池频道操作记录表")
    PageResult<List<TdRecommendStockTypeRecord>> getRecommendStockTypeRecordList(
            @RequestParam @ApiParam("频道id") Integer typeId,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "10") @ApiParam("每页条数") Integer size);

    @GetMapping("promotion-stock/list")
    @ApiOperation("尾盘选股记录")
    PageResult<List<TdPromotionStock>> getPromotionStockList(
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATETIME) LocalDateTime endTime,
            @RequestParam(required = false) @ApiParam("指标类型(10-尾盘选股 20-六维妖股)") Integer channel,
            @RequestParam(required = false, defaultValue = "1") @ApiParam("当前页") Integer current,
            @RequestParam(required = false, defaultValue = "20") @ApiParam("每页条数") Integer size);

    @GetMapping("promotion-stock/list/by/trade-date")
    @ApiOperation("前端尾盘选股记录")
    BaseResult<List<TdPromotionStock>> getPromotionStockListByTradeDate(
            @RequestParam(required = false) @ApiParam("选股日期") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate tradeDate,
            @RequestParam(required = false) @ApiParam("指标类型(10-尾盘选股 20-六维妖股)") Integer channel);

    @GetMapping("promotion-stock/record/by/trade-date")
    @ApiOperation("前端尾盘选股记录结果")
    BaseResult<TdPromotionStockRecord> getPromotionStockRecordByTradeDate(
            @RequestParam(required = false) @ApiParam("选股日期") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate tradeDate,
            @RequestParam(required = false) @ApiParam("指标类型(10-尾盘选股 20-六维妖股)") Integer channel);

    @GetMapping("promotion-stock/record/last-day")
    @ApiOperation("最后一天尾盘选股记录结果")
    BaseResult<List<TdPromotionStock>> getPromotionStockLastDayRecord(@RequestParam @ApiParam("指标类型(10-尾盘选股 20-六维妖股)") Integer channel);

    @PostMapping("stock/diagnose/create")
    @ApiOperation("创建诊股")
    BaseResult<Void> createStockDiagnose(@Validated @RequestBody StockDiagnoseCreateReq req);

    @GetMapping("stock/diagnose/list")
    @ApiOperation("诊股列表")
    PageResult<List<TdStockDiagnoseInfo>> listStockDiagnose(
            @RequestParam(required = false) @ApiParam("开始时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate startTime,
            @RequestParam(required = false) @ApiParam("结束时间") @DateTimeFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE) LocalDate endTime,
            @RequestParam(required = false) @ApiParam("searchContent") String searchContent,
            @RequestParam(required = false) @ApiParam("用户id") Integer userId,
            @RequestParam(defaultValue = DEFAULT_CURRENT, required = false) @ApiParam("页码") Integer current,
            @RequestParam(defaultValue = DEFAULT_SIZE, required = false) @ApiParam("每页条数") Integer size
    );

    @GetMapping("stock/diagnose/info")
    @ApiOperation("获取诊股信息")
    BaseResult<TdStockDiagnoseInfo> getStockDiagnoseInfo(
            @RequestParam @ApiParam("诊股id") Long id);

    @PostMapping("stock/case/refresh")
    @ApiOperation("后台手动筛选case收益")
    BaseResult<Void> refreshCaseProfit(
            @RequestParam Long caseId
    );

    @GetMapping("stock/case/profit-refresh-list")
    BaseResult<List<TdStockCase>> getProfitRefreshCaseList();

    @GetMapping("my/stock/list")
    @ApiOperation("自选股列表")
    PageResult<List<TdMyStockDetail>> getMyStockList(
            @RequestParam(required = false) List<Integer> userIds,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @GetMapping("my/stock/record/list")
    @ApiOperation("我的自选股操作记录列表")
    PageResult<List<TdMyStockRecord>> getMyStockRecordList(
            @RequestParam(required = false) List<Integer> userIds,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size
    );

    @PostMapping("stock-score/stock-list")
    @ApiOperation("竞价算力股票列表")
    PageResult<List<TdStockScoreInfo>> stockScoreList(@RequestBody @Valid StockScoreReq stockScoreReq);

    @GetMapping("stock-score/factor/list")
    @ApiOperation("获取因子列表")
    PageResult<List<StockFactorResp>> factorList(
            @RequestParam(required = false) @ApiParam("启用/禁用") Boolean enabled,
            @RequestParam(required = false, defaultValue = DEFAULT_CURRENT) Integer current,
            @RequestParam(required = false, defaultValue = DEFAULT_SIZE) Integer size);

    @PostMapping("stock-score/factor/add")
    @ApiOperation("创建因子")
    BaseResult<Void> addFactor(@RequestBody @Valid StockFactorReq stockFactorReq);

    @PostMapping("stock-score/factor/enable")
    @ApiOperation("更新启用状态")
    BaseResult<Void> factorEnabled(@RequestParam Long id, @RequestParam @ApiParam("启用/禁用") Boolean enabled);

    @PostMapping("stock-score/factor-map")
    @ApiOperation("获取因子信息map")
    BaseResult<Map<String, List<TdStockFactorRelation>>> getStockFactorMapByFactorIds(@RequestBody StockFactorMapReq req);

    @PostMapping("my/stock/add-list")
    @ApiOperation("用户添加自选股列表")
    BaseResult<TradeErrorCode> addStockList(@RequestBody @Valid StockAddListReq req);

    @PostMapping("case_channel/set-follow-hide")
    @ApiOperation("设置股票池频道解盘股票跟踪前端是否隐藏")
    BaseResult<Void> setFollowHide(@Validated @RequestBody UpdateCaseChannelHideReq req);
}

