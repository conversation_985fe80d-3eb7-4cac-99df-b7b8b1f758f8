package cn.shrise.radium.tradeservice.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel
public class CaseChannelManagerReq {

    @ApiModelProperty(value = "频道ID", required = true)
    private Long channelId;

    @ApiModelProperty(value = "处理人ID列表", required = true)
    private List<Integer> managerIds;

    @ApiModelProperty(value = "部门ID列表", required = true)
    private List<Integer> departmentIds;

    @ApiModelProperty(value = "操作人ID", hidden = true)
    private Integer operatorId;
}
