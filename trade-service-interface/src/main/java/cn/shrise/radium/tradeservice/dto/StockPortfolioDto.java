package cn.shrise.radium.tradeservice.dto;

import cn.shrise.radium.contentservice.entity.SsAnalystInfo;
import cn.shrise.radium.tradeservice.entity.TdPortfolioChannel;
import cn.shrise.radium.tradeservice.entity.TdStockPortfolio;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * desc:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StockPortfolioDto {
    private TdStockPortfolio portfolioInfo;
    private TdPortfolioChannel channelInfo;
    private SsAnalystInfo analystInfo;
}
