package cn.shrise.radium.tradeservice.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.persistence.*;
import java.time.Instant;

@Table(name = "td_stock_case_deal", indexes = {
        @Index(name = "operator_ibfk2_idx", columnList = "operator_id"),
        @Index(name = "case_ibfk1_idx", columnList = "case_id")
})
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdStockCaseDeal {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "case_id", nullable = false)
    private Long caseId;

    @Column(name = "company_type")
    private Integer companyType;

    @Column(name = "is_enabled")
    private Boolean isEnabled;

    @Column(name = "deal_type", nullable = false)
    private Integer dealType;

    @Column(name = "price")
    private Double price;

    @Column(name = "price_up")
    private Double priceUp;

    @Column(name = "price_down")
    private Double priceDown;

    @Column(name = "take_profit_up")
    private Double takeProfitUp;

    @Column(name = "take_profit_down")
    private Double takeProfitDown;

    @Column(name = "stop_loss_up")
    private Double stopLossUp;

    @Column(name = "stop_loss_down")
    private Double stopLossDown;

    @Column(name = "ex_price")
    private Double exPrice;

    @Column(name = "count")
    private Integer count;

    @Column(name = "end_count")
    private Integer endCount;

    @Lob
    @Column(name = "reason")
    private String reason;

    @Lob
    @Column(name = "risk")
    private String risk;

    @Lob
    @Column(name = "tips")
    private String tips;

    @Column(name = "operator_id")
    private Integer operatorId;

    @Column(name = "gmt_create", insertable = false, updatable = false)
    private Instant gmtCreate;

    @Column(name = "gmt_modified", insertable = false, updatable = false)
    private Instant gmtModified;

    @Column(name = "auditor_id")
    private Integer auditorId;

    @Column(name = "audit_time")
    private Instant auditTime;

    @Column(name = "audit_status")
    private Integer auditStatus;

    @Column(name = "app_push", nullable = false)
    private Boolean appPush;

    @Column(name = "reject_reason")
    private String rejectReason;

    @Column(name = "push_status")
    private Integer pushStatus;

    @Column(name = "loss_price")
    private Double lossPrice;

    @Column(name = "target_price")
    private Double targetPrice;

    @Column(name = "is_hide")
    private Boolean isHide;
}