package cn.shrise.radium.tradeservice.entity;

import lombok.*;

import javax.persistence.*;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Table(name = "td_notice_sub_info", indexes = {
        @Index(name = "uni_number_company", columnList = "CustomerID, NoticeType", unique = true),
        @Index(name = "CustomerID", columnList = "CustomerID")
})
@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TdNoticeSubInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PkID", nullable = false)
    private Integer id;

    @Column(name = "CustomerID")
    private Integer customerId;

    @Column(name = "NoticeType")
    private Integer noticeType;

    @Column(name = "CompanyType", nullable = false)
    private Integer companyType;

    @Column(name = "Enabled")
    private Boolean enabled;

    @Column(name = "CreateTime", insertable = false, updatable = false)
    private Instant createTime;

    @Column(name = "UpdateTime", insertable = false, updatable = false)
    private Instant updateTime;
}