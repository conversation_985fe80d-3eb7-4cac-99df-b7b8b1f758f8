package cn.shrise.radium.tradeservice.req;

import cn.shrise.radium.common.util.DateUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * @Author: tangjiajun
 * @Date: 2025/4/9 9:43
 * @Desc:
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockScoreReq {

    @NotNull(message = "参数不能为空")
    @JsonFormat(pattern = DateUtils.DEFAULT_PATTERN_DATE)
    @ApiModelProperty(value = "日期", required = true)
    private LocalDate date;

    @ApiModelProperty("排序字段: 10-股票代码，20-竞价得分，30-低吸得分")
    private Integer orderType;

    @ApiModelProperty(value = "因子id列表")
    private List<Long> factorList;

    @ApiModelProperty("是否升序")
    private Boolean isAsc;

    @NotNull(message = "参数不能为空")
    @ApiModelProperty(value = "是否过滤St股", required = true)
    private Boolean isFilterSt;

    @NotNull(message = "参数不能为空")
    @ApiModelProperty(value = "是否过滤一字板", required = true)
    private Boolean isFilterLimitUp;

    @ApiModelProperty("页码")
    private Integer current;

    @ApiModelProperty("分页数量")
    private Integer size;
}
